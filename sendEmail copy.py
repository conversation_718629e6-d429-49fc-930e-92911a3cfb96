import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# SMTP Server Configuration (replace with your company's SMTP settings)
SMTP_SERVER = "mail.comorin.co"  # Replace with your company's SMTP server
SMTP_PORT = 465  # Use 465 for SSL, 587 for TLS
EMAIL_ADDRESS = "<EMAIL>"  # Your company email address
EMAIL_PASSWORD = ""  # Password for the email account

def send_email(to_email, subject, body):
    try:
        # Set up the MIME email
        msg = MIMEMultipart()
        msg['From'] = EMAIL_ADDRESS
        msg['To'] = to_email
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'plain'))

        # Connect to SMTP server (SSL)
        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT, timeout=30) as server:
            server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            server.sendmail(EMAIL_ADDRESS, to_email, msg.as_string())
        
        print(f"Email sent successfully to {to_email}")
    except Exception as e:
        print(f"Failed to send email to {to_email}. Error: {e}")

# Example Usage
send_email("<EMAIL>", "Test Email", "This is a test email body.")
