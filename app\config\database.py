from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
import os
from typing import Generator
from fastapi import Request
from .db_pool import get_main_db, main_client

load_dotenv()

MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
DB_NAME = os.getenv("DB_NAME", "email_responder")

# Get the client and database from the connection pool
client = main_client
database = get_main_db()

async def get_db():
    return database
