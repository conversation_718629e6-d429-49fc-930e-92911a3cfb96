from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.routes import (
    user_routes, config_routes, email_routes, auth_routes, 
    workflow_routes, category_template_routes, draft_routes, 
    sample_routes, action_routes, websocket_routes, email_address_routes,
    attachment_routes
)
from fastapi.openapi.utils import get_openapi
from app.config.database import get_db, client
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.schemas.user_schema import UserC<PERSON>, User
from app.controllers.user_controller import UserController

import os

# Set PORT environment variable if not already set


# Create the FastAPI app
app = FastAPI(
    title="FastAPI User Management API",
    description="API for user management with authentication and authorization",
    version="1.0.0"
)


# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(user_routes.router)
app.include_router(config_routes.router)
app.include_router(email_routes.router)
app.include_router(auth_routes.router)
app.include_router(workflow_routes.router)
app.include_router(category_template_routes.router)
app.include_router(draft_routes.router)
app.include_router(sample_routes.router)
app.include_router(action_routes.router)
# Include other routers
app.include_router(websocket_routes.router)
app.include_router(email_address_routes.router)
app.include_router(attachment_routes.router)

# Mount Socket.IO at the end
from app.websocket.connection_manager import socket_app, sio
app.mount('/', socket_app)


@app.get("/", tags=["Root"])
async def root():
    """
    Root endpoint returning welcome message
    """
    return {"message": "Welcome to FastAPI User Management API"}

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="FastAPI User Management API",
        version="1.0.0",
        description="API for user management with authentication and authorization",
        routes=app.routes,
    )
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

@app.on_event("startup")
async def startup_event():
    pass
    
@app.on_event("shutdown")
async def shutdown_event():
    # Close database connection
    if client:
        client.close()
