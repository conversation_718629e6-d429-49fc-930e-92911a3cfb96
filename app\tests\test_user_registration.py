import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.config.database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
import os

client = TestClient(app)

@pytest.fixture
def test_db():
    MONGODB_TEST_URL = "mongodb://localhost:27017"
    DB_TEST_NAME = "fastapi_test_db"
    test_client = AsyncIOMotorClient(MONGODB_TEST_URL)
    test_database = test_client[DB_TEST_NAME]
    
    # Override the dependency
    async def override_get_db():
        try:
            yield test_database
        finally:
            test_client.close()
    
    app.dependency_overrides[get_db] = override_get_db
    return test_database

def test_valid_user_registration(test_db):
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "Test@123456",
        "email_config": {
            "email": "<EMAIL>",
            "password": "emailpass",
            "host": "smtp.example.com",
            "port": 587
        }
    }
    response = client.post("/users/register", json=user_data)
    assert response.status_code == 201
    assert response.json()["email"] == user_data["email"]
    assert response.json()["username"] == user_data["username"]
    assert "password" not in response.json()

def test_duplicate_email_registration(test_db):
    user_data = {
        "email": "<EMAIL>",
        "username": "uniqueuser",
        "full_name": "Test User",
        "password": "Test@123456",
        "email_config": {
            "email": "<EMAIL>",
            "password": "emailpass",
            "host": "smtp.example.com",
            "port": 587
        }
    }
    # First registration
    response = client.post("/users/register", json=user_data)
    assert response.status_code == 201
    
    # Duplicate registration
    response = client.post("/users/register", json=user_data)
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]

def test_weak_password_registration(test_db):
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "weak",
        "email_config": {
            "email": "<EMAIL>",
            "password": "emailpass",
            "host": "smtp.example.com",
            "port": 587
        }
    }
    response = client.post("/users/register", json=user_data)
    assert response.status_code == 400
    assert "Password must be at least 8 characters long" in response.json()["detail"]