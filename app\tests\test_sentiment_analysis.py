import pytest
from fastapi.testclient import Test<PERSON>lient
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.main import app
from app.services.claude_service import ClaudeSer<PERSON>
from unittest.mock import Mock, patch
from datetime import datetime

client = TestClient(app)

@pytest.fixture
def mock_db():
    return Mock()

@pytest.fixture
def claude_service(mock_db):
    return ClaudeService(mock_db)

@pytest.mark.asyncio
async def test_positive_sentiment_analysis(claude_service, mock_db):
    # Setup
    text = "I'm really happy with the excellent service!"
    user_id = "test_user"
    mock_db.configurations.find_one.return_value = {
        "email_responder": {
            "prompts": {
                "sentiment": "Test prompt"
            }
        }
    }
    
    # Execute
    result = await claude_service.analyze_sentiment(text, user_id)
    
    # Assert
    assert isinstance(result, dict)
    assert "label" in result
    assert "score" in result
    assert "confidence" in result
    assert result["label"] == "positive"
    assert result["score"] > 0.2
    assert 0 <= result["confidence"] <= 1

@pytest.mark.asyncio
async def test_negative_sentiment_analysis(claude_service, mock_db):
    # Setup
    text = "This is terrible service, I'm very disappointed!"
    user_id = "test_user"
    
    # Execute
    result = await claude_service.analyze_sentiment(text, user_id)
    
    # Assert
    assert result["label"] == "negative"
    assert result["score"] < -0.2
    assert 0 <= result["confidence"] <= 1

@pytest.mark.asyncio
async def test_neutral_sentiment_analysis(claude_service, mock_db):
    # Setup
    text = "The meeting is scheduled for tomorrow at 2 PM."
    user_id = "test_user"
    
    # Execute
    result = await claude_service.analyze_sentiment(text, user_id)
    
    # Assert
    assert result["label"] == "neutral"
    assert -0.2 <= result["score"] <= 0.2
    assert 0 <= result["confidence"] <= 1

@pytest.mark.asyncio
async def test_error_handling(claude_service, mock_db):
    # Setup
    text = None
    user_id = "test_user"
    
    # Execute & Assert
    with pytest.raises(HTTPException) as exc_info:
        await claude_service.analyze_sentiment(text, user_id)
    assert exc_info.value.status_code == 500

@pytest.mark.asyncio
async def test_sentiment_thresholds(claude_service, mock_db):
    # Test various sentiment scores
    test_cases = [
        ("Amazing service!", "positive", 0.8),
        ("OK service", "neutral", 0.1),
        ("Terrible experience", "negative", -0.7)
    ]
    
    for text, expected_label, expected_score in test_cases:
        result = await claude_service.analyze_sentiment(text, "test_user")
        assert result["label"] == expected_label
        assert abs(result["score"] - expected_score) < 0.5  # Allow some variance
