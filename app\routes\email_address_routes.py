from fastapi import API<PERSON>out<PERSON>, Depends, HTTP<PERSON><PERSON><PERSON>, <PERSON><PERSON>
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.schemas.email_schema import EmailAddressCreate, EmailAddressUpdate, EmailAddressResponse
from app.utils.security import decode_jwt_token
from datetime import datetime

router = APIRouter(
    prefix="/email-addresses",
    tags=["Email Addresses"]
)

@router.post("/exclusions", response_model=EmailAddressResponse)
async def create_email_addresses(
    email_data: EmailAddressCreate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Store list of email addresses for a user"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    try:
        # Store email addresses
        result = await db.user_email_addresses.update_one(
            {"user_id": user_id},
            {
                "$set": {
                    "emails": email_data.emails,
                    "updated_at": datetime.utcnow()
                }
            },
            upsert=True
        )
        
        return {
            "status_code": 200,
            "status": "success",
            "message": "Email addresses stored successfully",
            "data": email_data.emails
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/exclusions", response_model=EmailAddressResponse)
async def get_email_addresses(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Get list of email addresses for a user"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    try:
        # Get email addresses
        result = await db.user_email_addresses.find_one({"user_id": user_id})
        if not result:
            return {
                "status_code": 200,
                "status": "success",
                "message": "No email addresses found",
                "data": []
            }
        
        return {
            "status_code": 200,
            "status": "success",
            "message": "Email addresses retrieved successfully",
            "data": result.get("emails", [])
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/exclusions", response_model=EmailAddressResponse)
async def update_email_addresses(
    email_data: EmailAddressUpdate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Update list of email addresses for a user"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    try:
        # Update email addresses with upsert=True to create if not exists
        result = await db.user_email_addresses.update_one(
            {"user_id": user_id},
            {
                "$set": {
                    "emails": email_data.emails,
                    "updated_at": datetime.utcnow()
                }
            },
            upsert=True  # This will create a new document if none exists
        )
        
        action = "inserted" if result.upserted_id else "updated"
        
        return {
            "status_code": 200,
            "status": "success",
            "message": f"Email addresses {action} successfully",
            "data": email_data.emails
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/exclusions", response_model=EmailAddressResponse)
async def delete_email_addresses(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Delete all email addresses for a user"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    try:
        # Delete email addresses
        result = await db.user_email_addresses.delete_one({"user_id": user_id})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="No email addresses found to delete")
        
        return {
            "status_code": 200,
            "status": "success",
            "message": "Email addresses deleted successfully",
            "data": []
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
