from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List, Dict

class AttachmentResponse(BaseModel):
    s3_key: str
    filename: str
    content_type: str
    size: Optional[int] = None
    last_modified: Optional[datetime] = None
    url: Optional[str] = None

class AttachmentListResponse(BaseModel):
    files: List[AttachmentResponse]
    continuation_token: Optional[str] = None

class MultipleAttachmentResponse(BaseModel):
    files: List[AttachmentResponse]
    failed_uploads: List[Dict[str, str]] = []