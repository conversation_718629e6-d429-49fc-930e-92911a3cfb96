import logging
from app.utils.action_functions import create_quote_response, fetch_quote_docs_response

logger = logging.getLogger(__name__)

# Predefined actions mapping
PREDEFINED_ACTIONS = {
    "create_quote": {
        "name": "Create Insurance Quote",
        "description": "Create a new insurance quote based on customer request",
        "keywords": ["quote", "insurance quote", "new quote", "price", "cost"],
        "required_fields": ["customer_name", "insurance_type", "coverage_amount"],
        "response_function": create_quote_response
    },
    "fetch_quote_docs": {
        "name": "Fetch Quote Documents",
        "description": "Retrieve insurance quote documentation",
        "keywords": ["documents", "quote documents", "paperwork", "policy documents"],
        "required_fields": ["quote_id", "customer_name"],
        "response_function": fetch_quote_docs_response
    }
}

# Predefined actions for email processing
async def get_user_actions(db, user_id):
    """Fetch user's custom actions from database"""
    try:
        cursor = db.actions.find({"user_id": user_id})
        print("user_id========================",user_id)
        actions = await cursor.to_list(length=None)
        return {str(action["_id"]): action for action in actions}
    except Exception as e:
        logger.error(f"Error fetching user actions: {str(e)}")
        return {}

DEFAULT_EMAIL_RESPONDER = {
    "prompts": {
    "action_detection": {
        "system": '''You are an expert at analyzing emails to detect specific actions needed.
        Review the email content and determine if it matches any of the predefined actions.
        Consider keywords, context, and intent carefully.
        
        Return your response in JSON format:
        {
            "detected_action": "action_name or null",
            "confidence": 0.0 to 1.0,

        }''',
        "user": "Analyze this email to detect if it matches any predefined actions."
    },
    "classification": {
        "system": '''You are an expert email classifier.
        Analyze the email content and match it to the most appropriate category from the provided list.
        Consider the category descriptions carefully.
        Return only the category name that best matches, or 'unknown'- guess the best category name that is suitable for the email for eg(unkown - your suggestion), if no category is suitable.
        Keep your response concise and only return the category name.''',
        "user": "What is the classification of this text?"
    },
    "sentiment": {
        "system": ''' You are an expert in sentiment analysis.
        Analyze the emotional tone and sentiment of the text.
        Consider:
        - Positive: score > 0.2 (enthusiasm, gratitude, satisfaction)
        - Neutral: -0.2 <= score <= 0.2 (factual, balanced, objective)
        - Negative: score < -0.2 (frustration, complaints, anger)
        
        IMPORTANT: You must ONLY return your analysis in the exact format: label|score|confidence
        For example: positive|0.8|0.95
        
        DO NOT include any explanations or additional text.
        ONLY return the three values separated by pipes.
        The score must be a number between -1.0 and 1.0.
        The confidence must be a number between 0.0 and 1.0.
        ''',
        "user": "What is the sentiment of this text?"
    },
    "response_generation": {
        "system": '''You are an expert email response generator.
            Create a professional, context-aware response that follows the provided instructions.
            
            Format the response as a complete, ready-to-send email.
            End the email with a signature using this format:Best regards, jebin j''',
        "user": """Generate a concise professional email response following these instructions:

Instructions:
1. Use the category template as a guide if available
2. Match the tone to the detected sentiment
3. Maintain professional language
4. Be concise but thorough
5. Address all key points from the original email
6. Incorporate relevant information from the retrieved context
7. Ensure the response aligns with the knowledge base context

When feedback is provided, also follow these additional instructions:
1. Incorporate the user feedback to improve the response
2. Address the specific concerns/changes requested in the feedback""",
    }
},
    # we can provide keywords to differentiate between same criteria
    "categories": [
        {"name": "Customer Support", "description": " Emails related to customer inquiries, complaints, issue resolution, or general assistance requests"},
        {"name": "Welcome", "description": "Emails sent to new customers or users as part of an onboarding process, welcoming them to the service or platform."},
        {"name": "Order Confirmation", "description": "Emails confirming a customer’s order or purchase, including details such as order number, item summary, and payment information."},
        {"name":"Promotions and Offers", "description": "Emails promoting new products, services, or special offers, highlighting discounts, and providing information about upcoming events or promotions."},
        {"name":"CCS-HH","description":"Emails related to Homeowners and Household insurance policies, including policy details, updates, renewals, claims, and coverage information."}
    ],
    "workflow":{
        "processing_type":"concurrent",
        "processing_limit":10,
    }

}
RAG_ENDPOINT="https://hhi.qburst.build/rag/api/chat"
