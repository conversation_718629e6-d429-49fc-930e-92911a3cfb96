from typing import Dict, Optional, Any, List
from pydantic import BaseModel, Field
from enum import Enum

class HttpMethod(str, Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"

class ParameterLocation(str, Enum):
    QUERY = "query"
    BODY = "body"
    HEADER = "header"
    PATH = "path"

class Parameter(BaseModel):
    name: str
    type: str
    required: bool
    defaultValue: Optional[Any] = None
    location: ParameterLocation
    description: str

class RequestConfig(BaseModel):
    contentType: str
    requestFormat: str

class ActionCreate(BaseModel):
    name: str
    description: str
    endpoint: str
    method: HttpMethod
    parameters: Optional[List[Parameter]] = None
    isActive: bool = True
    requestConfig: RequestConfig
    id: Optional[str] = None

class ActionInDB(ActionCreate):
    id: str = Field(alias="_id")
    user_id: str
