[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
user=root
loglevel=info

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:app]
command=uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload --log-level debug
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/app.err.log
stdout_logfile=/var/log/supervisor/app.out.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"

[program:worker]
command=python -m app.workers.workflow_worker
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/worker.err.log
stdout_logfile=/var/log/supervisor/worker.out.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"
