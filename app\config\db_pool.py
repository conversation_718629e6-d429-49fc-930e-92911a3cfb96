from motor.motor_asyncio import AsyncIOMotorClient
import os
from typing import Dict
from functools import wraps
import asyncio

# Main connection pool for API operations
main_client = AsyncIOMotorClient(
    os.getenv("MONGODB_URL", "mongodb://localhost:27017"),
    maxPoolSize=100,  # Increased for API operations
    minPoolSize=10,
    maxIdleTimeMS=30000,
    serverSelectionTimeoutMS=2000,
    connectTimeoutMS=2000,
    socketTimeoutMS=2000,
    waitQueueTimeoutMS=2000,
    retryWrites=True,
    retryReads=True
)

# Dedicated connection pool for workflow processing
workflow_client = AsyncIOMotorClient(
    os.getenv("MONGODB_WORKER_URL", os.getenv("MONGODB_URL", "mongodb://localhost:27017")),
    maxPoolSize=20,  # Dedicated pool for background work
    minPoolSize=5,
    maxIdleTimeMS=60000,
    serverSelectionTimeoutMS=10000,
    connectTimeoutMS=10000,
    socketTimeoutMS=10000,
    waitQueueTimeoutMS=10000,
    retryWrites=True,
    retryReads=True
)

# Cache the database instances
_db_cache: Dict[str, AsyncIOMotorClient] = {}

def get_workflow_db():
    """Get database instance from workflow connection pool"""
    db_name = os.getenv("DB_NAME", "email_responder")
    if "workflow_db" not in _db_cache:
        _db_cache["workflow_db"] = workflow_client[db_name]
    return _db_cache["workflow_db"]

def get_main_db():
    """Get database instance from main connection pool"""
    db_name = os.getenv("DB_NAME", "email_responder")
    if "main_db" not in _db_cache:
        _db_cache["main_db"] = main_client[db_name]
    return _db_cache["main_db"]

def get_client():
    """Get the main MongoDB client instance"""
    return main_client

def with_database_retry(max_retries=3, delay=1):
    """Decorator for retrying database operations"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_error = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_error = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (attempt + 1))
            raise last_error
        return wrapper
    return decorator
