import logging
from typing import Optional
from fastapi import HTTPException
from app.services.email_service import EmailService
from app.utils.security import decode_jwt_token
from app.schemas.email_schema import EmailReply

logger = logging.getLogger(__name__)

class EmailController:
    @staticmethod
    async def fetch_emails(
        db, token: str, page: int = 1, page_size: int = 10,user_id: str = None, 
        refresh: bool = False, status: str = None, source: Optional[str] = None,
        category: Optional[str] = None,
        sentiment: Optional[str] = None,
        urgency: Optional[str] = None,
        search: Optional[str] = None,
        exclude: Optional[str] = None,
        conversation_id: Optional[str] = None
    ):
        """Fetch emails with pagination and optional refresh from server"""
        try:
            print("conversation_id: ", conversation_id)
            if user_id is None:
                user_id = decode_jwt_token(token)

            emails_data = await EmailService.fetch_emails(
                db, user_id, page, page_size, refresh, status,source,
                category=category,
                sentiment=sentiment,
                urgency=urgency,
                search=search,
                exclude=exclude,
                conversation_id=conversation_id
            )
            return {
                "status_code": 200,
                "status": "OK",
                "message": "Emails fetched successfully",
                "data": {
                    "emails": emails_data.get("emails", []),
                    "total": emails_data.get("total", 0),
                    "page": emails_data.get("page", 1),
                    "page_size": emails_data.get("page_size", 10),
                    "has_more": emails_data.get("has_more", False),
                    "refreshed": emails_data.get("refreshed", False),
                    "latest_fetched_date": emails_data.get("latest_fetched_date")
                }
            }
        except HTTPException as he:
            logger.warning(f"Error fetching emails: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error fetching emails: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def send_reply(db, reply_data: EmailReply):
        """Send email reply or save as draft"""
        try:
            # user_id = decode_jwt_token(token)
            result = await EmailService.send_reply(db,  reply_data.dict())
            return result
        except HTTPException as he:
            logger.warning(f"Error sending reply: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error sending reply: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to send reply")
