from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, handler):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, **kwargs):
        field_schema.update(type="string")
        return field_schema

class EmailConfig(BaseModel):
    smtp_server: str
    port: int
    sender_email: str
    password: str
    use_tls: bool = True

class PromptConfig(BaseModel):
    key: str
    value: str
    description: Optional[str] = None

class CategoryConfig(BaseModel):
    name: str
    description: str
    sample_email: Optional[str] = None

class PromptDetail(BaseModel):
    system: str
    user: str

class WorkflowConfig(BaseModel):
    processing_type: str = "sequential"  # "sequential" or "concurrent"
    processing_limit: int = 1  # Number of concurrent workflows when type is concurrent

class PromptConfig(BaseModel):
    system: str
    user: str

class EmailResponderConfig(BaseModel):
    prompts: Dict[str, PromptConfig]
    categories: List[CategoryConfig]
    workflow: Optional[WorkflowConfig] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "prompts": {
                    "classification": {
                        "system": "Classify the text into a category.",
                        "user": "What is the classification of this text?"
                    },
                    "sentiment": {
                        "system": "Analyze the sentiment of the text.",
                        "user": "What is the sentiment of this text?"
                    },
                    "summary": {
                        "system": "Summarize the text in a concise manner.", 
                        "user": "Summarize this text in one sentence."
                    }
                },
                "categories": [
                    {
                        "name": "Customer Support",
                        "description": "Emails related to customer inquiries, complaints, issue resolution, or general assistance requests"
                    },
                    {
                        "name": "Welcome",
                        "description": "Emails sent to new customers or users as part of an onboarding process, welcoming them to the service or platform."
                    },
                    {
                        "name": "Order Confirmation", 
                        "description": "Emails confirming a customer's order or purchase, including details such as order number, item summary, and payment information."
                    },
                    {
                        "name": "Promotions and Offers",
                        "description": "Emails promoting new products, services, or special offers, highlighting discounts, and providing information about upcoming events or promotions."
                    }
                ],
                "workflow": {
                    "processing_type": "concurrent",
                    "processing_limit": 10
                }
            }
        }
    }

class EmailAdaptarConfig(BaseModel):
    folderName: list[str]

class Configuration(BaseModel):
    workflow_config: Optional[WorkflowConfig] = None
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    email_responder: Optional[EmailResponderConfig] = None
    email_adaptar: Optional[EmailAdaptarConfig] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class ConfigurationCreate(BaseModel):
    email_responder: Optional[EmailResponderConfig] = None
    email_adaptar: Optional[EmailAdaptarConfig] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True
    }

class EmailConfigUpdate(BaseModel):
    email: Optional[str] = None
    password: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None

class ConfigurationUpdate(BaseModel):
    email_responder: Optional[EmailResponderConfig] = None
    email_adaptar: Optional[EmailAdaptarConfig] = None
    email_config: Optional[EmailConfigUpdate] = None
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_schema_extra": {
            "example": {
                "email_responder": {
                    "prompts": {
                        "classification": {
                            "system": "Classify the text into a category.",
                            "user": "What is the classification of this text?"
                        }
                    },
                    "categories": [
                        {
                            "name": "Customer Support",
                            "description": "Support related emails"
                        }
                    ],
                    "workflow": {
                        "processing_type": "concurrent",
                        "processing_limit": 1
                    }
                }
            }
        }
    }
