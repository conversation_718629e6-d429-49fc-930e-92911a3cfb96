import logging
from fastapi import HTTPException
from app.services.config_service import ConfigService
from app.schemas.config_schema import ConfigurationCreate, Configuration, ConfigurationUpdate
from app.utils.security import decode_jwt_token
from app.schemas.prompt_schema import PromptSettingsUpdate

logger = logging.getLogger(__name__)

class ConfigController:
    @staticmethod
    async def get_user_config(db, token: str):
        """Get user configuration details"""
        try:
            user_id = decode_jwt_token(token)
            result = await ConfigService.get_user_config(db, user_id)
            if not result:
                return {
                    "status_code": 404,
                    "status": "Not Found",
                    "message": "No configurations found",
                    "data": None
                }
            return {
                "status_code": 200,
                "status": "OK",
                "message": "Configurations retrieved successfully",
                "data": result
            }
        except HTTPException as he:
            logger.warning(f"Error fetching configurations: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error fetching configurations: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def get_prompt_settings(db, token: str):
        """Get prompt settings configuration"""
        try:
            user_id = decode_jwt_token(token)
            result = await ConfigService.get_prompt_settings(db, user_id)
            return {
                "status_code": 200,
                "status": "OK",
                "message": "Prompt settings retrieved successfully",
                "data": result
            }
        except HTTPException as he:
            logger.warning(f"Error fetching prompt settings: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error fetching prompt settings: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def update_prompt_settings(settings: PromptSettingsUpdate, db, token: str):
        """Update prompt settings configuration"""
        try:
            user_id = decode_jwt_token(token)
            result = await ConfigService.update_prompt_settings(settings, db, user_id)
            return result
        except HTTPException as he:
            logger.warning(f"Error updating prompt settings: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error updating prompt settings: {str(e)}")
            raise HTTPException(status_code=500, detail="Error updating prompt settings")

    @staticmethod
    async def update_user_config(config: ConfigurationUpdate, db, token: str):
        """Update user configuration"""
        try:
            user_id = decode_jwt_token(token)
            result = await ConfigService.update_user_config(config, db, user_id)
            return result
        except HTTPException as he:
            logger.warning(f"Error updating configurations: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error updating configurations: {str(e)}")
            raise HTTPException(status_code=500, detail="Error updating configurations")

    @staticmethod
    async def create_configurations(config: ConfigurationCreate, db, token: str):
        """Create new configurations"""
        try:
            user_id = decode_jwt_token(token)
            result = await ConfigService.create_configurations(config, db, user_id)
            return result
        except HTTPException as he:
            logger.warning(f"Error creating configurations: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error creating configurations: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")
