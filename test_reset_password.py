#!/usr/bin/env python3
"""
Simple test script to verify the reset password functionality
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.user_service import UserService
from app.config.database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_reset_password():
    """Test the reset password functionality"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        # Test data (replace with actual values for testing)
        test_email = "<EMAIL>"
        test_otp = "123456"  # This should be a real OTP from forget password
        new_password = "NewPassword123!"

        logger.info(f"Testing reset password functionality with email: {test_email}")

        # Call the reset password service
        result = await UserService.reset_password(test_email, test_otp, new_password, db)
        
        logger.info(f"Reset password result: {result}")
        
        # Close database connection
        client.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        return {"error": str(e)}

async def test_complete_flow():
    """Test the complete forget password -> reset password flow"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        test_email = "<EMAIL>"
        new_password = "NewPassword123!"
        
        logger.info("Testing complete password reset flow...")
        
        # Step 1: Request password reset
        logger.info("Step 1: Requesting password reset...")
        forget_result = await UserService.forget_password(test_email, db)
        logger.info(f"Forget password result: {forget_result}")
        
        # Step 2: Get the reset OTP from database (in real scenario, user gets this from email)
        user = await db.users.find_one({
            "$or": [
                {"email_config.gmail_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.outlook_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.imap_config.email": {"$regex": f"^{test_email}$", "$options": "i"}}
            ]
        })

        if user and "reset_otp" in user:
            reset_otp = user["reset_otp"]
            logger.info(f"Found reset OTP: {reset_otp}")

            # Step 3: Reset password using the OTP
            logger.info("Step 2: Resetting password with OTP...")
            reset_result = await UserService.reset_password(test_email, reset_otp, new_password, db)
            logger.info(f"Reset password result: {reset_result}")
        else:
            logger.warning("No reset OTP found in database")
        
        # Close database connection
        client.close()
        
        return {"forget_result": forget_result, "reset_result": reset_result if 'reset_result' in locals() else None}
        
    except Exception as e:
        logger.error(f"Complete flow test failed with error: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    print("Testing reset password functionality...")
    
    # Test individual reset password function
    print("\n=== Testing Reset Password ===")
    result = asyncio.run(test_reset_password())
    print(f"Reset password test result: {result}")
    
    # Test complete flow (uncomment to test with real data)
    # print("\n=== Testing Complete Flow ===")
    # flow_result = asyncio.run(test_complete_flow())
    # print(f"Complete flow test result: {flow_result}")
