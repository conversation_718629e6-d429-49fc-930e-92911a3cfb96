openapi: 3.0.0
info:
  title: FastAPI User Management API
  description: API for user management with authentication and authorization
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8000
    description: Development server

tags:
  - name: Users
    description: User management operations
  - name: Root
    description: Root endpoint operations
  - name: Workflows
    description: Email processing workflow operations
  - name: Configurations
    description: Configuration management operations
  - name: Emails
    description: Email management operations
  - name: Authentication
    description: Authentication related operations

paths:
  /:
    get:
      tags:
        - Root
      summary: Root endpoint
      description: Returns a welcome message
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Welcome to FastAPI User Management API"

  /workflows:
    post:
      tags:
        - Workflows
      summary: Create workflow
      description: Create a new workflow for processing selected emails
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email_ids:
                  type: array
                  items:
                    type: string
                  description: List of email IDs to process
            example:
              email_ids: ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"]
      responses:
        '201':
          description: Workflow created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 201
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Workflow created successfully"
                  data:
                    type: object
                    properties:
                      workflow_id:
                        type: string
                        example: "507f1f77bcf86cd799439013"
                      sentiment:
                        type: object
                        properties:
                          label:
                            type: string
                            enum: [positive, neutral, negative]
                            description: Sentiment classification
                          score:
                            type: number
                            format: float
                            minimum: -1.0
                            maximum: 1.0
                            description: Sentiment score (-1.0 to 1.0)
                          confidence:
                            type: number
                            format: float
                            minimum: 0.0
                            maximum: 1.0
                            description: Confidence score of the analysis
                        example:
                          label: "positive"
                          score: 0.8
                          confidence: 0.95
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /workflows/{workflow_id}:
    get:
      tags:
        - Workflows
      summary: Get workflow status
      description: Get current status of a workflow
      security:
        - bearerAuth: []
      parameters:
        - name: workflow_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the workflow to retrieve
      responses:
        '200':
          description: Workflow status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Workflow status retrieved successfully"
                  data:
                    $ref: '#/components/schemas/WorkflowContext'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/outlook:
    get:
      tags:
        - Authentication
      summary: Initiate Outlook OAuth
      description: Initiate Outlook OAuth authentication flow
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OAuth URL generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  auth_url:
                    type: string
                    description: URL to redirect user for OAuth authentication
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/outlook/callback:
    get:
      tags:
        - Authentication
      summary: Outlook OAuth callback
      description: Handle Outlook OAuth callback
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
          description: OAuth authorization code
        - name: state
          in: query
          required: true
          schema:
            type: string
          description: State parameter for validation
      responses:
        '200':
          description: OAuth callback handled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Authentication successful"
                  token_type:
                    type: string
                    example: "Bearer"
                  expires_in:
                    type: integer
                    example: 3600
        '400':
          $ref: '#/components/responses/BadRequestError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/register:
    post:
      tags:
        - Users
      summary: Register new user
      description: Register a new user with email configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
            example:
              username: "johndoe"
              email: "<EMAIL>"
              password: "StrongPass123!"
              full_name: "John Doe"
              email_config:
                email: "<EMAIL>"
                password: "smtppass123"
                host: "smtp.example.com"
                port: 587
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
              example:
                status_code: 201
                status: "success"
                message: "User registered successfully"
                data:
                  id: "507f1f77bcf86cd799439011"
                  username: "johndoe"
                  email: "<EMAIL>"
                  full_name: "John Doe"
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    ActionCreate:
      type: object
      required:
        - name
        - description
        - endpoint
        - method
        - requestConfig
      properties:
        name:
          type: string
          description: Name of the custom action
          example: "Create Insurance Quote"
        description:
          type: string
          description: Description of what the action does
          example: "Creates a new insurance quote in the system"
        endpoint:
          type: string
          description: API endpoint URL for the action
          example: "https://api.example.com/quotes"
        method:
          type: string
          enum: [GET, POST, PUT, DELETE, PATCH]
          description: HTTP method for the action
          example: "POST"
        parameters:
          type: array
          items:
            type: object
            required:
              - name
              - type
              - required
              - location
              - description
            properties:
              name:
                type: string
                description: Parameter name
              type:
                type: string
                description: Parameter data type
              required:
                type: boolean
                description: Whether the parameter is required
              defaultValue:
                type: string
                description: Default value for the parameter
              location:
                type: string
                enum: [query, body, header, path]
                description: Where the parameter should be placed in the request
              description:
                type: string
                description: Parameter description
        isActive:
          type: boolean
          description: Whether the action is active
          default: true
        requestConfig:
          type: object
          required:
            - contentType
            - requestFormat
          properties:
            contentType:
              type: string
              description: Content type for the request
              example: "application/json"
            requestFormat:
              type: string
              description: Format of the request body
              example: "json"
        id:
          type: string
          description: Optional ID for updating existing action
          example: "507f1f77bcf86cd799439011"

    ActionInDB:
      allOf:
        - $ref: '#/components/schemas/ActionCreate'
        - type: object
          required:
            - id
            - user_id
          properties:
            id:
              type: string
              description: Unique identifier of the action
            user_id:
              type: string
              description: ID of the user who owns this action
              example:
                detail: "Email already registered"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                detail: "Internal server error"

  /users/login:
    post:
      tags:
        - Users
      summary: User login
      description: Authenticate user and get access token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLogin'
            example:
              username: "johndoe"
              password: "StrongPass123!"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  token_type:
                    type: string
              example:
                access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                token_type: "bearer"
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                detail: "Invalid username or password"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                detail: "Internal server error"

  /configurations:
    put:
      tags:
        - Configurations
      summary: Update configurations
      description: Update existing configurations including email settings, prompts, and categories
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationUpdate'
      responses:
        '200':
          description: Configurations updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Configurations not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/configurations:
    get:
      tags:
        - Configurations
      summary: Get configurations
      description: Fetch configurations including email, prompts and categories along with user details
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: config_type
          schema:
            type: string
          required: false
          description: Filter configurations by type (email_responder, email_adaptar)
      responses:
        '200':
          description: Configurations retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Configurations retrieved successfully"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      configurations:
                        $ref: '#/components/schemas/Configuration'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Configurations not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Configurations
      summary: Create configurations
      description: Create new configurations including email responder and email adapter settings
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationCreate'
            example:
              email_responder:
                prompts:
                  classification: "What is the classification of this text?"
                  sentiment: "What is the sentiment of this text?"
                  summary: "Summarize this text in one sentence."
                categories:
                  - name: "category1"
                    description: "desc1"
                    sample_email: "<EMAIL>"
              email_adaptar:
                folderName: ["folder1", "folder2"]
      responses:
        '201':
          description: Configurations created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 201
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Configurations created successfully"
                  data:
                    $ref: '#/components/schemas/Configuration'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users:
    get:
      tags:
        - Users
      summary: Get current user details
      description: Get details of the currently authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "User details fetched successfully"
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        description: User's unique identifier
                      username:
                        type: string
                        description: User's username
                      full_name:
                        type: string
                        description: User's full name
        '401':
          description: Unauthorized - Missing or invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /drafts/{email_id}/regenerate:
    put:
      tags:
        - Drafts
      summary: Regenerate draft response
      description: Regenerate draft response for an email with feedback and update it in the email server
      security:
        - bearerAuth: []
      parameters:
        - name: email_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the email to regenerate draft for
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: Feedback for regenerating the draft
            example:
              content: "Please make the tone more formal"
      responses:
        '200':
          description: Draft regenerated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Draft response regenerated successfully"
                  data:
                    type: object
                    properties:
                      draft_id:
                        type: string
                        description: ID of the regenerated draft
                      response:
                        type: string
                        description: New draft content
        '400':
          description: Bad request - Invalid feedback format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /drafts/{email_id}:
    put:
      tags:
        - Drafts
      summary: Update draft content
      description: Update draft response with manually edited content
      security:
        - bearerAuth: []
      parameters:
        - name: email_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the email to update draft for
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: New draft content
            example:
              content: "Updated draft response content"
      responses:
        '200':
          description: Draft updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Draft response updated successfully"
                  data:
                    type: object
                    properties:
                      draft_id:
                        type: string
                      draft_server_id:
                        type: string
                      response:
                        type: string
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - Drafts
      summary: Delete draft
      description: Delete draft response from email server and update database
      security:
        - bearerAuth: []
      parameters:
        - name: email_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the email to delete draft for
      responses:
        '200':
          description: Draft deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Draft deleted successfully"
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /emails/category:
    post:
      tags:
        - Emails
      summary: Add new category
      description: Add a new category to configurations and update email category. This endpoint allows users to create a new email category and immediately apply it to an existing email.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryCreate'
            example:
              name: "Urgent Support"
              description: "High priority customer support requests requiring immediate attention"
              sample_email: "Subject: Urgent: Service Outage\nBody: Our production system is down..."
              email_id: "507f1f77bcf86cd799439011"
      responses:
        '201':
          description: Category added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 201
                  status:
                    type: string
                    example: "success"
                  message:
                    type: string
                    example: "Category added and email updated successfully"
                  data:
                    $ref: '#/components/schemas/CategoryConfig'
        '400':
          description: Category already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          description: Configuration or email not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/emails:
    get:
      tags:
        - Emails
      summary: Fetch emails
      description: Fetch emails from the user's email account with pagination support
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (starts from 1)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: Number of emails per page (1-100)
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: refresh
          in: query
          description: Force refresh from email server
          required: false
          schema:
            type: boolean
            default: false
        - name: status
          in: query
          description: Filter emails by status (unprocessed/processed)
          required: false
          schema:
            type: string
            enum: [unprocessed, processed]
      responses:
        '200':
          description: Emails fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Emails fetched successfully"
                  data:
                    type: object
                    properties:
                      emails:
                        type: array
                        items:
                          $ref: '#/components/schemas/EmailMessage'
                      total:
                        type: integer
                        description: Total number of emails
                      page:
                        type: integer
                        description: Current page number
                      page_size:
                        type: integer
                        description: Number of emails per page
                      has_more:
                        type: boolean
                        description: Whether there are more emails to fetch
                      refreshed:
                        type: boolean
                        description: Whether emails were refreshed from server
                      latest_updated_date:
                        type: string
                        format: date-time
                        description: Timestamp of the latest email update
        '401':
          description: Unauthorized - Missing or invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /actions:
    post:
      tags:
        - Actions
      summary: Create custom action
      description: Create a new custom action for automated email processing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActionCreate'
      responses:
        '201':
          description: Action created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 201
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Action created successfully"
                  data:
                    $ref: '#/components/schemas/ActionInDB'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    get:
      tags:
        - Actions
      summary: Get all actions
      description: Get all custom actions for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Actions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ActionInDB'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /actions/{action_id}:
    put:
      tags:
        - Actions
      summary: Update action
      description: Update an existing custom action
      security:
        - bearerAuth: []
      parameters:
        - name: action_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the action to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActionCreate'
      responses:
        '200':
          description: Action updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Action updated successfully"
                  data:
                    $ref: '#/components/schemas/ActionInDB'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - Actions
      summary: Delete action
      description: Delete an existing custom action
      security:
        - bearerAuth: []
      parameters:
        - name: action_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the action to delete
      responses:
        '200':
          description: Action deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  message:
                    type: string
                    example: "Action deleted successfully"
                  data:
                    type: object
                    properties:
                      deleted:
                        type: boolean
                        example: true
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /actions/{action_id}/execute:
    post:
      tags:
        - Actions
      summary: Execute action
      description: Execute a specific custom action with provided context data
      security:
        - bearerAuth: []
      parameters:
        - name: action_id
          in: path
          required: true
          schema:
            type: string
          description: ID of the action to execute
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
              description: Context data for action execution
      responses:
        '200':
          description: Action executed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status_code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: "OK"
                  data:
                    type: object
                    properties:
                      status_code:
                        type: integer
                        description: HTTP status code from action execution
                      response:
                        type: object
                        description: Response data from action execution
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/{user_id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Retrieve user information by user ID
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  responses:
    UnauthorizedError:
      description: Authentication information is missing or invalid
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Could not validate credentials"

    ForbiddenError:
      description: Not authorized to access the resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Not authorized to access this resource"

    NotFoundError:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Resource not found"

    BadRequestError:
      description: The request is invalid
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Invalid request parameters"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Internal server error"

  schemas:
    WorkflowContext:
      type: object
      properties:
        user_id:
          type: string
          description: ID of the user who owns the workflow
        email_ids:
          type: array
          items:
            type: string
          description: List of email IDs being processed
        current_state:
          type: string
          enum: [initiated, fetched, category_classified, sentiment_analyzed, context_retrieved, response_generated, draft_created, completed, error]
          description: Current state of the workflow
        error_message:
          type: string
          description: Error message if workflow failed
        results:
          type: object
          description: Results from each processing step
        created_at:
          type: string
          format: date-time
          description: Workflow creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
        retry_count:
          type: integer
          description: Number of retry attempts
        max_retries:
          type: integer
          description: Maximum number of retry attempts allowed
    UserBase:
      type: object
      properties:
        email:
          type: string
          format: email
        username:
          type: string
          minLength: 3
          maxLength: 50
        full_name:
          type: string
          minLength: 2
          maxLength: 100
      required:
        - email
        - username
        - full_name

    UserCreate:
      allOf:
        - $ref: '#/components/schemas/UserBase'
        - type: object
          properties:
            password:
              type: string
              minLength: 8
              description: User's password (must contain at least 8 characters, including uppercase, lowercase, number, and special character)
            email_config:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: SMTP email address
                password:
                  type: string
                  description: SMTP password
                host:
                  type: string
                  description: SMTP server host
                port:
                  type: integer
                  description: SMTP server port
              required:
                - email
                - password
                - host
                - port
          required:
            - password
            - email_config

    UserLogin:
      type: object
      properties:
        username:
          type: string
          description: User's username
        password:
          type: string
          description: User's password
      required:
        - username
        - password

    UserResponse:
      type: object
      properties:
        status_code:
          type: integer
          description: HTTP status code
        status:
          type: string
          description: Response status (success/error)
        message:
          type: string
          description: Response message
        data:
          $ref: '#/components/schemas/User'
      required:
        - status_code
        - status
        - message
        - data

    User:
      allOf:
        - $ref: '#/components/schemas/UserBase'
        - type: object
          properties:
            id:
              type: string
              description: Unique identifier of the user

    ConfigurationUpdate:
      type: object
      properties:
        email_responder:
          type: object
          properties:
            prompts:
              type: object
              additionalProperties:
                type: string
            categories:
              type: array
              items:
                $ref: '#/components/schemas/CategoryConfig'
        email_adaptar:
          type: object
          properties:
            foldername:
              type: array
              items:
                type: string

    ConfigurationResponse:
      type: object
      properties:
        status_code:
          type: integer
          description: HTTP status code
        status:
          type: string
          description: Response status
        message:
          type: string
          description: Response message
        data:
          $ref: '#/components/schemas/Configuration'

    Error:
      type: object
      properties:
        detail:
          type: string
          description: Error message
      required:
        - detail

    EmailMessage:
      type: object
      properties:
        message_id:
          type: string
          description: Unique identifier of the email
        subject:
          type: string
          description: Email subject
        sender:
          type: string
          description: Email sender address
        recipient:
          type: string
          description: Email recipient address
        date:
          type: string
          format: date-time
          description: Original email date
        updated_date:
          type: string
          format: date-time
          description: Last update timestamp
        body:
          type: string
          description: Email body content
        is_read:
          type: boolean
          description: Whether the email has been read
        folder:
          type: string
          description: Email folder location
          default: "INBOX"
        status:
          type: string
          description: Processing status
          enum: [unprocessed, processed]
          default: "unprocessed"

    Configuration:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier of the configuration
        user_id:
          type: string
          description: ID of the user who owns this configuration
        email_responder:
          type: object
          properties:
            prompts:
              type: object
              additionalProperties:
                type: string
              description: Key-value pairs of prompt types and their content
            categories:
              type: array
              items:
                $ref: '#/components/schemas/CategoryConfig'
        email_adaptar:
          type: object
          properties:
            folderName:
              type: array
              items:
                type: string
              description: List of folder names for email adaptation
        email_config:
          type: object
          properties:
            email:
              type: string
              format: email
            host:
              type: string
            port:
              type: integer

    CategoryConfig:
      type: object
      properties:
        name:
          type: string
          description: Category name
        description:
          type: string
          description: Category description
        sample_email:
          type: string
          description: Sample email for the category

    CategoryCreate:
      type: object
      required:
        - name
        - description
        - sample_email
        - email_id
      properties:
        name:
          type: string
          description: Name of the new category
          example: "Urgent Support"
        description:
          type: string
          description: Description of the category and its purpose
          example: "High priority customer support requests requiring immediate attention"
        sample_email:
          type: string
          description: Example email content that fits this category
          example: "Subject: Urgent: Service Outage\nBody: Our production system is down..."
        email_id:
          type: string
          description: MongoDB ObjectId of the email to be categorized
          example: "507f1f77bcf86cd799439011"

    ConfigurationCreate:
      type: object
      properties:
        email_responder:
          type: object
          properties:
            prompts:
              type: object
              additionalProperties:
                type: string
            categories:
              type: array
              items:
                $ref: '#/components/schemas/CategoryConfig'
        email_adaptar:
          type: object
          properties:
            folderName:
              type: array
              items:
                type: string

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
