import socketio
from typing import Dict


sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins='*',
    # cors_allowed_origins=['*'],
    allow_headers='*',
    # allow_headers=['*'],
    namespaces='*',
    logger=True,
    engineio_logger=True,
    ping_timeout=60000,  # milliseconds
    ping_interval=25000,  # milliseconds
    max_http_buffer_size=1e8,
    allow_upgrades=True
)
socket_app = socketio.ASGIApp(
    socketio_server=sio,
    # socketio_path='/socket.io/'  
    socketio_path='/email-responder/a2a/socket.io/'  
)

class SocketManager:
    def __init__(self):
        self.active_users: Dict[str, str] = {}  # sid -> user_id mapping
        self.user_to_sids: Dict[str, list] = {}  # user_id -> list of sids

    async def connect_user(self, sid: str, user_id: str = None):
        """Store user connection and disconnect old ones if the same user connects again"""
        user_id = user_id or sid

        # Ensure user_id exists in dictionary before appending
        if user_id not in self.user_to_sids:
            self.user_to_sids[user_id] = []  # Initialize list to avoid KeyError

        # If the user already has active connections, disconnect the previous ones
        old_sids = self.user_to_sids[user_id].copy()  # Copy to avoid modifying while iterating
        for old_sid in old_sids:
            print(f"Disconnecting old session {old_sid} for user {user_id}")
            await sio.disconnect(old_sid)  # Disconnect old session

        # Clear old SIDs before storing the new one
        self.user_to_sids[user_id] = []  
        self.active_users[sid] = user_id
        self.user_to_sids[user_id].append(sid)  # Now, this won't raise KeyError

        print(f"User {user_id} connected with sid {sid}")
        print(f"Active connections for user {user_id}: {self.user_to_sids[user_id]}")


        
    def disconnect_user(self, sid: str):
        """Remove user connection"""
        if sid in self.active_users:
            user_id = self.active_users[sid]
            
            # Remove sid from user's connections
            if user_id in self.user_to_sids and sid in self.user_to_sids[user_id]:
                self.user_to_sids[user_id].remove(sid)
                
                # Clean up empty user entries
                if not self.user_to_sids[user_id]:
                    del self.user_to_sids[user_id]
            
            # Remove from active users
            del self.active_users[sid]
            print(f"User {user_id} disconnected (sid: {sid})")

    async def broadcast(self, event: str, data: dict, room: str = None):
        """Broadcast message to all users or specific room"""
        await sio.emit(event, data, room=room)

    async def send_personal_message(self, event: str, data: dict, sid: str):
        """Send message to specific user by socket ID"""
        await sio.emit(event, data, room=sid)
        
    async def send_to_user(self, user_id: str, event: str, data: dict):
        print("***************Sending message to user:", user_id)
        """Send message to all connections of a specific user"""
        if user_id in self.user_to_sids:
            # Ensure data is JSON serializable by handling datetime objects
            import json
            from datetime import datetime
            
            def json_serial(obj):
                """JSON serializer for objects not serializable by default json code"""
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Type {type(obj)} not serializable")
            
            # Test if data is serializable and fix if needed
            try:
                json.dumps(data)
            except TypeError:
                # If not serializable, create a copy and convert datetime objects
                import copy
                serializable_data = copy.deepcopy(data)
                
                def convert_datetime(item):
                    if isinstance(item, dict):
                        for key, value in list(item.items()):
                            if isinstance(value, datetime):
                                item[key] = value.isoformat()
                            elif isinstance(value, (dict, list)):
                                convert_datetime(value)
                    elif isinstance(item, list):
                        for i, value in enumerate(item):
                            if isinstance(value, datetime):
                                item[i] = value.isoformat()
                            elif isinstance(value, (dict, list)):
                                convert_datetime(value)
                
                convert_datetime(serializable_data)
                data = serializable_data
            
            # Send to all user's connections
            for sid in self.user_to_sids[user_id]:
                await sio.emit(event, data, room=sid, namespace='/email-responder/a2a/socket.io')
                print(f"Sent {event} to user {user_id} (sid: {sid})")
        else:
            print(f"No active connections for user {user_id}")

manager = SocketManager()

@sio.event(namespace='/email-responder/a2a/socket.io')
# @sio.event
async def connect(sid, environ):
    """Handle client connection"""
    print(f"------New connection established: {sid}-------")
    
    # Extract user_id from query parameters if available
    query_string = environ.get('QUERY_STRING', '')
    import urllib.parse
    parsed_qs = urllib.parse.parse_qs(query_string)
    user_id = parsed_qs.get('user_id', [None])[0]
    
    if user_id:
        # If user_id is provided in query params, authenticate immediately
        await manager.connect_user(sid, user_id)
        print(f"-----User {user_id} automatically authenticated via query param (sid: {sid})------")
        await sio.emit('authentication_response', 
                      {'status': 'success', 'message': 'Authentication successful via query param'}, 
                      room=sid)
    else:
        # Otherwise just connect without user association
        await manager.connect_user(sid)
        
    await sio.emit('connection_response', {'message': 'Connected successfully'}, room=sid)
    print(f"Active users: {manager.active_users}")
    return {'status': 'Connected'}

@sio.event
async def authenticate(sid, data):
    """Authenticate user and associate socket with user ID"""
    user_id = data.get('user_id')
    if user_id:
        await manager.connect_user(sid, user_id)
        await sio.emit('authentication_response', 
                      {'status': 'success', 'message': 'Authentication successful'}, 
                      room=sid)
        print(f"User {user_id} authenticated (sid: {sid})")
        return {'status': 'Authenticated'}
    else:
        await sio.emit('authentication_response', 
                      {'status': 'error', 'message': 'User ID required'}, 
                      room=sid)
        return {'status': 'Failed'}

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    manager.disconnect_user(sid)
    await sio.emit('user_disconnected', {'message': f'Client {sid} disconnected'})

@sio.event
async def message(sid, data):
    """Handle incoming messages"""
    print(f"----Received message from {sid}: {data}-----")
    
    # Check if this is an authentication message in string format
    if isinstance(data, str) and "authenticate" in data:
        try:
            # Try to extract user_id from the message
            import re
            import json
            
            # Extract the JSON part from the message
            match = re.search(r'42\["authenticate",(\{.*?\})\]', data)
            if match:
                auth_data = json.loads(match.group(1))
                user_id = auth_data.get("user_id")
                
                if user_id:
                    # Authenticate the user
                    await manager.connect_user(sid, user_id)
                    await sio.emit('authentication_response', 
                                  {'status': 'success', 'message': 'Authentication successful'}, 
                                  room=sid)
                    print(f"----User {user_id} authenticated (sid: {sid})----")
                    return
        except Exception as e:
            print(f"Error parsing authentication message: {str(e)}")
    
    # If not an authentication message or parsing failed, handle as regular message
    await manager.send_personal_message('message_response', {'message': f'You sent: {data}'}, sid)
    await manager.broadcast('broadcast_message', {'message': f'Client broadcast: {data}'})

@sio.on('custom_event')
async def handle_custom_event(sid, data):
    """Handle custom events"""
    print(f"Received custom event from {sid}: {data}")
    await manager.broadcast('custom_response', {'message': f'Received custom event: {data}'})

@sio.on('private_message')
async def handle_private_message(sid, data):
    """Handle private messages"""
    print(f"Received private message from {sid}: {data}")
    target_sid = data.get('to')
    if target_sid in manager.active_users:
        await manager.send_personal_message('private_response', 
            {'message': f'Private message: {data.get("message")}'}, 
            target_sid
        )
