import logging
import requests
from datetime import datetime, timedelta
import asyncio
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.services.notification_service import NotificationService
from fastapi import HTTPException

logger = logging.getLogger(__name__)

class SubscriptionManager:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.notification_service = NotificationService(db)
        
    async def setup_subscription(self, user_id: str, notification_url: str):
        """Setup initial subscription for a user"""
        try:
            print("************Received request to setup subscription********")
            logger.info(f"Setting up subscription for user {user_id} with URL {notification_url}")
            
            # Verify notification URL format and accessibility
            logger.debug("Verifying notification URL format and accessibility")
            # try:
                # Test the notification endpoint first
            #     test_response = requests.get(
            #         f"{notification_url}?validationToken=TestToken",
            #         timeout=30
            #     )
            #     if test_response.status_code != 200:
            #         raise HTTPException(
            #             status_code=400,
            #             detail="Notification endpoint validation failed. Please ensure the endpoint is accessible and responds correctly to validation requests."
            #         )
            # except requests.exceptions.RequestException as e:
            #     raise HTTPException(
            #         status_code=400,
            #         detail=f"Notification endpoint is not accessible: {str(e)}"
            #     )

            # Create new subscription with retry logic
            logger.debug("Attempting to create subscription via notification service")
            max_retries = 3
            retry_count = 0
            last_error = None
            
            while retry_count < max_retries:
                print("************while loop********")
                try:
                    subscription_data = await self.notification_service.create_subscription(
                        user_id, 
                        notification_url
                    )
                    # If successful, break out of the retry loop
                    break
                except HTTPException as he:
                    if he.status_code == 400 and "ValidationError" in str(he.detail):
                        last_error = he
                        retry_count += 1
                        logger.warning(f"Validation attempt {retry_count} failed: {str(he.detail)}")
                        if retry_count < max_retries:
                            # Wait before retrying (exponential backoff)
                            await asyncio.sleep(5 * retry_count)
                    else:
                        raise he
                except Exception as e:
                    last_error = e
                    retry_count += 1
                    logger.warning(f"Subscription attempt {retry_count} failed: {str(e)}")
                    if retry_count < max_retries:
                        # Wait before retrying (exponential backoff)
                        await asyncio.sleep(5 * retry_count)
            
            # If we've exhausted all retries and still have an error
            if retry_count == max_retries and last_error:
                logger.error(f"All subscription attempts failed: {str(last_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error creating subscription after {max_retries} attempts: {str(last_error)}"
                )
            logger.info("Successfully created subscription")
            logger.debug(f"Subscription data: {subscription_data}")
            
            # Schedule renewal check
            logger.debug("Scheduling subscription monitoring task")
            # asyncio.create_task(self._monitor_subscription(user_id))
            logger.info("Subscription monitoring task scheduled")
            
            return subscription_data
            
        except HTTPException as he:
            logger.error(f"HTTP error in setup_subscription: {str(he)}")
            raise he
        except Exception as e:
            logger.error(f"Error setting up subscription: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to setup subscription. Error: {str(e)}"
            )
            
    async def _monitor_subscription(self, user_id: str):
        """Monitor subscription and renew before expiration"""
        while True:
            try:
                print("************Inside monitor subscription********")
                # Get subscription details
                subscription = await self.db.subscriptions.find_one({"user_id": user_id})
                if not subscription:
                    logger.warning(f"No subscription found for user {user_id}")
                    return
                    
                # Calculate time until expiration
                expiration = datetime.fromisoformat(subscription["expiration_date"].replace('Z', '+00:00'))
                now = datetime.utcnow()
                time_until_expiry = expiration - now
                print("Time until expiry:", time_until_expiry)
                # Renew if expiring within 12 hours
                if time_until_expiry <= timedelta(hours=12):
                    logger.info(f"Renewing subscription for user {user_id}")
                    await self.notification_service.renew_subscription(user_id)
                    
                # Check again in 6 hours
                await asyncio.sleep(6)  # 6 hours in seconds
                
            except Exception as e:
                logger.error(f"Error monitoring subscription: {str(e)}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying on error
                
    async def remove_subscription(self, user_id: str):
        """Remove all subscriptions for a user"""
        try:
            logger.info(f"Removing all subscriptions for user {user_id}")
            result = await self.notification_service.delete_subscription(user_id)
            logger.info(f"Subscription removal result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error removing subscriptions: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to remove subscriptions: {str(e)}"
            )
