from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from bson import ObjectId
from datetime import datetime

router = APIRouter(prefix="/sample", tags=["sample"])

class UserData(BaseModel):
    first_name: str
    last_name: str

class SamplePayload(BaseModel):
    message: str
    data: Dict[str, Any]

@router.post("/process")
async def process_data(payload: SamplePayload):
    """
    Process the incoming payload and return a response
    """
    try:
        # Sample processing logic
        processed_data = {
            "original_message": payload.message,
            "processed_data": {
                k: f"Processed {v}" if isinstance(v, str) else v * 2 
                for k, v in payload.data.items()
            },
            "status": "success"
        }
        return processed_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/info")
async def get_info():
    """
    Get sample information
    """
    return {
        "status": "active",
        "version": "1.0.0",
        "endpoints": [
            {
                "path": "/sample/process",
                "method": "POST",
                "description": "Process sample data"
            },
            {
                "path": "/sample/info",
                "method": "GET", 
                "description": "Get sample information"
            },
            {
                "path": "/sample/user",
                "method": "POST",
                "description": "Store user data"
            },
            {
                "path": "/sample/user",
                "method": "GET",
                "description": "Get user data by ID (using query parameter ?user_id=)"
            }
        ]
    }

@router.post("/user")
async def store_user_data(
    user_data: UserData,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Store user data in database
    """
    try:
        # Prepare user document
        user_doc = {
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "created_at": datetime.utcnow()
        }
        
        # Insert into database
        result = await db.users.insert_one(user_doc)
        
        return {
            "status": "success",
            "message": "User data stored successfully",
            "_id": str(result.inserted_id)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to store user data: {str(e)}")

@router.get("/user")
async def get_user_data(
    user_id: str = None,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Get user data by ID
    """
    try:
        if not user_id:
            raise HTTPException(status_code=400, detail="User ID is required")
            
        try:
            # Find user in database
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
                
            return {
                "status": "success",
                "data": {
                    "_id": str(user["_id"]),
                    "first_name": user["first_name"],
                    "last_name": user["last_name"]
                }
            }
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=f"Failed to fetch user data: {str(e)}")
            
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"Failed to fetch user data: {str(e)}")
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"Failed to fetch user data: {str(e)}")
