import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.schemas.prompt_schema import System<PERSON>rom<PERSON>, PromptSettings
from unittest.mock import Mock, patch
from datetime import datetime

client = TestClient(app)

@pytest.fixture
def mock_db():
    return Mock()

@pytest.mark.asyncio
async def test_get_prompt_settings(mock_db):
    # Setup
    user_id = "test_user"
    mock_settings = {
        "prompts": {
            "classification": {
                "name": "classification",
                "prompt": "Classify this email",
                "system_prompt": "You are an expert classifier"
            }
        }
    }
    mock_db.prompt_settings.find_one.return_value = mock_settings
    
    # Execute & Assert
    response = await client.get("/configurations/prompts")
    assert response.status_code == 200
    assert "prompts" in response.json()["data"]

@pytest.mark.asyncio
async def test_update_prompt_settings(mock_db):
    # Setup
    settings = {
        "prompts": {
            "classification": {
                "name": "classification",
                "prompt": "New prompt",
                "system_prompt": "New system prompt"
            }
        }
    }
    
    # Execute
    response = await client.put("/configurations/prompts", json=settings)
    
    # Assert
    assert response.status_code == 200
    assert response.json()["message"] == "Prompt settings updated successfully"
