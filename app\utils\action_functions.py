import re
from typing import Dict, Any, Optional

def extract_customer_name(email_body: str) -> Optional[str]:
    """Extract customer name from email body using common patterns"""
    # Common name patterns
    patterns = [
        r"(?i)(?:I am|my name is|this is) ([A-Z][a-z]+ [A-Z][a-z]+)",  # "My name is <PERSON>"
        r"(?i)(?:sincerely|regards|from),?\s+([A-Z][a-z]+ [A-Z][a-z]+)", # "Regards, <PERSON>"
        r"(?i)^([A-Z][a-z]+ [A-Z][a-z]+)" # Name at start of email
    ]
    
    for pattern in patterns:
        match = re.search(pattern, email_body)
        if match:
            return match.group(1)
    return None

def create_quote_response(email_data: Dict[str, Any]) -> str:
    """Generate response for quote creation action"""
    print("hello world ------------------------->")
    customer_name = extract_customer_name(email_data.get("body", "")) or "valued customer"
    return f"Hi {customer_name}, your insurance quote request has been received and created successfully. Our team will review the details and get back to you shortly with the complete quote information."

def fetch_quote_docs_response(email_data: Dict[str, Any]) -> str:
    """Generate response for fetching quote documents action"""
    customer_name = extract_customer_name(email_data.get("body", "")) or "valued customer"
    return f"Hi {customer_name}, we have processed your request for quote documentation. The requested documents will be sent to your email address shortly. Please check your inbox in the next few minutes."
