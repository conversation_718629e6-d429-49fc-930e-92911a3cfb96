Stack trace:
Frame         Function      Args
0007FFFF74C0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF63C0) msys-2.0.dll+0x2118E
0007FFFF74C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF74C0  0002100469F2 (00021028DF99, 0007FFFF7378, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF74C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF74C0  00021006A545 (0007FFFF74D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF74D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE75980000 ntdll.dll
7FFE73DF0000 KERNEL32.DLL
7FFE72DA0000 KERNELBASE.dll
7FFE75420000 USER32.dll
7FFE72AD0000 win32u.dll
7FFE75230000 GDI32.dll
7FFE73230000 gdi32full.dll
7FFE73370000 msvcp_win.dll
7FFE72B90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE738A0000 advapi32.dll
7FFE73EC0000 msvcrt.dll
7FFE75370000 sechost.dll
7FFE73BA0000 RPCRT4.dll
7FFE720D0000 CRYPTBASE.DLL
7FFE73190000 bcryptPrimitives.dll
7FFE73D40000 IMM32.DLL
