from fastapi import FastAP<PERSON>
from starlette.requests import Request
from starlette.responses import Response
import uvicorn

app = FastAPI()

@app.api_route("/validate", methods=["GET", "POST", "OPTIONS"])
async def validate(request: Request):
    token = request.query_params.get("validationToken")
    print(f"Received validation request with token: {token}")
    if token:
        print(f"Responding with token: {token}")
        return Response(
            content=token,
            media_type="text/plain",
            headers={"Content-Type": "text/plain"}
        )
    else:
        print("No token found, returning empty response")
        # Await the body content
        body_content = await request.body()
        print(f"Received body content: {body_content}")
        return Response(
            content=body_content,
            media_type="text/plain",
            headers={"Content-Type": "text/plain"}
        )
    print("No token found, returning empty response")
    return Response()

# Add a simple route to test if the server is running
@app.get("/")
def read_root():
    return {"status": "Server is running"}

if __name__ == "__main__":
    # Run the server on port 8002 (different from your main app)
    print("Starting validation server on port 8002...")
    uvicorn.run(app, host="0.0.0.0", port=8002)