from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Body
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.utils.security import decode_jwt_token
from datetime import datetime, timedelta
from bson import ObjectId
import logging
import asyncio

router = APIRouter(prefix="/workflows", tags=["workflows"])

logger = logging.getLogger(__name__)


@router.post("/", status_code=201)
async def create_workflow(
    request: dict,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Create a new workflow and queue it for processing"""
    email_ids = [str(id) for id in request.get("email_ids", [])]
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        logger.info(f"Creating workflow for user {user_id} with email_ids: {email_ids}")
        
        # Get user's workflow configuration
        config = await db.configurations.find_one(
            {"user_id": user_id},
            projection={"email_responder.workflow": 1}
        )
        workflow_config = config.get("email_responder", {}).get("workflow", {
            "processing_type": "sequential",
            "processing_limit": 1
        })
        
        # Create workflow record
        workflow = {
            "user_id": user_id,
            "email_ids": [ObjectId(id) for id in email_ids],
            "status": "queued",
            "config": workflow_config,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await db.workflows.insert_one(workflow)
        workflow_id = str(result.inserted_id)
            
        # Update workflow status to queued with timestamp
        await db.workflows.update_one(
            {"_id": result.inserted_id},
            {
                "$set": {
                    "queued_at": datetime.utcnow(),
                    "processing_attempt": 0,
                    "next_attempt_at": datetime.utcnow()
                }
            }
        )

        # Update email statuses to inprocess
        await db.emails.update_many(
            {"_id": {"$in": [ObjectId(id) for id in email_ids]}},
            {
                "$set": {
                    "status": "inprocess",
                    "stage": "queued",
                    "updated_date": datetime.utcnow()
                }
            }
        )
        
        return {
            "status_code": 201,
            "status": "OK",
            "message": "Workflow created and processing started",
            "data": {
                "workflow_id": workflow_id,
                "status": "processing",
                "processing_type": workflow_config["processing_type"]
            }
        }
        
    except Exception as e:
        logger.error(f"Error creating workflow: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{workflow_id}")
async def get_workflow_status(
    workflow_id: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Get workflow status with optimized query"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        
        # Use find_one with maxTimeMS and specific projection
        workflow = await db.workflows.find_one(
            {"_id": ObjectId(workflow_id)},
            projection={"status": 1, "user_id": 1, "email_ids": 1, "results": 1},
            max_time_ms=2000  # 2 second timeout
        )
        if not workflow:
            raise HTTPException(status_code=404, detail="Workflow not found")
            
        if workflow["user_id"] != user_id:
            raise HTTPException(status_code=403, detail="Not authorized to access this workflow")
            
        return {
            "status_code": 200,
            "status": "OK", 
            "data": workflow
        }
        
    except Exception as e:
        logger.error(f"Error getting workflow status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
