import smtplib
import logging
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from fastapi import HTTPException

logger = logging.getLogger(__name__)

class EmailUtils:
    """Utility class for sending emails using SMTP"""
    
    @staticmethod
    def send_email(
        recipient_email: str,
        subject: str,
        body: str,
        sender_email: Optional[str] = None,
        sender_password: Optional[str] = None,
        smtp_server: Optional[str] = None,
        smtp_port: Optional[int] = None,
        use_ssl: bool = True
    ) -> dict:
        """
        Send an email using SMTP
        
        Args:
            recipient_email: Email address of the recipient
            subject: Email subject
            body: Email body content
            sender_email: Sender's email address (defaults to env variable)
            sender_password: Sender's email password (defaults to env variable)
            smtp_server: SMTP server address (defaults to Gmail)
            smtp_port: SMTP server port (defaults to 465 for SSL, 587 for TLS)
            use_ssl: Whether to use SSL (True) or TLS (False)
            
        Returns:
            Dictionary with status and message
            
        Raises:
            HTTPException: If email sending fails
        """
        try:
            # Use provided values or fallback to environment variables or defaults
            sender = sender_email or os.getenv("SMTP_EMAIL", "<EMAIL>")
            password = sender_password or os.getenv("SMTP_PASSWORD", "yourpassword")
            server = smtp_server or os.getenv("SMTP_SERVER", "smtp.gmail.com")
            port = smtp_port or (465 if use_ssl else 587)
            
            logger.info(f"Attempting to send email to {recipient_email} via {server}:{port}")
            
            # Create message
            msg = MIMEMultipart()
            msg["From"] = sender
            msg["To"] = recipient_email
            msg["Subject"] = subject
            
            # Attach body to email
            msg.attach(MIMEText(body, "plain"))
            
            # Send email
            if use_ssl:
                # Use SSL connection
                with smtplib.SMTP_SSL(server, port) as smtp_server:
                    smtp_server.login(sender, password)
                    smtp_server.sendmail(sender, recipient_email, msg.as_string())
            else:
                # Use TLS connection
                with smtplib.SMTP(server, port) as smtp_server:
                    smtp_server.starttls()
                    smtp_server.login(sender, password)
                    smtp_server.sendmail(sender, recipient_email, msg.as_string())
            
            logger.info(f"Email sent successfully to {recipient_email}")
            return {
                "status": "success",
                "message": f"Email sent successfully to {recipient_email}"
            }
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP authentication failed: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail="Email authentication failed. Please check email credentials."
            )
        except smtplib.SMTPRecipientsRefused as e:
            logger.error(f"Recipient refused: {str(e)}")
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid recipient email address: {recipient_email}"
            )
        except smtplib.SMTPServerDisconnected as e:
            logger.error(f"SMTP server disconnected: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail="Email server connection failed. Please try again later."
            )
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to send email: {str(e)}"
            )
    
    @staticmethod
    def send_password_reset_email(recipient_email: str, reset_token: str, reset_url: Optional[str] = None) -> dict:
        """
        Send a password reset email with token
        
        Args:
            recipient_email: Email address to send reset link to
            reset_token: Password reset token
            reset_url: Base URL for password reset (defaults to env variable)
            
        Returns:
            Dictionary with status and message
        """
        try:
            # Create reset URL
            base_url = reset_url or os.getenv("FRONTEND_URL", "http://localhost:3000")
            reset_link = f"{base_url}/reset-password?token={reset_token}"
            
            # Email subject and body
            subject = "Password Reset Request"
            body = f"""
Hello,

You have requested to reset your password. Please click the link below to reset your password:

{reset_link}

This link will expire in 1 hour for security reasons.

If you did not request this password reset, please ignore this email.

Best regards,
Email Responder Team
            """.strip()
            
            return EmailUtils.send_email(
                recipient_email=recipient_email,
                subject=subject,
                body=body
            )
            
        except Exception as e:
            logger.error(f"Error sending password reset email: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to send password reset email: {str(e)}"
            )

    @staticmethod
    def send_password_reset_otp_email(recipient_email: str, otp: str) -> dict:
        """
        Send a password reset email with 6-digit OTP

        Args:
            recipient_email: Email address to send OTP to
            otp: 6-digit OTP code

        Returns:
            Dictionary with status and message
        """
        try:
            # Email subject and body
            subject = "Password Reset OTP"
            body = f"""
Hello,

You have requested to reset your password. Please use the following 6-digit OTP to reset your password:

OTP: {otp}

This OTP will expire in 10 minutes for security reasons.

If you did not request this password reset, please ignore this email.

Best regards,
Email Responder Team
            """.strip()

            return EmailUtils.send_email(
                recipient_email=recipient_email,
                subject=subject,
                body=body
            )

        except Exception as e:
            logger.error(f"Error sending password reset OTP email: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to send password reset OTP email: {str(e)}"
            )
