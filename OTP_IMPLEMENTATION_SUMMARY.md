# OTP-Based Password Reset Implementation

## Overview
Successfully updated the email-responder-backend to use 6-digit OTP (One-Time Password) instead of tokens for password reset functionality.

## Changes Made

### 1. Schema Updates (`app/schemas/user_schema.py`)
- Modified `ResetPasswordRequest` to use `otp` field instead of `token`
- Added validation for 6-digit numeric OTP: `constr(min_length=6, max_length=6, pattern=r'^\d{6}$')`

### 2. Security Utilities (`app/utils/security.py`)
- Added `generate_otp()` function that creates secure 6-digit OTP codes
- Uses `secrets.randbelow(1000000)` for cryptographically secure random generation

### 3. Email Utilities (`app/utils/email_utils.py`)
- Added `send_password_reset_otp_email()` method
- Sends OTP directly in email body instead of reset link
- OTP expires in 10 minutes (configurable)

### 4. User Service (`app/services/user_service.py`)
- Updated `forget_password()` method:
  - Generates 6-digit OTP instead of 32-character token
  - Stores `reset_otp` and `reset_otp_expires` in database
  - Reduced expiration time from 1 hour to 10 minutes
- Updated `reset_password()` method:
  - Validates OTP instead of token
  - Checks OTP format and expiration
  - Cleans up OTP fields after successful reset

### 5. Controller Updates (`app/controllers/user_controller.py`)
- Updated method signatures to use `otp` parameter instead of `token`
- Updated documentation and error messages

### 6. Route Updates (`app/routes/user_routes.py`)
- Updated API documentation to reflect OTP usage
- Modified route handlers to pass OTP to controller methods

## API Usage Examples

### Forget Password Request
```bash
POST /users/forget-password
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

**Response:**
```json
{
    "status_code": 200,
    "status": "OK",
    "message": "If the email exists in our system, a password reset OTP has been sent."
}
```

### Reset Password Request
```bash
POST /users/reset-password
Content-Type: application/json

{
    "email": "<EMAIL>",
    "otp": "123456",
    "new_password": "NewSecurePassword123!"
}
```

**Response:**
```json
{
    "status_code": 200,
    "status": "OK",
    "message": "Password has been reset successfully"
}
```

## Database Changes

### Before (Token-based)
```javascript
{
    "_id": ObjectId("..."),
    "username": "user123",
    "email_config": {...},
    "reset_token": "abc123def456...", // 32-character token
    "reset_token_expires": ISODate("2024-01-01T13:00:00Z") // 1 hour expiry
}
```

### After (OTP-based)
```javascript
{
    "_id": ObjectId("..."),
    "username": "user123",
    "email_config": {...},
    "reset_otp": "123456", // 6-digit OTP
    "reset_otp_expires": ISODate("2024-01-01T12:10:00Z") // 10 minutes expiry
}
```

## Security Improvements

1. **Shorter Expiration**: OTP expires in 10 minutes vs 1 hour for tokens
2. **User-Friendly**: 6-digit codes are easier to type than long tokens
3. **Secure Generation**: Uses cryptographically secure random number generation
4. **Input Validation**: Strict validation ensures only 6-digit numeric codes are accepted

## Email Template

The OTP email includes:
- Clear subject: "Password Reset OTP"
- 6-digit OTP prominently displayed
- 10-minute expiration notice
- Security warning about ignoring if not requested

## Testing

Created comprehensive tests in `test_otp_functionality.py`:
- ✅ OTP generation validation
- ✅ Format verification (6 digits, numeric only)
- ✅ Database integration testing
- ✅ Complete workflow testing

## Backward Compatibility

⚠️ **Breaking Change**: This implementation is not backward compatible with the previous token-based system. Existing reset tokens in the database will not work with the new OTP system.

## Migration Notes

If you have existing users with pending reset tokens:
1. Clear existing `reset_token` and `reset_token_expires` fields
2. Users will need to request new password resets to get OTP codes
3. Update any frontend applications to use the new OTP field instead of token

## Configuration

Environment variables that may need updating:
- Email templates and subjects
- OTP expiration time (currently hardcoded to 10 minutes)
- SMTP settings for sending OTP emails
