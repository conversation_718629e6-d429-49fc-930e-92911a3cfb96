```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant BE as Backend
    participant G as Google Auth
    participant GM as Gmail API
    participant PS as Pub/Sub

    Note over BE: Initial Setup
    Note over BE: Load GOOGLE_APPLICATION_CREDENTIALS
    Note over BE: Service account JSON used for<br/>Pub/Sub & Gmail API access

    U->>FE: Click "Connect Gmail"
    FE->>BE: Request Gmail auth URL
    BE->>G: Request authorization URL
    G-->>BE: Return auth URL
    BE-->>FE: Return auth URL
    FE->>G: Redirect to Google login
    U->>G: Login & authorize
    G->>FE: Redirect with auth code
    FE->>BE: Send auth code
    BE->>G: Exchange code for tokens
    G-->>BE: Return access & refresh tokens
    BE->>BE: Store tokens in DB

    Note over BE,PS: Push Notification Setup
    BE->>PS: Create/Verify Pub/Sub topic
    BE->>GM: Setup Gmail push notifications<br/>using service account
    GM-->>PS: Configure notification delivery
    PS-->>BE: Confirm topic setup
    GM-->>BE: Confirm subscription
    BE-->>FE: Auth success response
    FE-->>U: Show success message

    Note over BE,G: Token Refresh Flow
    BE->>BE: Check token expiry
    BE->>BE: get_valid_token()
    alt Token expired
        BE->>G: Request new token using refresh token
        G-->>BE: Return new access token
        BE->>BE: Update token in DB
    end
```

# Gmail OAuth Flow Explanation

1. **Initial Backend Setup**
   - Backend loads GOOGLE_APPLICATION_CREDENTIALS
   - Service account JSON required for:
     - Pub/Sub topic management
     - Gmail API watch notifications
     - Secure server-to-server auth

2. **Initial Request**
   - User clicks "Connect Gmail" button
   - Frontend requests auth URL from backend
   - Backend generates state token for security

3. **Google Authorization**
   - User is redirected to Google login
   - User authenticates and grants permissions
   - Google redirects back with auth code

4. **Token Exchange**
   - Backend exchanges auth code for tokens
   - Access and refresh tokens are stored securely

5. **Push Notification Setup**
   - Backend uses service account to:
     - Create/verify Pub/Sub topic
     - Configure Gmail API notifications
     - Setup secure notification delivery
   - Push notification subscription activated

6. **Completion**
   - Success message shown to user
   - Gmail integration is now active
