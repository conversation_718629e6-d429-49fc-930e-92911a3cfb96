import json
import logging
import asyncio
from datetime import datetime
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON><PERSON>, Query, HTTPException, Request
from app.startup.subscription_monitor import start_subscription_monitor
from app.services.email_service import EmailService
from fastapi.responses import PlainTextResponse
from app.schemas.user_schema import UserDetailsResponse
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.controllers.email_controller import EmailController
from app.controllers.user_controller import UserController
from app.schemas.email_schema import EmailResponse, CategoryCreate
from app.utils.security import decode_jwt_token
from bson import ObjectId
from app.schemas.email_schema import EmailReply
from app.utils.debug_utils import log_exception, log_api_request

router = APIRouter(tags=["emails"])
logger = logging.getLogger(__name__)

import os

# Initialize IMAP IDLE worker
imap_worker = None

@router.on_event("startup")
async def startup_event():
    """Initialize services on application startup"""
    global imap_worker
    
    monitor_subscription = os.environ.get('MOINTER_SUBSCRIPTION', 'false').lower() == 'true'
    
    if monitor_subscription:
        # Start subscription monitor if MOINTER_SUBSCRIPTION is true
        logger.info("Starting subscription monitor")
        await start_subscription_monitor()
    else:
        # Initialize IMAP IDLE worker if MOINTER_SUBSCRIPTION is false
        logger.info("Starting IMAP IDLE worker")
        from app.workers.imap_idle_worker import ImapIdleWorker
        imap_worker = ImapIdleWorker()
        await imap_worker.start()

@router.on_event("shutdown")
async def shutdown_event():
    """Cleanup services on application shutdown"""
    global imap_worker
    
    # Stop IMAP IDLE worker if it was started
    if imap_worker:
        logger.info("Stopping IMAP IDLE worker")
        await imap_worker.stop()


from urllib.parse import urlparse
from fastapi import Query

@router.post("/emails/subscription/gmail", 
    description="Create Gmail push notification subscription",
    response_model=dict,
    responses={
        401: {"description": "Unauthorized"},
        400: {"description": "Invalid notification URL"},
        500: {"description": "Internal server error"}
    }
)
async def create_gmail_subscription(
    # notification_url: str = Query(..., description="Full notification URL including path"),
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Create subscription for Gmail push notifications
    
    Args:
        notification_url: Full notification URL (e.g., https://example.com/emails/notifications/gmail)
        
    Returns:
        dict: Subscription details including ID and expiration
    """
    # Generate a unique request ID for tracking this request through logs
    request_id = datetime.now().strftime("%Y%m%d%H%M%S")
    logger.info(f"Request ID: {request_id} - Processing Gmail subscription creation request")
    
    try:
        # Log request details
        log_api_request({"request_id": request_id, "endpoint": "/emails/subscription/gmail"})
        
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Validate notification URL
        # logger.info("Validating notification URL")
        # parsed_url = urlparse(notification_url)
        # if not all([parsed_url.scheme, parsed_url.netloc]):
        #     logger.error(f"Invalid URL format: {notification_url}")
        #     raise HTTPException(status_code=400, detail="Invalid notification URL format")
            
        # if parsed_url.scheme.lower() != "https":
        #     logger.error("Non-HTTPS URL provided")
        #     raise HTTPException(status_code=400, detail="Notification URL must use HTTPS")

        # logger.info(f"Parsed URL - Host: {parsed_url.netloc}, Path: {parsed_url.path}")
        
        # Extract user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Processing for user ID: {user_id}")
        
        # Create Gmail notification service
        from app.services.gmail_notification_service import GmailNotificationService
        notification_service = GmailNotificationService(db)
        
        try:
            # First check if the token is already marked as expired
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if user and "gmail_config" in user.get("email_config", {}) and user["email_config"]["gmail_config"].get("token_status") == "expired":
                logger.warning("Token is already marked as expired, skipping subscription attempt")
                return {
                    "status": "error",
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": "Gmail authentication required",
                    "detail": "Your Gmail authorization has expired. Please re-authenticate with Gmail.",
                    "auth_url": f"/api/auth/gmail?user_id={user_id}"
                }
            
            result = await notification_service.create_subscription(user_id)
            
            # Check if result is a dictionary with a status field
            if isinstance(result, dict) and "status" in result:
                if result.get("status") == "error":
                    # Add auth_url to the error response
                    result["auth_url"] = f"/api/auth/gmail?user_id={user_id}"
                return result  # Return the result directly as it already has status
            
            # If result is not a dictionary with status (legacy format), wrap it
            return {
                "status": "success",
                "message": "Gmail subscription created successfully",
                "data": result
            }
        except HTTPException as he:
            if he.status_code == 401 and "re-authenticate" in he.detail:
                # Return a more user-friendly error for expired tokens
                return {
                    "status": "error",
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": "Gmail authentication required",
                    "detail": he.detail,
                    "auth_url": f"/api/auth/gmail?user_id={user_id}"
                }
            raise
        
    except HTTPException as he:
        # Check if this is an authentication error
        if he.status_code == 401 and "re-authenticate" in he.detail:
            logger.warning(f"Request ID: {request_id} - Authentication error: {he.detail}")
            return {
                "status": "error",
                "code": "AUTHENTICATION_REQUIRED",
                "message": "Gmail authentication required",
                "detail": he.detail,
                "auth_url": f"/api/auth/gmail?user_id={user_id}",
                "request_id": request_id
            }
        # Log the HTTP exception
        logger.error(f"Request ID: {request_id} - HTTP Exception: {he.status_code} - {he.detail}")
        raise he
    except Exception as e:
        # Use our exception logging utility
        error_response = log_exception(e, f"Request ID: {request_id} - Error creating Gmail subscription", user_id)
        
        # Add auth_url to the response
        error_response["auth_url"] = f"/api/auth/gmail?user_id={user_id}"
        
        # Check if the error message contains invalid_grant
        error_message = str(e)
        if "invalid_grant" in error_message.lower():
            error_response["code"] = "AUTHENTICATION_REQUIRED"
            error_response["message"] = "Gmail authentication required"
            error_response["detail"] = "Your Gmail authorization has expired. Please re-authenticate with Gmail."
            return error_response
        
        # For other errors, return a structured error response
        return {
            "status": "error",
            "code": "SUBSCRIPTION_ERROR",
            "message": "Failed to create subscription",
            "detail": f"Failed to create subscription: {error_message}",
            "auth_url": f"/api/auth/gmail?user_id={user_id}",
            "request_id": request_id
        }

@router.post("/emails/subscription/outlook", 
    description="Create Outlook email notification subscription",
    response_model=dict,
    responses={
        401: {"description": "Unauthorized"},
        400: {"description": "Invalid notification URL"},
        500: {"description": "Internal server error"}
    }
)
async def create_subscription(
    notification_url: str = Query(..., description="Full notification URL including path"),
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Create subscription for email notifications
    
    Args:
        notification_url: Full notification URL (e.g., https://example.com/emails/notifications/outlook)
        
    Returns:
        dict: Subscription details including ID and expiration
    """
    try:
        # notification_url = "https://email-ai-test.comorin.co/api/email-responder/emails/notifications/outlook"
        logger.info("Processing subscription creation request")
        logger.debug(f"Notification URL provided: {notification_url}")
        
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Validate notification URL
        logger.info("Validating notification URL")
        parsed_url = urlparse(notification_url)
        if not all([parsed_url.scheme, parsed_url.netloc]):
            logger.error(f"Invalid URL format: {notification_url}")
            raise HTTPException(status_code=400, detail="Invalid notification URL format")
            
        if parsed_url.scheme.lower() != "https":
            logger.error("Non-HTTPS URL provided")
            raise HTTPException(status_code=400, detail="Notification URL must use HTTPS")

        logger.info(f"Parsed URL - Host: {parsed_url.netloc}, Path: {parsed_url.path}")
        
        # Use the full URL as provided
        logger.debug(f"Using notification URL: {notification_url}")
        
        logger.debug("Extracting user ID from token")
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Processing for user ID: {user_id}")
        
        from app.services.subscription_manager import SubscriptionManager
        subscription_manager = SubscriptionManager(db)
        
        result = await subscription_manager.setup_subscription(user_id, notification_url)
        
        return {
            "status": "success",
            "message": "Subscription created successfully",
            "data": result
        }
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create subscription: {str(e)}")

@router.delete("/emails/subscription/gmail",
    description="Delete Gmail push notification subscription",
    responses={
        200: {"description": "Subscription deleted successfully"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def delete_gmail_subscription(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Delete Gmail push notification subscription
    
    Returns:
        dict: Status of subscription deletion
    """
    try:
        logger.info("Processing Gmail subscription deletion request")
        
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Get user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Processing for user ID: {user_id}")
        
        # Create Gmail notification service
        from app.services.gmail_notification_service import GmailNotificationService
        notification_service = GmailNotificationService(db)
        
        result = await notification_service.delete_subscription(user_id)
        
        return result
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error deleting Gmail subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete subscription: {str(e)}")

# 
# @router.get("/emails/notifications/outlook", description="Handle Outlook subscription validation")

@router.post("/emails/notifications/gmail", description="Handle Gmail push notifications")
async def handle_gmail_notification(
    request: Request,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """Handle incoming notifications from Gmail via Cloud Pub/Sub"""
    try:
        notification_data = await request.json()
        logger.info("Received Gmail notification")
        logger.debug(f"Notification data: {notification_data}")

        from app.services.gmail_notification_service import GmailNotificationService
        notification_service = GmailNotificationService(db)
        await notification_service.handle_notification(notification_data)

        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error processing Gmail notification: {str(e)}")
        return {"status": "error", "detail": str(e)}

@router.post("/gmail/webhook", description="Handle Gmail webhook notifications")
async def gmail_webhook(
    request: Request,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Handle incoming webhook notifications from Gmail
    
    This endpoint receives notifications when new emails arrive in Gmail
    """
    try:
        print("*********************Received Gmail webhook notification****************")
        # Log the incoming request
        logger.info("Received Gmail webhook notification")
        
        # Try to parse the request body as JSON
        try:
            data = await request.json()
            print("********data*********",data)
            logger.debug(f"Webhook data (JSON): {data}")
        except Exception:
            # If not JSON, try to get the raw body
            body = await request.body()
            logger.debug(f"Webhook data (raw): {body}")
            data = {"raw_body": str(body)}
        
        # Extract headers for debugging
        headers = dict(request.headers)
        logger.debug(f"Webhook headers: {headers}")
        
        # Process the notification
        from app.services.gmail_service import GmailService
        gmail_service = GmailService()
        
        # Extract the data from the notification
        try:
            import base64
            data_string = data.get("message", {}).get("data")
            if not data_string:
                logger.error("Missing data in webhook notification")
                return {"status": "error", "detail": "Missing data in webhook notification"}
            
            # Decode the base64 encoded string
            decoded_bytes = base64.b64decode(data_string)
            decoded_string = decoded_bytes.decode("utf-8")
            
            # Parse the decoded string as JSON
            decoded_data = json.loads(decoded_string)
            
            # Extract the email address from the decoded data
            email_address = decoded_data.get("emailAddress")
            history_id = decoded_data.get("historyId")
            
            if not email_address:
                logger.error("Missing emailAddress in webhook notification")
                return {"status": "error", "detail": "Missing emailAddress in webhook notification"}
            
            logger.info(f"Extracted email address: {email_address}")
            
        except Exception as decode_error:
            logger.error(f"Error decoding webhook  {str(decode_error)}")
            return {"status": "error", "detail": f"Error decoding webhook  {str(decode_error)}"}
        
        # Find the user associated with this email address
        # user = await db.users.find_one({"email_config.gmail_config.email": email_address})
        user = await db.users.find_one(
            {"email_config.gmail_config.email": email_address},
            sort=[("_id", -1)]  # Sort by _id in descending order (newest first)
        )      
        
        if not user:
            logger.warning(f"No user found with email address: {email_address}")
            return {"status": "success", "message": f"No user found with email address: {email_address}"}
        
        user_id = str(user["_id"])
        logger.info(f"Processing webhook for user {user_id} ({email_address})")
        
        try:
            # Get the latest emails for this user
            await gmail_service.fetch_recent_emails(db, user_id)
        except Exception as user_error:
            logger.error(f"Error processing webhook for user {user_id}: {str(user_error)}")
        
        return {"status": "success", "message": "Webhook processed successfully"}
    except Exception as e:
        logger.error(f"Error processing Gmail webhook: {str(e)}")
        # Return 200 OK even on error to prevent Gmail from retrying
        return {"status": "error", "detail": str(e)}
@router.post("/emails/notifications/outlook", description="Handle Outlook email notifications")
async def handle_outlook_notification(
    request: Request,
    validationToken: Optional[str] = None,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Handle incoming notifications and validation from Microsoft Graph API
    """
    print("Received request to notifications endpoint")
    logger.info("Received request to notifications endpoint")
    logger.debug(f"Method: {request.method}")
    logger.debug(f"Query params: {request.query_params}")
    
    # Check for validation token in query parameters or as a direct parameter
    # Microsoft sends this as a URL-encoded parameter
    validation_token = validationToken or request.query_params.get('validationToken')
    print("validation_token====>", validation_token)
    if validation_token:
        print("yes validation_token")
        logger.info(f"Handling validation request with token: {validation_token}")
        # Return the validation token as plain text with 200 OK
        return PlainTextResponse(
            content=validation_token,
            status_code=200,
            headers={"Content-Type": "text/plain"}
        )

    # For validation requests, we've already handled them above
    # For actual notifications, handle them directly
    print("above if----------------")
    if request.method == "POST" and not validation_token:
        print("inside if-------------")
        try:
            # Process notification directly
            print("Processing notification----")
            notification = await request.json()
            print("notification----", notification)
            logger.info("Processing notification")
            logger.debug(f"Notification data: {notification}")
            
            from app.services.notification_service import NotificationService
            notification_service = NotificationService(db)
            await notification_service.handle_notification(notification)
            
            logger.info("Notification processed successfully")
            return PlainTextResponse(content="Accepted", status_code=202)
        except Exception as e:
            logger.error(f"Error processing notification: {str(e)}")
            # Still return 202 to acknowledge receipt
            return PlainTextResponse(content="Accepted", status_code=202)
    
    # Return 200 OK for any other request to prevent validation failures
    return PlainTextResponse(content="OK", status_code=200)



@router.get("/emails",  
            description="Fetch emails with pagination support")
async def fetch_emails(
    page: int = Query(1, description="Page number", ge=1),
    page_size: int = Query(10, description="Number of emails per page", ge=1, le=100),
    refresh: bool = Query(False, description="Force refresh from email server"),
    status: Optional[str] = Query(None, description="Filter emails by status (unprocessed/processed)"),
    source: Optional[str] = Query(None, description="Filter emails by source (Gmail/Outlook)"),
    category: Optional[str] = Query(None, description="Filter by Classification Specialist output"),
    sentiment: Optional[str] = Query(None, description="Filter by Sentiment Analysis Specialist output"),
    urgency: Optional[str] = Query(None, description="Filter by Urgency Type Specialist output"),
    search: Optional[str] = Query(None, description="Search in email subject, body, or sender"),
    exclude: Optional[str] = Query(None, description="Exclude emails from specific senders (comma-separated)"),
    conversation_id: Optional[str] = Query(None, description="Filter by conversation ID"),
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None),
    user_id: Optional[str] = Header(None, alias="user-id")

):
    print("status-o1", status)
    """
    Fetch emails from the user's email account with pagination support.
    
    Args:
        page: Page number (starts from 1)
        page_size: Number of emails per page (1-100)
        
    Returns:
        List of emails with pagination metadata
    """
    print("refresh: ", refresh, user_id)
    if not authorization and not user_id:
        raise HTTPException(status_code=401, detail="Authorization header 1 missing")
    if not authorization:
        token = user_id
    else:
        token = authorization.replace("Bearer ", "")

    response = await EmailController.fetch_emails(
        db, token, page, page_size,user_id, refresh, status,source,
        category=category,
        sentiment=sentiment,
        urgency=urgency,
        search=search,
        exclude=exclude,
        conversation_id=conversation_id
        
    )
    return response

@router.post("/emails/category", 
            description="Add a new category and update email category")
async def add_category(
    category: CategoryCreate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Add a new category to configurations and update email category
    
    Args:
        category: Category details including name, description, sample_email and email_id
        
    Returns:
        Updated category information
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    try:
        # Get user's configuration
        config = await db.configurations.find_one({"user_id": user_id})
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        # Check if category name already exists
        existing_categories = config.get("email_responder", {}).get("categories", [])
        if any(cat["name"].lower() == category.name.lower() for cat in existing_categories):
            raise HTTPException(status_code=400, detail="Category name already exists")
        
        # Add new category
        new_category = {
            "name": category.name,
            "description": category.description,
            "sample_email": category.sample_email
        }
        
        # Update configuration with new category
        result = await db.configurations.update_one(
            {"user_id": user_id},
            {"$push": {"email_responder.categories": new_category}}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to add category")
        
        # Update email with new category
        email_result = await db.emails.update_one(
            {"_id": ObjectId(category.email_id), "user_id": user_id},
            {"$set": {"category": category.name}}
        )
        
        if email_result.modified_count == 0:
            raise HTTPException(status_code=404, detail="Email not found or update failed")
        
        return {
            "status_code": 201,
            "status": "success",
            "message": "Category added and email updated successfully",
            "data": new_category
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add category: {str(e)}")
@router.post("/emails/reply", description="Send or save as draft an email reply")
async def send_reply(
     reply_data: EmailReply,
     db: AsyncIOMotorDatabase = Depends(get_db),
     
 ):
     """
     Send an email reply or save it as draft

     Args:
         reply_data: Contains email_id, body and is_draft flag

     Returns:
         Response status and message
     """
    #  if not authorization:
    #      raise HTTPException(status_code=401, detail="Authorization header missing")

    #  token = authorization.replace("Bearer ", "")
     try:
         return await EmailController.send_reply(db, reply_data)
     except Exception as e:
         print(f"Detailed error: {str(e)}")
         raise

@router.post("/emails/test-notification", description="Test socket notification for a specific user")
async def test_notification(
    user_id: str,
    email_data: dict = None,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Test endpoint to simulate a new email notification for a specific user
    
    Args:
        user_id: The user ID to send the notification to
        email_data: Optional email data to use (will generate sample if not provided)
        
    Returns:
        Status of the notification and connection instructions
    """
    try:
        logger.info(f"Testing notification for user: {user_id}")
        
        # Create sample email data if not provided
        if not email_data:
            current_time = datetime.utcnow().isoformat()
            email_data = {
                "_id": f"test_{current_time}",
                "message_id": f"test_msg_{current_time}",
                "subject": "Test Notification Email",
                "sender": "<EMAIL>",
                "recipient": "<EMAIL>",
                "date": current_time,
                "fetched_date": current_time,
                "body": "This is a test email notification sent via the test endpoint.",
                "is_read": False,
                "folder": "INBOX",
                "status": "unprocessed",
                "user_id": user_id
            }
        
        # Send notification via Socket.IO
        from app.websocket.connection_manager import manager
        await manager.send_to_user(
            user_id,
            'new_email',
            email_data
        )
        
        logger.info(f"Test notification sent to user {user_id}")
        return {
            "status": "success",
            "message": f"Test notification sent to user {user_id}",
            "active_connections": len(manager.user_to_sids.get(user_id, []))
            if user_id in manager.user_to_sids else 0,
            "connection_instructions": {
                "websocket_url": f"ws://localhost:8001/?EIO=4&transport=websocket&user_id={user_id}",
                "note": "Connect to this URL to automatically authenticate with the user_id"
            }
        }
        
    except Exception as e:
        logger.error(f"Error sending test notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send test notification: {str(e)}")

# This is a duplicate route that was removed

@router.get("/emails/subscription/gmail/status",
    description="Check Gmail subscription status",
    responses={
        200: {"description": "Subscription status"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def check_gmail_subscription_status(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Check Gmail subscription status
    
    Returns:
        dict: Status and details of the Gmail subscription
    """
    try:
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Get user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Checking Gmail subscription status for user ID: {user_id}")
        
        # Check if user has Gmail configuration
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        if not user or "gmail_config" not in user.get("email_config", {}):
            return {
                "status": "error",
                "code": "CONFIGURATION_ERROR",
                "message": "Gmail configuration not found",
                "detail": "User does not have Gmail configuration. Please authenticate with Gmail first."
            }
        
        # Check if subscription exists
        subscription = await db.gmail_subscriptions.find_one({"user_id": user_id})
        
        # Get Gmail configuration status
        gmail_config = user["email_config"]["gmail_config"]
        
        response = {
            "status": "success",
            "user_gmail_status": {
                "email": gmail_config.get("email"),
                "has_access_token": "access_token" in gmail_config,
                "has_refresh_token": "refresh_token" in gmail_config,
                "token_status": gmail_config.get("token_status", "unknown")
            },
            "subscription": subscription if subscription else None,
            "has_active_subscription": subscription is not None
        }
        
        return response
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error checking Gmail subscription status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check Gmail subscription status: {str(e)}")

@router.get("/emails/subscription/gmail/check-setup",
    description="Check Google Cloud setup for Gmail notifications",
    responses={
        200: {"description": "Setup check results"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def check_gmail_setup(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Check Google Cloud setup for Gmail notifications
    
    Returns:
        dict: Status and details of the Google Cloud setup
    """
    try:
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Get user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Checking Gmail setup for user ID: {user_id}")
        
        # Check if user has Gmail configuration
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        if not user or "gmail_config" not in user.get("email_config", {}):
            return {
                "status": "error",
                "code": "CONFIGURATION_ERROR",
                "message": "Gmail configuration not found",
                "detail": "User does not have Gmail configuration. Please authenticate with Gmail first."
            }
        
        # Check Google Cloud setup
        from app.utils.debug_utils import check_google_cloud_setup
        setup_status = check_google_cloud_setup()
        
        # Add user's Gmail configuration status
        gmail_config = user["email_config"]["gmail_config"]
        setup_status["user_gmail_status"] = {
            "email": gmail_config.get("email"),
            "has_access_token": "access_token" in gmail_config,
            "has_refresh_token": "refresh_token" in gmail_config,
            "token_status": gmail_config.get("token_status", "unknown")
        }
        
        return setup_status
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error checking Gmail setup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check Gmail setup: {str(e)}")

@router.put("/emails/{email_id}/metadata",
    description="Update email document with additional metadata",
    responses={
        200: {"description": "Email metadata updated successfully"},
        401: {"description": "Unauthorized"},
        404: {"description": "Email not found"},
        500: {"description": "Internal server error"}
    }
)
async def update_email_metadata(
    email_id: str,
    metadata: dict,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Update email document with additional metadata
    
    Args:
        email_id: ID of the email to update
        metadata: Dictionary containing key-value pairs to add/update
        
    Returns:
        dict: Updated email metadata
    """
    try:
        # Validate authorization
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
            
        # Get user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        
        # Update email document
        result = await db.emails.update_one(
            {"_id": ObjectId(email_id), "user_id": user_id},
            {"$set": metadata}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Email not found")
            
        if result.modified_count == 0:
            return {
                "status": "success",
                "message": "No changes were needed",
                "email_id": email_id
            }
            
        return {
            "status": "success",
            "message": "Email metadata updated successfully",
            "email_id": email_id,
            "updated_fields": metadata
        }
        
    except Exception as e:
        logger.error(f"Error updating email metadata: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update email metadata: {str(e)}")

@router.delete("/emails/subscription/outlook",
    description="Delete Outlook email notification subscription",
    responses={
        200: {"description": "Subscription deleted successfully"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def delete_outlook_subscription(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Delete subscription for email notifications
    
    Returns:
        dict: Status of subscription deletion
    """
    try:
        logger.info("Processing subscription deletion request")
        
        # Validate authorization
        if not authorization:
            logger.error("Missing authorization header")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        # Get user ID from token
        token = authorization.replace("Bearer ", "")
        user_id = decode_jwt_token(token)
        logger.info(f"Processing for user ID: {user_id}")
        
        from app.services.subscription_manager import SubscriptionManager
        subscription_manager = SubscriptionManager(db)
        
        result = await subscription_manager.remove_subscription(user_id)
        
        return result
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error deleting subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete subscription: {str(e)}")
