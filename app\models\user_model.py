from typing import Optional
from pydantic import BaseModel, Field
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, handler):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, **kwargs):
        field_schema.update(type="string")
        return field_schema

class UserInDB(BaseModel):
    id: Optional[PyObjectId] = Field(alias="_id")
    username: str
    full_name: str
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str},
        "from_attributes": True
    }

    def dict(self, *args, **kwargs):
        dict_repr = super().dict(*args, **kwargs)
        # Convert ObjectId to string and ensure _id is included
        if hasattr(self, 'id'):
            dict_repr["_id"] = str(self.id)
        return {k: v for k, v in dict_repr.items() if v is not None}
    
    def get_basic_details(self):
        return {
            "username": self.username,
            "full_name": self.full_name
        }
