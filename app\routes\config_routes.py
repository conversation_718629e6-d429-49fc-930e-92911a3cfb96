from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Header
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.controllers.config_controller import ConfigController
from app.schemas.config_schema import Configuration, ConfigurationCreate, ConfigurationUpdate
from app.schemas.prompt_schema import PromptSettingsUpdate

router = APIRouter(prefix="/configurations", tags=["configurations"])

@router.get("/", 
            description="Fetch user configuration details")
async def get_user_config(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)):
    """
    Retrieve all configurations including:
    - Email configurations (SMTP settings)
    - Prompts for language model interactions
    - Category management configurations
    
    Returns:
        Configuration object containing all settings
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    return await ConfigController.get_user_config(db, token)

@router.post("/", status_code=201,
             description="Create new configurations")
async def create_configurations(config: ConfigurationCreate,
                              db: AsyncIOMotorDatabase = Depends(get_db),
                              authorization: str = Header(None)):
    """
    Create new configurations including:
    - Email configurations (SMTP settings)
    - Prompts for language model interactions
    - Category management configurations
    
    Args:
        config: Configuration data including email, prompts and categories
        
    Returns:
        Created configuration object
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    # print("token", token)
    return await ConfigController.create_configurations(config, db, token)

@router.get("/prompts",
            description="Fetch prompt settings configuration")
async def get_prompt_settings(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)):
    """
    Retrieve prompt settings configuration including:
    - System prompts for different operations
    - Email adapter settings
    
    Returns:
        Prompt settings configuration object
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    return await ConfigController.get_prompt_settings(db, token)

@router.put("/prompts",
           description="Update prompt settings configuration")
async def update_prompt_settings(
    settings: PromptSettingsUpdate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)):
    """
    Update prompt settings configuration including:
    - System prompts for different operations
    - Email adapter settings
    
    Args:
        settings: Updated prompt settings data
        
    Returns:
        Updated prompt settings configuration
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    return await ConfigController.update_prompt_settings(settings, db, token)

@router.put("/", 
           description="Update user configuration")
async def update_configurations(
    config: ConfigurationUpdate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)):
    """
    Update existing configurations including:
    - Email configurations (SMTP settings)
    - Prompts for language model interactions
    - Category management configurations
    
    Args:
        config: Updated configuration data
        
    Returns:
        Updated configuration object
    """
    # print("config", config)
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    return await ConfigController.update_user_config(config, db, token)
