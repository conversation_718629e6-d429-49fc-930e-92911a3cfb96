import logging
from typing import List, Optional
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
from app.models.action_model import ActionCreate, ActionInDB
import httpx
import json
from string import Template

logger = logging.getLogger(__name__)

class ActionService:
    @staticmethod
    async def create_action(action: ActionCreate, user_id: str, db: AsyncIOMotorDatabase) -> ActionInDB:
        try:
            # Check if action name already exists for this user
            existing_action = await db.actions.find_one({
                "name": action.name,
                "user_id": user_id
            })
            
            if existing_action:
                raise HTTPException(
                    status_code=400,
                    detail=f"Action with name '{action.name}' already exists"
                )

            # Create action document
            action_dict = action.dict(exclude_none=True)
            action_dict["user_id"] = user_id
            
            # If id is provided, use it as _id
            if action.id:
                action_dict["_id"] = action.id
                del action_dict["id"]
            
            # Insert or update based on whether id exists
            if "id" in action_dict:
                result = await db.actions.update_one(
                    {"_id": action_dict["_id"]},
                    {"$set": action_dict},
                    upsert=True
                )
            else:
                result = await db.actions.insert_one(action_dict)
            
            # Return created/updated action
            created_action = await db.actions.find_one({"_id": action_dict.get("_id", result.inserted_id)})
            if created_action:
                # Convert ObjectId to string before passing to Pydantic model
                created_action["_id"] = str(created_action["_id"])
            return ActionInDB(**created_action)
            
        except Exception as e:
            logger.error(f"Error creating action: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create action: {str(e)}")

    @staticmethod
    async def get_actions(user_id: str, db: AsyncIOMotorDatabase) -> List[ActionInDB]:
        try:
            cursor = db.actions.find({"user_id": user_id})
            actions = await cursor.to_list(length=None)
            # Convert ObjectId to string for each action
            for action in actions:
                action["_id"] = str(action["_id"])
            return [ActionInDB(**action) for action in actions]
        except Exception as e:
            logger.error(f"Error fetching actions: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch actions: {str(e)}")

    @staticmethod
    async def update_action(action_id: str, action_update: ActionCreate, user_id: str, db: AsyncIOMotorDatabase) -> ActionInDB:
        try:
            # Verify action exists and belongs to user
            action = await db.actions.find_one({"_id": ObjectId(action_id), "user_id": user_id})
            if not action:
                raise HTTPException(status_code=404, detail="Action not found")

            # If name is being changed, check if new name already exists
            if action_update.name != action.get("name"):
                existing_action = await db.actions.find_one({
                    "name": action_update.name,
                    "user_id": user_id,
                    "_id": {"$ne": ObjectId(action_id)}  # Exclude current action
                })
                
                if existing_action:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Action with name '{action_update.name}' already exists"
                    )

            # Update action
            update_dict = action_update.dict()
            result = await db.actions.update_one(
                {"_id": ObjectId(action_id)},
                {"$set": update_dict}
            )

            if result.modified_count == 0:
                raise HTTPException(status_code=500, detail="Failed to update action")

            # Return updated action
            updated_action = await db.actions.find_one({"_id": ObjectId(action_id)})
            if updated_action:
                # Convert ObjectId to string before passing to Pydantic model
                updated_action["_id"] = str(updated_action["_id"])
            return ActionInDB(**updated_action)

        except Exception as e:
            logger.error(f"Error updating action: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update action: {str(e)}")

    @staticmethod
    async def delete_action(action_id: str, user_id: str, db: AsyncIOMotorDatabase) -> bool:
        try:
            # Verify action exists and belongs to user
            action = await db.actions.find_one({"_id": ObjectId(action_id), "user_id": user_id})
            if not action:
                raise HTTPException(status_code=404, detail="Action not found")

            # Delete action
            result = await db.actions.delete_one({"_id": ObjectId(action_id)})
            return result.deleted_count > 0

        except Exception as e:
            logger.error(f"Error deleting action: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to delete action: {str(e)}")

    @staticmethod
    async def execute_action(action_name: str, email_id: str, user_id: str, db: AsyncIOMotorDatabase, custom_payload: dict = None):
        print(action_name, email_id, user_id)
        try:
            logger.info(f"Starting action execution - Action Name: {action_name}, Email ID: {email_id}, User ID: {user_id}")

            # Initialize variables for payload data
            action_payload = None
            extracted_data = None

            if custom_payload:
                # Use custom payload if provided
                logger.info("Using custom payload for action execution")
                extracted_data = custom_payload
            else:
                # Fetch email details with action_payloads
                logger.info("Fetching payload from database")
                email = await db.emails.find_one({"_id": ObjectId(email_id)})
                if not email:
                    logger.error(f"Email not found - Email ID: {email_id}")
                    raise HTTPException(status_code=404, detail="Email not found")

                # Get action payload data for the specific action
                action_payload = email.get("action_payloads", {}).get(action_name)
                if not action_payload:
                    logger.error(f"Action payload not found for action: {action_name}")
                    raise HTTPException(status_code=404, detail=f"Action payload not found for {action_name}")
                
                # Get extracted data from action payload
                extracted_data = action_payload.get("extracted_data", {})

            # Fetch action details from actions collection
            action = await db.actions.find_one({"name": action_name, "user_id": user_id})
            if not action:
                logger.error(f"Action not found - Action Name: {action_name}")
                raise HTTPException(status_code=404, detail="Action configuration not found")

            logger.info(f"Action found: {action['name']}")
            logger.debug(f"Full action config: {action}")

            # Prepare request parameters
            query_params = {}
            body_params = {}
            headers = {}

            print("extracted_data", extracted_data)
            
            # Process parameters based on their location
            if action.get("parameters"):
                for param in action["parameters"]:
                    param_name = param["name"]
                    
                    # Handle nested parameters
                    if "." in param_name:
                        # For nested parameters like "data.key1", traverse the extracted_data
                        parts = param_name.split(".")
                        value = extracted_data
                        for part in parts:
                            value = value.get(part, {}) if isinstance(value, dict) else None
                    else:
                        value = extracted_data.get(param_name, param.get("defaultValue"))
                    
                    logger.debug(f"Processing parameter: {param_name}, Value: {value}")

                    if param["required"] and value is None:
                        logger.error(f"Missing required parameter: {param_name}")
                        raise HTTPException(
                            status_code=400, 
                            detail=f"Missing required parameter: {param_name}"
                        )

                    # For body parameters, construct the nested structure if needed
                    if param["location"] == "body":
                        if "." in param_name:
                            # Build nested dictionary structure
                            current = body_params
                            parts = param_name.split(".")
                            for part in parts[:-1]:
                                current = current.setdefault(part, {})
                            current[parts[-1]] = value
                        else:
                            body_params[param_name] = value
                    elif param["location"] == "query":
                        query_params[param_name] = value
                    elif param["location"] == "header":
                        headers[param_name] = value

            logger.debug(f"Prepared parameters - Query: {query_params}, Body: {body_params}, Headers: {headers}")

            # Execute HTTP request
            async with httpx.AsyncClient() as client:
                print("query_params",query_params)
                request_kwargs = {
                    "method": action["method"],
                    "url": action["endpoint"],
                    "params": query_params,
                    "headers": headers,
                    "timeout": 30.0  # 30 second timeout
                }

                # Add body parameters if any
                if body_params:
                    if action.get("requestConfig", {}).get("requestFormat") == "json":
                        request_kwargs["json"] = body_params
                        logger.debug("Using JSON request format")
                    else:
                        request_kwargs["data"] = body_params
                        logger.debug("Using form-data request format")

                logger.info(f"Executing HTTP request to: {action['endpoint']}")
                logger.debug(f"Request configuration: {request_kwargs}")

                try:
                    response = await client.request(**request_kwargs)
                    logger.info(f"Request completed - Status: {response.status_code}")
                    logger.debug(f"Response headers: {dict(response.headers)}")

                    # Determine response format
                    content_type = response.headers.get("content-type", "")
                    if "application/json" in content_type:
                        response_data = response.json()
                        logger.debug(f"JSON response received: {response_data}")
                    else:
                        response_data = response.text
                        logger.debug(f"Text response received: {response_data[:200]}...")  # Log first 200 chars

                    # Prepare response data
                    execution_result = {
                        "status_code": response.status_code,
                        "response": response_data,
                        "executed_at": datetime.utcnow()
                    }

                    # Update email document with action execution result
                    await db.emails.update_one(
                        {"_id": ObjectId(email_id)},
                        {
                            "$set": {
                                f"action_executions.{action_name}": execution_result,
                                "updated_date": datetime.utcnow()
                            }
                        }
                    )

                    return execution_result

                except httpx.TimeoutException as e:
                    logger.error(f"Request timeout: {str(e)}")
                    raise HTTPException(status_code=504, detail="Request timeout")
                except httpx.RequestError as e:
                    logger.error(f"Request failed: {str(e)}")
                    raise HTTPException(status_code=502, detail=f"Request failed: {str(e)}")

        except HTTPException as he:
            # Re-raise HTTP exceptions with their original status codes
            raise he
        except Exception as e:
            logger.error(f"Unexpected error executing action: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Failed to execute action: {str(e)}")
