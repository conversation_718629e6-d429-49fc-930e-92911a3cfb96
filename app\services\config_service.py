import logging
from fastapi import HTT<PERSON>Exception
from app.config.constant import DEFAULT_EMAIL_RESPONDER
from app.schemas.config_schema import ConfigurationCreate, ConfigurationUpdate
from app.schemas.prompt_schema import PromptSettingsUpdate
from app.utils.security import get_password_hash, decrypt_password, fernet
from bson import ObjectId


logger = logging.getLogger(__name__)

class ConfigService:
    @staticmethod
    async def get_user_config(db, user_id: str):
        """Get user configuration details"""
        try:
            # Get user configuration
            config = await db.configurations.find_one({"user_id": user_id})
            if not config:
                logger.warning("No user configuration found")
                raise HTTPException(status_code=404, detail="User configuration not found")

            # Convert ObjectId to string for JSON serialization
            if "_id" in config:
                config["_id"] = str(config["_id"])

            # Remove sensitive information
            if "email_config" in config and "password" in config["email_config"]:
                del config["email_config"]["password"]

            # Add default settings to the response if they exist
            if config and "default_settings" in config:
                return config
            else:
                # If default settings don't exist, add them
                await db.configurations.update_one(
                    {"user_id": user_id},
                    {"$set": {"default_settings": DEFAULT_EMAIL_RESPONDER}}
                )
                config = await db.configurations.find_one({"user_id": user_id})
                return config
        
        except HTTPException as http_error:
            logger.error(f"HTTP error: {http_error.detail}")
            raise http_error  # propagate the HTTPException

        except Exception as e:
            logger.error(f"Error retrieving configurations: {str(e)}")
            raise HTTPException(status_code=500, detail="Error retrieving configurations")


    @staticmethod
    async def create_configurations(config: ConfigurationCreate, db, user_id: str):
        """Create new configurations"""
        try:
            # Check if user exists
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Check if configuration already exists for user
            existing_config = await db.configurations.find_one({"user_id": user_id})
            
            update_dict = {}
            if config.email_responder:
                update_dict["email_responder"] = {
                    "prompts": {
                        k: v.dict() for k, v in config.email_responder.prompts.items()
                    },
                    "categories": [cat.dict() for cat in config.email_responder.categories],
                    "workflow": config.email_responder.workflow.dict() if config.email_responder.workflow else None
                }
            
            if config.email_adaptar:
                update_dict["email_adaptar"] = {
                    "folderName": config.email_adaptar.folderName
                }

            if not update_dict:
                raise HTTPException(
                    status_code=400,
                    detail="No configuration provided"
                )

            if existing_config:
                # Update existing configuration if email_responder or email_adaptar is missing
                if config.email_responder and "email_responder" not in existing_config:
                    result = await db.configurations.update_one(
                        {"user_id": user_id},
                        {"$set": {"email_responder": update_dict["email_responder"]}}
                    )
                if config.email_adaptar and "email_adaptar" not in existing_config:
                    result = await db.configurations.update_one(
                        {"user_id": user_id},
                        {"$set": {"email_adaptar": update_dict["email_adaptar"]}}
                    )
                created_config = await db.configurations.find_one({"user_id": user_id})
            else:
                # Create new configuration if none exists
                update_dict["user_id"] = user_id
                result = await db.configurations.insert_one(update_dict)
                created_config = await db.configurations.find_one({"_id": result.inserted_id})
            
            # Convert ObjectId to string for JSON serialization
            if created_config and "_id" in created_config:
                created_config["_id"] = str(created_config["_id"])
            
            logger.info(f"Configurations created successfully")
            return {
                "status_code": 201,
                "status": "OK", 
                "message": "Configurations created successfully",
                "data": created_config
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating configurations: {str(e)}")
            raise HTTPException(status_code=500, detail="Error creating configurations")

    @staticmethod
    async def get_prompt_settings(db, user_id: str):
        """Get prompt settings configuration"""
        try:
            config = await db.prompt_settings.find_one({"user_id": user_id})
            if not config:
                logger.warning("No prompt settings found")
                raise HTTPException(status_code=404, detail="Prompt settings not found")

            # Convert ObjectId to string
            if "_id" in config:
                config["_id"] = str(config["_id"])

            return config

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving prompt settings: {str(e)}")
            raise HTTPException(status_code=500, detail="Error retrieving prompt settings")

    @staticmethod
    async def update_prompt_settings(settings: PromptSettingsUpdate, db, user_id: str):
        """Update prompt settings configuration"""
        try:
            update_data = settings.dict(exclude_none=True)
            if not update_data:
                raise HTTPException(status_code=400, detail="No update data provided")

            result = await db.prompt_settings.update_one(
                {"user_id": user_id},
                {"$set": update_data},
                upsert=True
            )

            updated_settings = await db.prompt_settings.find_one({"user_id": user_id})
            if "_id" in updated_settings:
                updated_settings["_id"] = str(updated_settings["_id"])

            return {
                "status_code": 200,
                "status": "OK",
                "message": "Prompt settings updated successfully",
                "data": updated_settings
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating prompt settings: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to update prompt settings")

    @staticmethod
    async def update_user_config(config: ConfigurationUpdate, db, user_id: str):
        print("yes")
        """Update configurations with proper email password encryption handling"""
        try:
            # Update configurations
            update_data = {}
            
            if config.email_responder:
                update_data["email_responder"] = config.email_responder.dict()
            if config.email_adaptar:
                update_data["email_adaptar"] = config.email_adaptar.dict()
            
            # Handle email configuration updates
            if config.email_config:
                email_config_update = config.email_config.dict()
                
                # If email config exists, get current config to preserve password if not updated
                current_config = await db.configurations.find_one({"user_id": user_id})
                # if current_config and "email_config" in current_config:
                    # If password not in update, keep existing encrypted password
                #     if not email_config_update.get("password"):
                #         email_config_update["password"] = current_config["email_config"]["password"]
                #     else:
                #         # Encrypt new password
                #         try:
                #             encrypted_password = fernet.encrypt(email_config_update["password"].encode())
                #             email_config_update["password"] = encrypted_password.decode()
                #         except Exception as e:
                #             logger.error(f"Error encrypting new password: {str(e)}")
                #             raise HTTPException(status_code=500, detail="Failed to encrypt email password")
                
                # update_data["email_config"] = email_config_update

            if not update_data:
                raise HTTPException(status_code=400, detail="No update data provided")

            print("update_data", update_data)
            # Update the configuration in database
            result = await db.configurations.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )

            if result.modified_count == 0:
                raise HTTPException(status_code=404, detail="Configuration not found or no changes made")

            # Fetch and return updated configuration
            updated_config = await db.configurations.find_one({"user_id": user_id})
            if not updated_config:
                raise HTTPException(status_code=404, detail="Failed to retrieve updated configuration")

            # Remove sensitive information before returning
            # if "email_config" in updated_config and "password" in updated_config["email_config"]:
            #     del updated_config["email_config"]["password"]

            # Convert ObjectId to string
            if "_id" in updated_config:
                updated_config["_id"] = str(updated_config["_id"])

            return {
                "status_code": 200,
                "status": "OK",
                "message": "Configuration updated successfully",
                # "data": updated_config
            }

        except HTTPException as he:
            logger.warning(f"HTTP error during configuration update: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Error updating configuration: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to update configuration")
