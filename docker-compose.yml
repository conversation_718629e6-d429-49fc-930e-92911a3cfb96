version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - DB_NAME=email_responder
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OUTLOOK_CLIENT_ID=${OUTLOOK_CLIENT_ID}
      - OUTLOOK_CLIENT_SECRET=${OUTLOOK_CLIENT_SECRET}
      - OUTLOOK_TENANT_ID=${OUTLOOK_TENANT_ID}
      - OUTLOOK_REDIRECT_URI=${OUTLOOK_REDIRECT_URI}
    networks:
      - app_network
    depends_on:
      - mongodb
      - worker

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - DB_NAME=email_responder
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OUTL<PERSON><PERSON>_CLIENT_ID=${OUTLOOK_CLIENT_ID}
      - OUTLOOK_CLIENT_SECRET=${OUTLOOK_CLIENT_SECRET}
      - OUTLOOK_TENANT_ID=${OUTLOOK_TENANT_ID}
      - OUTLOOK_REDIRECT_URI=${OUTLOOK_REDIRECT_URI}
    networks:
      - app_network
    depends_on:
      - mongodb

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - app_network

networks:
  app_network:
    driver: bridge

volumes:
  mongodb_data:
