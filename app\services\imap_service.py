import logging
import imaplib
import email as email_lib
from email.header import decode_header
import base64
from datetime import datetime, timezone
from fastapi import HTT<PERSON>Exception
from app.utils.security import fernet
from typing import List, Dict, Optional, Any, Tuple
from bson import ObjectId
import traceback
import os

logger = logging.getLogger(__name__)

class ImapService:
    """Service for handling IMAP email operations"""
    
    @staticmethod
    async def connect_to_imap(email_config: Dict[str, Any]) -> Tuple[imaplib.IMAP4_SSL, str]:
        """
        Connect to IMAP server using the provided email configuration
        
        Args:
            email_config: Dictionary containing email configuration
            
        Returns:
            Tuple of IMAP connection and decrypted password
        """
        try:
            print("Starting IMAP connection with config:", email_config)
            
            # Determine IMAP server based on email address
            if "gmail.com" in email_config["imap_config"]["email"].lower():
                imap_server = "imap.gmail.com"
            else:
                imap_server = email_config["imap_config"].get("imap_server","mail.comorin.co")
            print(f"Using IMAP server: {imap_server}")
            
            # Decrypt password
            try:
                print("Attempting to decrypt password...")
                decrypted_password = fernet.decrypt(email_config["imap_config"]["password"].encode()).decode()
                print("Password decrypted successfully")
                
            except Exception as e:
                logger.error(f"Failed to decrypt email password: {str(e)}")
                print(f"Password decryption failed: {str(e)}")
                raise HTTPException(status_code=500, detail="Failed to decrypt email password")
            
            # Connect to IMAP server
            print(f"Connecting to IMAP server {imap_server} with email {email_config['imap_config']['email']}")
            mail = imaplib.IMAP4_SSL(imap_server)
            print("SSL connection established")
            
            print("Attempting login...")
            mail.login(email_config["imap_config"]["email"], decrypted_password)
            print("Login successful")
            
            return mail, decrypted_password
        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP connection error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"IMAP connection error: {str(e)}")
        except Exception as e:
            logger.error(f"Error connecting to IMAP server: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error connecting to IMAP server: {str(e)}")
    
    @staticmethod
    async def fetch_emails(email_config: Dict[str, Any], max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Fetch emails from IMAP server
        
        Args:
            email_config: Dictionary containing email configuration
            max_results: Maximum number of emails to fetch
            
        Returns:
            List of email dictionaries
        """
        try:
            mail, _ = await ImapService.connect_to_imap(email_config)
            
            try:
                # Select inbox folder
                mail.select('INBOX')
                
                # Search for all emails, sorted by date (newest first)
                _, data = mail.search(None, 'ALL')
                email_ids = data[0].split()
                # print("fetched email ids", email_ids)
                
                # Get the most recent emails (limited by max_results)
                email_ids = email_ids[-max_results:] if len(email_ids) > max_results else email_ids
                
                emails = []
                current_time = datetime.utcnow()
                
                for email_id in reversed(email_ids):  # Process newest first
                    _, msg_data = mail.fetch(email_id, '(RFC822)')
                    raw_email = msg_data[0][1]
                    
                    # Parse the raw email
                    msg = email_lib.message_from_bytes(raw_email)
                    
                    # Extract headers
                    message_id = msg.get('Message-ID', '')
                    subject = ImapService._decode_header(msg.get('Subject', ''))
                    sender = ImapService._decode_header(msg.get('From', ''))
                    recipient = ImapService._decode_header(msg.get('To', ''))
                    date_str = msg.get('Date', '')
                    in_reply_to = msg.get('In-Reply-To', None)
                    references = msg.get('References', None)
                    
                    # Extract body
                    body = ImapService._get_email_body(msg)
                    
                    # Check if email is read
                    _, response = mail.fetch(email_id, '(FLAGS)')
                    flags = response[0].decode('utf-8')
                    is_read = '\\Seen' in flags
                    
                    # Create email object
                    email_data = {
                        "message_id": str(message_id),
                        "subject": str(subject),
                        "sender": str(sender),
                        "recipient": str(recipient),
                        "date": ImapService._parse_email_date(date_str),
                        "fetched_date": current_time,
                        "body": str(body) if body else "",
                        "is_read": bool(is_read),
                        "folder": "INBOX",
                        "in_reply_to": str(in_reply_to) if in_reply_to else None,
                        "references": str(references) if references else None,
                        "headers": {
                            h.lower(): str(msg.get(h, '')) 
                            for h in msg.keys() 
                            if h.lower() not in ["subject", "from", "to", "date", "message-id", "in-reply-to", "references"]
                        }
                    }
                    
                    emails.append(email_data)
                
                return emails
                
            finally:
                mail.logout()
                
        except Exception as e:
            logger.error(f"Error fetching emails via IMAP: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error fetching emails: {str(e)}")
    
    @staticmethod
    async def create_draft(email_config: Dict[str, Any], to_address: str, subject: str, 
                          body: str, in_reply_to: Optional[str] = None, 
                          references: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a draft email using IMAP
        
        Args:
            email_config: Dictionary containing email configuration
            to_address: Recipient email address
            subject: Email subject
            body: Email body content
            in_reply_to: Optional Message-ID this email is replying to
            references: Optional References header for threading
            
        Returns:
            Dictionary with draft information including server_uid
        """
        try:
            mail, _ = await ImapService.connect_to_imap(email_config)
            
            try:
                # Determine drafts folder based on email provider
                if "gmail.com" in email_config["imap_config"]["email"].lower():
                    draft_folder = "[Gmail]/Drafts"
                else:
                    # For non-Gmail accounts, list all available folders
                    print("Listing available folders...")
                    folder_list = mail.list()[1]
                    available_folders = []
                    for f in folder_list:
                        try:
                            folder_name = f.decode().split('"')[-1].strip()
                            available_folders.append(folder_name)
                            print(f"Found folder: {folder_name}")
                        except Exception as e:
                            print(f"Error parsing folder: {str(e)}")
                    
                    # Check if we need to prefix with INBOX
                    needs_inbox_prefix = any("INBOX." in folder for folder in available_folders)
                    
                    # Try common draft folder names with appropriate prefix
                    if needs_inbox_prefix:
                        possible_folders = ["INBOX.Drafts", "INBOX.Draft", "INBOX.DRAFTS", "INBOX.DRAFT"]
                    else:
                        possible_folders = ["Drafts", "Draft", "DRAFTS", "DRAFT"]
                    
                    # Find matching draft folder
                    draft_folder = None
                    for folder in possible_folders:
                        if folder in available_folders:
                            draft_folder = folder
                            print(f"Found matching draft folder: {folder}")
                            break
                    
                    # Default to INBOX if no match found
                    if not draft_folder:
                        print("No draft folder found, using INBOX")
                        draft_folder = "INBOX"
                
                print(f"Using draft folder: {draft_folder}")
                
                # Try to select the drafts folder
                try:
                    result = mail.select(draft_folder)
                    status = result[0].decode() if isinstance(result[0], bytes) else result[0]
                    print(f"Selected folder {draft_folder}: {status}")
                    
                    if status != 'OK':
                        print(f"Failed to select {draft_folder}, falling back to INBOX")
                        draft_folder = "INBOX"
                        mail.select(draft_folder)
                except Exception as e:
                    print(f"Error selecting folder: {str(e)}")
                    # Don't try to create folders - just use INBOX
                    draft_folder = "INBOX"
                    mail.select(draft_folder)
                
                # Create draft message with threading headers
                from email.mime.text import MIMEText
                from email.mime.multipart import MIMEMultipart
                
                # Use MIMEMultipart instead of EmailMessage for better compatibility
                msg = MIMEMultipart()
                msg["From"] = email_config["imap_config"]["email"]
                msg["To"] = to_address
                msg["Subject"] = subject
                msg["Date"] = email_lib.utils.formatdate(localtime=True)
                
                # Generate unique Message-ID
                timestamp = datetime.now().timestamp()
                domain = email_config["imap_config"]["email"].split('@')[1]
                unique_id = f"draft.{timestamp}@{domain}"
                msg["Message-ID"] = f"<{unique_id}>"
                
                # Set threading headers if provided
                if in_reply_to:
                    msg["In-Reply-To"] = in_reply_to
                    # Add custom header to ensure linkage
                    msg["X-Original-Message-ID"] = in_reply_to
                
                if references:
                    msg["References"] = references
                elif in_reply_to:  # If no references but there is in_reply_to, use that
                    msg["References"] = in_reply_to
                
                # Set content
                msg.attach(MIMEText(body, 'plain'))
                
                # Convert message to string and encode
                message_bytes = msg.as_string().encode()
                
                print(f"Saving draft to {draft_folder} folder")
                
                # Save to Drafts folder
                result = mail.append(
                    draft_folder, 
                    r'(\Draft)', 
                    imaplib.Time2Internaldate(datetime.now(timezone.utc)), 
                    message_bytes
                )
                
                print(f"IMAP append result: {result}")
                
                # Extract UID and other information from APPEND response
                draft_server_id = {
                    "email": email_config["imap_config"]["email"],
                    "folder": draft_folder,
                    "server_uid": None,
                    "validity_token": None,
                    "full_response": str(result),
                    "message_id": msg["Message-ID"]
                }
                
                if isinstance(result, tuple) and result[1]:
                    response = result[1][0].decode() if isinstance(result[1][0], bytes) else str(result[1][0])
                    print(f"IMAP response: {response}")
                    
                    if "[APPENDUID" in response:
                        uid_parts = response.split()
                        if len(uid_parts) >= 3:
                            draft_server_id.update({
                                "server_uid": uid_parts[2].rstrip(']'),
                                "validity_token": uid_parts[1],
                                "full_response": response
                            })
                    
                    # If we couldn't extract UID from APPEND response, search for the message
                    if not draft_server_id["server_uid"]:
                        print("Searching for message by Message-ID")
                        try:
                            # Make sure we're in a selected state
                            mail_state = getattr(mail, 'state', None)
                            print(f"Current IMAP state: {mail_state}")
                            
                            if mail_state != 'SELECTED':
                                print(f"Selecting folder {draft_folder} before search")
                                mail.select(draft_folder)
                            
                            # Search for the message we just added by Message-ID
                            message_id = msg["Message-ID"].strip("<>")
                            print(f"Searching for Message-ID: {message_id}")
                            
                            # Use UID SEARCH instead of regular SEARCH
                            search_cmd = f'HEADER Message-ID "{message_id}"'
                            typ, data = mail.uid('SEARCH', None, search_cmd)
                            print(f"Search result: {typ}, {data}")
                            
                            if typ == 'OK' and data[0]:
                                uid = data[0].split()[-1].decode() if isinstance(data[0].split()[-1], bytes) else data[0].split()[-1]
                                draft_server_id["server_uid"] = uid
                                print(f"Found message UID: {uid}")
                        except Exception as search_err:
                            print(f"Error searching for message: {str(search_err)}")
                
                return draft_server_id
                
            finally:
                mail.logout()
                
        except Exception as e:
            logger.error(f"Error creating draft via IMAP: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating draft: {str(e)}")
    
    @staticmethod
    async def update_draft(email_config: Dict[str, Any], draft_server_id: Dict[str, Any], 
                          to_address: str, subject: str, body: str) -> Dict[str, Any]:
        """
        Update an existing draft email using IMAP
        
        Args:
            email_config: Dictionary containing email configuration
            draft_server_id: Dictionary with server information about the draft
            to_address: Recipient email address
            subject: Email subject
            body: Email body content
            
        Returns:
            Updated draft_server_id dictionary
        """
        try:
            mail, _ = await ImapService.connect_to_imap(email_config)
            
            try:
                # Get the folder from draft_server_id or use default
                draft_folder = draft_server_id.get("folder", "[Gmail]/Drafts")
                
                # For non-Gmail accounts, check if we need to use INBOX
                if "gmail.com" not in email_config["imap_config"]["email"].lower() and not draft_folder.startswith("INBOX."):
                    # List all available folders
                    folder_list = mail.list()[1]
                    available_folders = [f.decode().split('"')[-1].strip() for f in folder_list if isinstance(f, bytes)]
                    
                    # Check if we need to prefix with INBOX
                    if any("INBOX." in folder for folder in available_folders) and not draft_folder.startswith("INBOX."):
                        # Try with INBOX prefix
                        inbox_prefixed = f"INBOX.{draft_folder}"
                        if inbox_prefixed in available_folders:
                            draft_folder = inbox_prefixed
                
                # Try to select the drafts folder
                try:
                    result = mail.select(draft_folder)
                    status = result[0].decode() if isinstance(result[0], bytes) else result[0]
                    print(f"Selected folder {draft_folder}: {status}")
                    
                    if status != 'OK':
                        print(f"Failed to select {draft_folder}, falling back to INBOX")
                        draft_folder = "INBOX"
                        mail.select(draft_folder)
                        draft_server_id["folder"] = draft_folder
                except Exception as e:
                    print(f"Error selecting folder {draft_folder}: {str(e)}")
                    # Fallback to INBOX
                    draft_folder = "INBOX"
                    mail.select(draft_folder)
                    draft_server_id["folder"] = draft_folder
                
                if "server_uid" in draft_server_id and draft_server_id["server_uid"]:
                    server_uid = draft_server_id["server_uid"]
                    print(f"Updating draft with UID: {server_uid}")
                    
                    # Create updated message
                    from email.mime.text import MIMEText
                    from email.mime.multipart import MIMEMultipart
                    
                    # Use MIMEMultipart instead of EmailMessage for better compatibility
                    msg = MIMEMultipart()
                    msg["From"] = email_config["imap_config"]["email"]
                    msg["To"] = to_address
                    msg["Subject"] = subject
                    msg["Date"] = email_lib.utils.formatdate(localtime=True)
                    
                    # Preserve Message-ID if available
                    if "message_id" in draft_server_id:
                        msg["Message-ID"] = draft_server_id["message_id"]
                    
                    # Preserve threading headers if available
                    if "in_reply_to" in draft_server_id:
                        msg["In-Reply-To"] = draft_server_id["in_reply_to"]
                    
                    if "references" in draft_server_id:
                        msg["References"] = draft_server_id["references"]
                    
                    # Set content
                    msg.attach(MIMEText(body, 'plain'))
                    
                    try:
                        # Delete the old draft
                        print(f"Deleting old draft with UID: {server_uid}")
                        mail.uid("STORE", server_uid, "+FLAGS", r"(\Deleted)")
                        mail.expunge()
                        print("Old draft deleted successfully")
                    except Exception as del_err:
                        print(f"Error deleting old draft: {str(del_err)}")
                        # Continue even if delete fails
                    
                    # Append the updated draft
                    print(f"Appending updated draft to {draft_folder}")
                    result = mail.append(
                        draft_folder,
                        r'(\Draft)',
                        imaplib.Time2Internaldate(datetime.now(timezone.utc)),
                        msg.as_string().encode()
                    )
                    
                    print(f"IMAP append result: {result}")
                    
                    # Update the server_uid with the new one
                    if isinstance(result, tuple) and result[1]:
                        response = result[1][0].decode() if isinstance(result[1][0], bytes) else str(result[1][0])
                        print(f"IMAP response: {response}")
                        
                        if "[APPENDUID" in response:
                            uid_parts = response.split()
                            if len(uid_parts) >= 3:
                                draft_server_id["server_uid"] = uid_parts[2].rstrip("]")
                                draft_server_id["validity_token"] = uid_parts[1]
                                draft_server_id["full_response"] = response
                                print(f"Updated draft UID: {draft_server_id['server_uid']}")
                        
                        # If we couldn't extract UID from APPEND response, search for the message
                        if not draft_server_id.get("server_uid"):
                            print("Searching for message by Message-ID")
                            # Search for the message we just added by Message-ID
                            message_id = msg["Message-ID"].strip("<>")
                            search_cmd = f'(HEADER Message-ID "{message_id}")'
                            typ, data = mail.search(None, search_cmd)
                            
                            if typ == 'OK' and data[0]:
                                msg_num = data[0].split()[-1]
                                # Get the UID for this message number
                                typ, data = mail.fetch(msg_num, '(UID)')
                                if typ == 'OK' and data[0]:
                                    uid_data = data[0].decode()
                                    if 'UID' in uid_data:
                                        uid = uid_data.split('UID ')[1].rstrip(')')
                                        draft_server_id["server_uid"] = uid
                                        print(f"Found message UID: {uid}")
                    
                    return draft_server_id
                else:
                    print("No server_uid found, creating new draft")
                    # If no server_uid, create a new draft
                    return await ImapService.create_draft(
                        email_config, to_address, subject, body,
                        draft_server_id.get("in_reply_to"), draft_server_id.get("references")
                    )
                
            finally:
                mail.logout()
                
        except Exception as e:
            logger.error(f"Error updating draft via IMAP: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating draft: {str(e)}")
    
    @staticmethod
    async def send_email(email_config: Dict[str, Any], to_address: str, subject: str, 
                        body: str, in_reply_to: Optional[str] = None, 
                        references: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email using SMTP
        
        Args:
            email_config: Dictionary containing email configuration
            to_address: Recipient email address
            subject: Email subject
            body: Email body content
            in_reply_to: Optional Message-ID this email is replying to
            references: Optional References header for threading
            
        Returns:
            Dictionary with send status and message ID
        """
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Determine SMTP server based on email address
            if "gmail.com" in email_config["imap_config"]["email"].lower():
                smtp_server = "smtp.gmail.com"
                smtp_port = 587
            else:
                smtp_server = email_config["imap_config"].get("smtp_server", "smtp.comorin.co")
                smtp_port = email_config["imap_config"].get("smtp_port", 587)
            
            # Decrypt password
            try:
                decrypted_password = fernet.decrypt(email_config["imap_config"]["password"].encode()).decode()
                
            except Exception as e:
                logger.error(f"Failed to decrypt email password: {str(e)}")
                raise HTTPException(status_code=500, detail="Failed to decrypt email password")
            
            # Create message
            msg = MIMEMultipart()
            msg["From"] = email_config["imap_config"]["email"]
            msg["To"] = to_address
            msg["Subject"] = subject
            
            # Generate unique Message-ID
            timestamp = datetime.now().timestamp()
            domain = email_config["imap_config"]["email"].split('@')[1]
            unique_id = f"sent.{timestamp}@{domain}"
            msg["Message-ID"] = f"<{unique_id}>"
            
            # Set threading headers if provided
            if in_reply_to:
                msg["In-Reply-To"] = in_reply_to
            
            if references:
                msg["References"] = references
            elif in_reply_to:  # If no references but there is in_reply_to, use that
                msg["References"] = in_reply_to
            
            # Attach body
            msg.attach(MIMEText(body, "plain"))
            
            # Connect to SMTP server and send
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(email_config["imap_config"]["email"], decrypted_password)
                server.send_message(msg)
            
            return {
                "status": "success",
                "message": "Email sent successfully",
                "message_id": msg["Message-ID"]
            }
            
        except Exception as e:
            logger.error(f"Error sending email via SMTP: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")
    
    @staticmethod
    async def delete_draft(email_config: Dict[str, Any], draft_server_id: Dict[str, Any]) -> bool:
        """
        Delete a draft email using IMAP
        
        Args:
            email_config: Dictionary containing email configuration
            draft_server_id: Dictionary with server information about the draft
            
        Returns:
            Boolean indicating success
        """
        try:
            mail, _ = await ImapService.connect_to_imap(email_config)
            
            try:
                draft_folder = draft_server_id.get("folder", "[Gmail]/Drafts")
                
                try:
                    result = mail.select(draft_folder)
                    status = result[0].decode() if isinstance(result[0], bytes) else result[0]
                    print(f"Selected folder {draft_folder}: {status}")
                    
                    if status != 'OK':
                        print(f"Failed to select {draft_folder}, trying INBOX")
                        draft_folder = "INBOX"
                        mail.select(draft_folder)
                except Exception as e:
                    print(f"Error selecting folder {draft_folder}: {str(e)}")
                    # Fallback to INBOX
                    draft_folder = "INBOX"
                    mail.select(draft_folder)
                
                if "server_uid" in draft_server_id:
                    server_uid = draft_server_id["server_uid"]
                    
                    # Delete message using UID
                    mail.uid('STORE', server_uid, '+FLAGS', r'(\Deleted)')
                    mail.expunge()
                    return True
                else:
                    # Fallback for legacy draft IDs - try to find by Message-ID
                    message_id = draft_server_id.get("message_id")
                    if message_id:
                        _, messages = mail.search(None, f'(HEADER Message-ID "{message_id}")')
                        if messages[0]:
                            for num in messages[0].split():
                                mail.store(num, '+FLAGS', r'(\Deleted)')
                            mail.expunge()
                            return True
                    
                    # If we get here, we couldn't find the draft
                    logger.warning(f"Could not find draft to delete: {draft_server_id}")
                    return False
                
            finally:
                mail.logout()
                
        except Exception as e:
            logger.error(f"Error deleting draft via IMAP: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting draft: {str(e)}")
    
    @staticmethod
    def _decode_header(header: str) -> str:
        """Decode email header"""
        if not header:
            return ""
            
        decoded_parts = []
        for part, encoding in decode_header(header):
            if isinstance(part, bytes):
                if encoding:
                    try:
                        decoded_parts.append(part.decode(encoding))
                    except:
                        decoded_parts.append(part.decode('utf-8', errors='replace'))
                else:
                    decoded_parts.append(part.decode('utf-8', errors='replace'))
            else:
                decoded_parts.append(part)
                
        return ''.join(str(p) for p in decoded_parts)
    
    @staticmethod
    def _get_email_body(msg) -> str:
        """Extract email body from message"""
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                # Skip attachments
                if "attachment" in content_disposition:
                    continue
                
                # Get text content
                if content_type == "text/plain":
                    try:
                        body = part.get_payload(decode=True).decode()
                        return body
                    except:
                        pass
            
            # If no text/plain part found, try to get any text content
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type.startswith("text/"):
                    try:
                        body = part.get_payload(decode=True).decode()
                        return body
                    except:
                        pass
            
            return ""
        else:
            # Not multipart - get payload directly
            try:
                body = msg.get_payload(decode=True).decode()
                return body
            except:
                return ""
    
    @staticmethod 
    def _parse_email_date(date_str: str) -> datetime:
        """Parse email date string to datetime object"""
        if not date_str:
            return datetime.utcnow()
            
        # Remove timezone name in parentheses if present
        if '(' in date_str:
            date_str = date_str[:date_str.index('(')].strip()
            
        date_formats = [
            "%a, %d %b %Y %H:%M:%S %z",  # RFC 2822
            "%a, %d %b %Y %H:%M:%S %Z",  # With timezone name
            "%a, %d %b %Y %H:%M:%S GMT", # GMT format
            "%a, %d %b %Y %H:%M:%S +0000", # Explicit +0000
            "%a, %d %b %Y %H:%M:%S -0700", # PDT/PST
            "%a, %d %b %Y %H:%M:%S +0530", # IST
            "%d %b %Y %H:%M:%S %z",      # Without weekday
            "%a, %d %b %Y %H:%M:%S",     # Without timezone
            "%a, %d %b %Y %H:%M %z",     # Without seconds
            "%a, %d %b %Y %H:%M"         # Basic format
        ]
        
        # Try each format
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # If no timezone info, assume UTC
                if not hasattr(parsed_date, 'tzinfo') or parsed_date.tzinfo is None:
                    from datetime import timezone
                    return parsed_date.replace(tzinfo=timezone.utc)
                return parsed_date
            except ValueError:
                continue
            except Exception as e:
                logger.debug(f"Error parsing date '{date_str}' with format '{fmt}': {str(e)}")
                continue
        
        logger.warning(f"Could not parse date: {date_str}")
        return datetime.utcnow()
    
import asyncio

if __name__ == "__main__":
    # Example usage
    email_config = {
        "email": "<EMAIL>",
        "password": os.environ.get("EMAIL_PASSWORD"),
        "host": "mail.comorin.co",
        "port": 465,
        "imap_server": "mail.comorin.co",
        "smtp_server": "mail.comorin.co",
        "smtp_port": 587
    }
    
    


    
