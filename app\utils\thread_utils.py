import threading
import asyncio
import logging
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
from typing import Optional
from typing import Callable, Any
from functools import partial

logger = logging.getLogger(__name__)

class ThreadManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ThreadManager, cls).__new__(cls)
                cls._instance.thread_pool: Optional[ThreadPoolExecutor] = ThreadPoolExecutor(max_workers=10)
                cls._instance.workflow_executor: Optional[ThreadPoolExecutor] = ThreadPoolExecutor(max_workers=2)
            return cls._instance
    
    @staticmethod
    async def run_in_thread(func: Callable, *args, **kwargs) -> Any:
        """Run a function in a separate thread from the thread pool"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            ThreadManager().thread_pool,
            partial(func, *args, **kwargs)
        )
    
    @staticmethod
    async def run_workflow_in_thread(func: Callable, *args, **kwargs) -> Any:
        """Run a workflow function in a dedicated workflow thread"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            ThreadManager().workflow_executor,
            partial(func, *args, **kwargs)
        )
