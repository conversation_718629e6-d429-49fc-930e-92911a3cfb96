import os
import re
import base64
import logging
import mimetypes
import tempfile
import uuid
from typing import Dict, List, Optional, Tuple, BinaryIO, Any, Union
from pathlib import Path
from datetime import datetime
import aiofiles
import aiohttp
import boto3
from botocore.exceptions import ClientError
from email.message import EmailMessage
from email.header import decode_header, Header
from email.utils import parseaddr
from googleapiclient.errors import HttpError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

logger = logging.getLogger(__name__)

class AttachmentService:
    """Service for handling email attachments and inline images."""
    
    def __init__(self, s3_bucket: str = None, temp_dir: str = None):
        """
        Initialize the attachment service with streaming upload support.
        
        Args:
            s3_bucket: S3 bucket name for storing attachments
            temp_dir: Directory for temporary file storage (fallback for large files)
        """
        self.s3_bucket = s3_bucket or os.getenv("S3_BUCKET_NAME")
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.gettempdir()) / 'email_attachments'
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.chunk_size = 8 * 1024 * 1024  # 8MB chunks for multipart uploads
        self.max_memory_size = 50 * 1024 * 1024  # 10MB max for in-memory processing
        self.url_expiration = int(os.getenv('SIGNED_URL_EXPIRATION', 3600))  # 1 hour default
        
        # Initialize S3 client with optimized configuration
        self.s3 = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION', 'us-east-1'),
            config=boto3.session.Config(
                retries={
                    'max_attempts': 3,
                    'mode': 'standard'
                },
                max_pool_connections=100,  # Increased for better concurrency
                s3={'use_accelerate_endpoint': False}  # Disable transfer acceleration unless needed
            )
        )
    
    # ===== Core Attachment Processing =====
    
    async def process_email_attachments(
        self,
        email_message: Dict[str, Any],
        service: Any,
        message_id: str,
        provider: str = 'gmail',
        process_inline: bool = True
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Process all attachments in an email with streaming support.
        
        Args:
            email_message: Raw email message from the email provider
            service: Email service client (Gmail, Outlook, etc.)
            message_id: ID of the email message
            provider: Email provider ('gmail', 'outlook', 'imap')
            process_inline: Whether to process inline images
            
        Returns:
            Tuple of (email_data, attachments) where:
            - email_data: Dict with processed email data and inline images
            - attachments: List of processed attachments
            
        The method automatically chooses the appropriate processing method:
        - For small files (<10MB): Processes in memory
        - For large files: Uses streaming upload with multipart
        - For very large files: Uses temp files as fallback
        """
        try:
            print(f"Processing attachments for email {message_id}")
            
            # Process based on provider
            if provider == 'imap':
                result = await self._process_imap_attachments(email_message, service, message_id, process_inline)
            else:
                # Default to Gmail format for all other providers including 'outlook'
                result = await self._process_gmail_attachments(email_message, service, message_id, process_inline)
            
            # Prepare the return values expected by the Gmail service
            email_data = {
                'text': email_message.get('snippet', ''),
                'html': '',  # This would be populated from the email body
                'inline_images': result.get('inline_images', {})
            }
            
            return email_data, result.get('attachments', [])
                
        except Exception as e:
            logger.error(f"Error processing attachments: {str(e)}")
            logger.exception("Full traceback:")
            # Return empty results on error to not block email processing
            return {'text': '', 'html': '', 'inline_images': {}}, []
    
    # ===== Gmail Attachment Processing =====
    
    async def _process_gmail_attachments(
        self,
        email_message: Dict[str, Any],
        service: Any,
        message_id: str,
        process_inline: bool = True
    ) -> Dict[str, Any]:
        """
        Process all attachments and inline images from a Gmail message.
        
        Args:
            email_message: Raw email message from Gmail API
            service: Gmail service client
            message_id: ID of the email message
            process_inline: Whether to process inline images
            
        Returns:
            Dict with processed attachments and inline images with S3 URLs
        """
        try:
            # Initialize results
            result = {
                'attachments': [],
                'inline_images': {},
                'has_attachments': False
            }
            
            # Extract user_id from the service or email headers
            user_id = 'unknown'  # Default value if we can't determine the user
            if hasattr(service, 'user_id'):
                user_id = service.user_id
            elif 'payload' in email_message and 'headers' in email_message['payload']:
                for header in email_message['payload'].get('headers', []):
                    if header.get('name', '').lower() == 'to':
                        # Parse the address to separate name and email
                        _, user_id = parseaddr(header.get('value', 'unknown'))
                        break
            
            # Check if the email has any parts to process
            if 'payload' not in email_message:
                logger.warning(f"No payload in email message: {message_id}")
                return result
                
            payload = email_message.get('payload', {})
            
            # Process all parts of the email
            parts = payload.get('parts', [])
            if not parts and 'body' in payload and 'attachmentId' in payload['body']:
                # Handle single attachment case
                parts = [payload]
            
            if not parts:
                logger.info(f"No parts to process in email: {message_id}")
                return result
            
            try:
                # Process all email parts (this will handle both attachments and inline images)
                attachments, inline_images = await self._process_gmail_parts(
                    parts=parts,
                    user_id=user_id,
                    email_id=message_id,
                    service=service
                )
                
                # Process inline images
                if process_inline and inline_images:
                    result['inline_images'] = {
                        cid: {
                            'filename': img['filename'],
                            's3_key': img['s3_key'],
                            'mime_type': img['mime_type'],
                            'size': img['size'],
                            'url': img['url'],
                            'content_id': cid,
                            'is_inline': True
                        }
                        for cid, img in inline_images.items()
                        if cid and img and 'url' in img
                    }
                
                # Process regular attachments
                if attachments:
                    result['attachments'] = [
                        {
                            'filename': att['filename'],
                            's3_key': att['s3_key'],
                            'mime_type': att['mime_type'],
                            'size': att['size'],
                            'url': att['url'],
                            'is_inline': False
                        }
                        for att in attachments
                        if att and 'url' in att
                    ]
                
                # Update has_attachments flag
                result['has_attachments'] = len(result.get('attachments', [])) > 0 or len(result.get('inline_images', {})) > 0
                
            except Exception as e:
                logger.error(f"Error processing email parts: {str(e)}")
                logger.exception("Error details:")
                
            return result
            
        except Exception as e:
            logger.error(f"Error processing Gmail attachments: {str(e)}")
            logger.exception("Full traceback:")
            return {
                'attachments': [],
                'inline_images': {},
                'has_attachments': False
            }
            
    async def _upload_to_s3(self, file_data: bytes, s3_key: str, content_type: str) -> None:
        """
        Upload file data to S3.
        
        Args:
            file_data: File data to upload
            s3_key: S3 object key
            content_type: MIME type of the file
            
        Raises:
            Exception: If upload fails
        """
        try:
            self.s3.put_object(
                Bucket=self.s3_bucket,
                Key=s3_key,
                Body=file_data,
                ContentType=content_type
            )
        except Exception as e:
            logger.error(f"Failed to upload to S3: {str(e)}")
            raise
            
    def _generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> str:
        """
        Generate a pre-signed URL for an S3 object.
        
        Args:
            s3_key: S3 object key
            expiration: Expiration time in seconds
            
        Returns:
            Pre-signed URL as string
        """
        try:
            return self.s3.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.s3_bucket,
                    'Key': s3_key
                },
                ExpiresIn=expiration
            )
        except Exception as e:
            logger.error(f"Failed to generate pre-signed URL: {str(e)}")
            return ""
    
    async def _download_gmail_attachment(
        self,
        attachment_id: str,
        filename: str,
        mime_type: str,
        headers: List[Dict[str, str]],
        service: Any = None,
        message_id: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        Download an attachment from Gmail using the Gmail API.
        
        Args:
            attachment_id: The attachment ID from Gmail API
            filename: Name of the attachment file
            mime_type: MIME type of the attachment
            headers: List of headers for the attachment part
            service: Optional Gmail service client (if not using global)
            message_id: ID of the email message (required if using service)
            
        Returns:
            Dict containing attachment metadata and data, or None if download fails
            
        Raises:
            HttpError: If the Gmail API request fails
        """
        try:
            # Use provided service or get from app context if not provided
            if service is None:
                from app.main import app
                service = getattr(app.state, 'gmail_service', None)
                if service is None:
                    logger.error("Gmail service not available in app context")
                    return None
            
            if message_id is None:
                logger.error("Message ID is required to download attachment")
                return None
            
            # Get the attachment data
            attachment = service.users().messages().attachments().get(
                userId='me',
                messageId=message_id,
                id=attachment_id
            ).execute()
            
            if not attachment or 'data' not in attachment:
                logger.error(f"No data received for attachment {attachment_id}")
                return None
            
            # Decode the attachment data
            file_data = base64.urlsafe_b64decode(attachment['data'].encode('UTF-8'))
            
            return {
                'filename': filename,
                'mime_type': mime_type,
                'data': file_data,
                'size': len(file_data),
                'headers': {h.get('name', ''): h.get('value', '') for h in headers}
            }
            
        except HttpError as error:
            logger.error(f"Error downloading Gmail attachment {attachment_id}: {error}")
            logger.exception("Download error:")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading attachment: {str(e)}")
            logger.exception("Unexpected error:")
            return None
    
    async def _process_gmail_parts(
        self, 
        parts: List[Dict], 
        user_id: str, 
        email_id: str,
        service: Any = None
    ) -> Tuple[List[Dict], Dict]:
        """
        Process parts of a Gmail message with streaming support.
        
        Args:
            parts: List of message parts from Gmail API
            user_id: ID of the user
            email_id: ID of the email
            service: Optional Gmail service client (if not using global)
            
        Returns:
            Tuple of (attachments, inline_images) where:
            - attachments: List of processed attachments
            - inline_images: Dict of content_id -> image data
        """
        attachments = []
        inline_images = {}
        
        for part in parts:
            # Handle nested parts (multipart/alternative, etc.)
            if 'parts' in part:
                nested_attachments, nested_inline = await self._process_gmail_parts(
                    part['parts'], 
                    user_id, 
                    email_id,
                    service
                )
                attachments.extend(nested_attachments)
                inline_images.update(nested_inline)
                continue
                
            # Skip if no body data or attachment ID
            if 'body' not in part or 'attachmentId' not in part['body']:
                continue
                
            # Process attachment
            try:
                # Generate a unique filename if not provided
                filename = part.get('filename', f"attachment_{uuid.uuid4().hex}")
                mime_type = part.get('mimeType', 'application/octet-stream')
                content_id = ''
                is_inline = False
                
                # Extract headers and check if inline
                headers = {}
                for header in part.get('headers', []):
                    name = header.get('name', '').lower()
                    value = header.get('value', '')
                    headers[name] = value
                    
                    if name == 'content-id':
                        content_id = value.strip('<>')
                    elif name == 'content-disposition':
                        is_inline = 'inline' in value.lower()
                
                # Download the attachment data
                try:
                    attachment_data = await self._download_gmail_attachment(
                        attachment_id=part['body']['attachmentId'],
                        filename=filename,
                        mime_type=mime_type,
                        headers=part.get('headers', []),
                        service=service,
                        message_id=email_id  # Pass the email_id as message_id
                    )
                    
                    if not attachment_data or 'data' not in attachment_data:
                        logger.warning(f"No data received for attachment: {filename}")
                        continue
                        
                    file_data = attachment_data['data']
                    if not file_data:
                        logger.warning(f"Empty data for attachment: {filename}")
                        continue
                    
                    # Determine content type folder based on MIME type
                    mime_type = attachment_data.get('mime_type', 'application/octet-stream').lower()
                    
                    # Map MIME types to folder names
                    content_folder = 'other'
                    if mime_type.startswith('image/'):
                        content_folder = 'images'
                    elif mime_type.startswith('video/'):
                        content_folder = 'videos'
                    elif mime_type.startswith('audio/'):
                        content_folder = 'audio'
                    elif mime_type.startswith('application/pdf'):
                        content_folder = 'documents'
                    elif any(mime_type.startswith(f) for f in ['text/', 'application/msword', 'application/vnd.openxmlformats-officedocument']):
                        content_folder = 'documents'
                    elif mime_type.startswith('application/zip') or 'compressed' in mime_type:
                        content_folder = 'archives'
                    
                    # Generate S3 key with content type folder
                    safe_filename = re.sub(r'[^\w\-_.]', '_', filename)
                    s3_key = f"{user_id}/attachments/{content_folder}/{uuid.uuid4().hex}_{safe_filename}"
                    
                    # Upload to S3
                    try:
                        await self._upload_to_s3(
                            file_data=file_data,
                            s3_key=s3_key,
                            content_type=mime_type
                        )
                        
                        # Generate pre-signed URL
                        url = self._generate_presigned_url(s3_key)
                        
                        # Prepare attachment info
                        attachment_info = {
                            'filename': filename,
                            'mime_type': mime_type,
                            'data': file_data,  # Keep the data for further processing
                            'size': len(file_data),
                            's3_key': s3_key,
                            'url': url,
                            'content_id': content_id,
                            'is_inline': is_inline
                        }
                    except Exception as upload_error:
                        logger.error(f"Failed to upload {filename} to S3: {str(upload_error)}")
                        continue
                        
                except Exception as download_error:
                    logger.error(f"Failed to download attachment {filename}: {str(download_error)}")
                    continue
                
                # Add to appropriate collection
                if is_inline and content_id:
                    inline_images[content_id] = attachment_info
                else:
                    attachments.append(attachment_info)
                    
            except Exception as e:
                logger.error(f"Error processing attachment {filename}: {str(e)}")
                logger.exception("Attachment processing error:")
                continue
                
        return attachments, inline_images
    
    # ===== S3 Utilities =====
    
    async def get_signed_url(self, s3_key: str, expires_in: int = None) -> str:
        """
        Generate a signed URL for an S3 object.
        
        Args:
            s3_key: The S3 object key
            expires_in: URL expiration time in seconds (default: 1 hour)
            
        Returns:
            str: Signed URL or None if generation fails
        """
        try:
            url = self.s3.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.s3_bucket,
                    'Key': s3_key
                },
                ExpiresIn=expires_in or self.url_expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Error generating signed URL for {s3_key}: {str(e)}")
            return None
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(ClientError)
    )
    async def _upload_to_s3(
        self,
        file_data: Union[bytes, BinaryIO],
        s3_key: str,
        content_type: str,
        metadata: Optional[Dict[str, str]] = None,
        size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Upload file data to S3 using streaming for large files.
        
        Args:
            file_data: File data as bytes or file-like object
            s3_key: Destination S3 key
            content_type: MIME type of the file
            metadata: Optional metadata for the S3 object
            size: Size of the file data (required for file-like objects)
            
        Returns:
            Dict with upload details including signed URL
            
        Raises:
            ClientError: If upload to S3 fails
            ValueError: If size is not provided for file-like objects
        """
        if hasattr(file_data, 'read') and size is None:
            raise ValueError("Size must be provided for file-like objects")
            
        file_size = size if size is not None else len(file_data)
        
        try:
            extra_args = {
                'ContentType': content_type,
                'Metadata': metadata or {}
            }
            
            # For small files, use single PUT operation
            if file_size <= self.max_memory_size:
                if hasattr(file_data, 'read'):
                    # If it's a file-like object but small, read it all
                    file_data = file_data.read()
                
                response = self.s3.put_object(
                    Bucket=self.s3_bucket,
                    Key=s3_key,
                    Body=file_data,
                    **extra_args
                )
            else:
                # For large files, use multipart upload
                mpu = self.s3.create_multipart_upload(
                    Bucket=self.s3_bucket,
                    Key=s3_key,
                    **extra_args
                )
                
                parts = []
                part_number = 1
                
                if hasattr(file_data, 'read'):
                    # For file-like objects, read in chunks
                    while True:
                        chunk = file_data.read(self.chunk_size)
                        if not chunk:
                            break
                            
                        part = self.s3.upload_part(
                            Bucket=self.s3_bucket,
                            Key=s3_key,
                            PartNumber=part_number,
                            UploadId=mpu['UploadId'],
                            Body=chunk
                        )
                        
                        parts.append({
                            'PartNumber': part_number,
                            'ETag': part['ETag']
                        })
                        part_number += 1
                else:
                    # For in-memory data, process in chunks
                    for i in range(0, file_size, self.chunk_size):
                        chunk = file_data[i:i + self.chunk_size]
                        part = self.s3.upload_part(
                            Bucket=self.s3_bucket,
                            Key=s3_key,
                            PartNumber=part_number,
                            UploadId=mpu['UploadId'],
                            Body=chunk
                        )
                        
                        parts.append({
                            'PartNumber': part_number,
                            'ETag': part['ETag']
                        })
                        part_number += 1
                
                # Complete the multipart upload
                response = self.s3.complete_multipart_upload(
                    Bucket=self.s3_bucket,
                    Key=s3_key,
                    UploadId=mpu['UploadId'],
                    MultipartUpload={'Parts': parts}
                )
            
            # Get file metadata
            head = self.s3.head_object(Bucket=self.s3_bucket, Key=s3_key)
            
            return {
                's3_key': s3_key,
                'bucket': self.s3_bucket,
                'etag': head.get('ETag', '').strip('"'),
                'content_type': content_type,
                'size': head.get('ContentLength', file_size),
                'signed_url': await self.get_signed_url(s3_key)
            }
            
        except ClientError as e:
            logger.error(f"S3 upload failed for {s3_key}: {str(e)}")
            # Attempt to clean up failed upload
            try:
                if 'mpu' in locals():
                    self.s3.abort_multipart_upload(
                        Bucket=self.s3_bucket,
                        Key=s3_key,
                        UploadId=mpu['UploadId']
                    )
                else:
                    self.s3.delete_object(
                        Bucket=self.s3_bucket,
                        Key=s3_key
                    )
            except Exception as cleanup_error:
                logger.error(f"Failed to clean up failed upload {s3_key}: {str(cleanup_error)}")
            raise
    
    # ===== Cleanup Utilities =====
    
    async def cleanup_temp_files(self) -> None:
        """Clean up temporary files in the temp directory."""
        try:
            for file_path in Path(self.temp_dir).glob('*'):
                try:
                    if file_path.is_file():
                        file_path.unlink()
                except Exception as e:
                    logger.error(f"Failed to delete temp file {file_path}: {str(e)}")
        except Exception as e:
            logger.error(f"Error during temp file cleanup: {str(e)}")


# Singleton instance for easy import
attachment_service = AttachmentService()
