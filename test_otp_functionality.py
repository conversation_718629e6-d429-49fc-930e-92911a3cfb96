#!/usr/bin/env python3
"""
Simple test script to verify the OTP functionality
"""

import asyncio
import sys
import os
import re

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.security import generate_otp
from app.services.user_service import UserService
from app.config.database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_otp_generation():
    """Test OTP generation functionality"""
    logger.info("Testing OTP generation...")
    
    # Generate multiple OTPs to test
    otps = []
    for i in range(10):
        otp = generate_otp()
        otps.append(otp)
        logger.info(f"Generated OTP {i+1}: {otp}")
        
        # Validate OTP format
        assert len(otp) == 6, f"OTP should be 6 digits, got {len(otp)}"
        assert otp.isdigit(), f"OTP should contain only digits, got {otp}"
        assert re.match(r'^\d{6}$', otp), f"OTP should match 6-digit pattern, got {otp}"
    
    # Check that OTPs are unique (though not guaranteed, very likely for small sample)
    unique_otps = set(otps)
    logger.info(f"Generated {len(otps)} OTPs, {len(unique_otps)} unique")
    
    logger.info("OTP generation test passed!")
    return True

async def test_otp_forget_password():
    """Test the forget password functionality with OTP"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        # Test email (replace with a real email for testing)
        test_email = "<EMAIL>"
        
        logger.info(f"Testing forget password OTP functionality with email: {test_email}")
        
        # Call the forget password service
        result = await UserService.forget_password(test_email, db)
        
        logger.info(f"Forget password result: {result}")
        
        # Check if user exists and has OTP
        user = await db.users.find_one({
            "$or": [
                {"email_config.gmail_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.outlook_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.imap_config.email": {"$regex": f"^{test_email}$", "$options": "i"}}
            ]
        })
        
        if user and "reset_otp" in user:
            otp = user["reset_otp"]
            logger.info(f"Found reset OTP in database: {otp}")
            
            # Validate OTP format
            assert len(otp) == 6, f"Stored OTP should be 6 digits, got {len(otp)}"
            assert otp.isdigit(), f"Stored OTP should contain only digits, got {otp}"
            
            logger.info("OTP format validation passed!")
        else:
            logger.warning("No user found or no reset OTP in database")
        
        # Close database connection
        client.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        return {"error": str(e)}

async def test_complete_otp_flow():
    """Test the complete forget password -> reset password flow with OTP"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        test_email = "<EMAIL>"
        new_password = "NewPassword123!"
        
        logger.info("Testing complete OTP password reset flow...")
        
        # Step 1: Request password reset
        logger.info("Step 1: Requesting password reset...")
        forget_result = await UserService.forget_password(test_email, db)
        logger.info(f"Forget password result: {forget_result}")
        
        # Step 2: Get the reset OTP from database (in real scenario, user gets this from email)
        user = await db.users.find_one({
            "$or": [
                {"email_config.gmail_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.outlook_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.imap_config.email": {"$regex": f"^{test_email}$", "$options": "i"}}
            ]
        })
        
        reset_result = None
        if user and "reset_otp" in user:
            reset_otp = user["reset_otp"]
            logger.info(f"Found reset OTP: {reset_otp}")
            
            # Step 3: Reset password using the OTP
            logger.info("Step 2: Resetting password with OTP...")
            reset_result = await UserService.reset_password(test_email, reset_otp, new_password, db)
            logger.info(f"Reset password result: {reset_result}")
        else:
            logger.warning("No reset OTP found in database")
        
        # Close database connection
        client.close()
        
        return {"forget_result": forget_result, "reset_result": reset_result}
        
    except Exception as e:
        logger.error(f"Complete flow test failed with error: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    print("Testing OTP functionality...")
    
    # Test OTP generation
    print("\n=== Testing OTP Generation ===")
    test_otp_generation()
    
    # Test forget password with OTP
    print("\n=== Testing Forget Password with OTP ===")
    result = asyncio.run(test_otp_forget_password())
    print(f"Forget password OTP test result: {result}")
    
    # Test complete flow (uncomment to test with real data)
    print("\n=== Testing Complete OTP Flow ===")
    flow_result = asyncio.run(test_complete_otp_flow())
    print(f"Complete OTP flow test result: {flow_result}")
