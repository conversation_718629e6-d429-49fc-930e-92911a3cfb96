import logging
import json
import asyncio
import traceback
from datetime import datetime, timedelta, timezone
from fastapi import HTTPException
import requests
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.services.outlook_service import OutlookService
from app.services.gmail_notification_service import GmailNotificationService
from app.services.email_service import EmailService
from bson import ObjectId

logger = logging.getLogger(__name__)

class NotificationService:
    _instance = None
    _monitor_task = None

    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.outlook_service = OutlookService()
        self.gmail_notification_service = GmailNotificationService(db)

    async def create_subscription(self, user_id: str, notification_url: str):
        """Create subscription for new email notifications"""
        try:
            logger.info("Received request to create subscription")
            
            # Get user's email from outlook config
            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            if not user or "email_config" not in user or "outlook_config" not in user["email_config"]:
                raise HTTPException(status_code=400, detail="User outlook configuration not found")
            
            user_email = user["email_config"]["outlook_config"].get("email")
            if not user_email:
                raise HTTPException(status_code=400, detail="User email not found in outlook configuration")

            # Find all users with this email in outlook config
            users_with_email = await self.db.users.find({
                "email_config.outlook_config.email": user_email
            }).to_list(None)

            # Check for existing subscriptions for any user with this email
            for other_user in users_with_email:
                existing_subs = await self.db.subscriptions.find({"user_id": str(other_user["_id"])}).to_list(None)
                if existing_subs:
                    logger.info(f"Found {len(existing_subs)} existing subscriptions for email {user_email}")
                    
                    # Delete all existing subscriptions
                    delete_result = await self.delete_subscription(str(other_user["_id"]))
                    logger.info(f"Deleted existing subscriptions for user {other_user['_id']}: {delete_result}")
            
            # Add delay to ensure old subscriptions are fully cleaned up
            await asyncio.sleep(2)
            
            # Monitor task is now handled at application startup
            # if not NotificationService._monitor_task or NotificationService._monitor_task.done():
            #     NotificationService._monitor_task = asyncio.create_task(self.monitor_subscriptions())
            #     logger.info("Started subscription monitoring task")
            print("**********user_id",user_id,"notification_url",notification_url)
            logger.info(f"Creating subscription for user {user_id}")
            logger.debug(f"Notification URL: {notification_url}")
            
            # Use the notification URL as provided
            logger.debug(f"Using notification URL: {notification_url}")
            
            # Get valid access token
            logger.debug("Requesting access token from Outlook service")
            access_token = await self.outlook_service.get_valid_token(self.db, user_id)
            print("access_token----> true 1", access_token)
            logger.info("Successfully obtained access token")
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            logger.debug(f"Using headers: {headers}")

            # First verify the notification endpoint is accessible
            logger.info(f"Verifying notification endpoint accessibility: {notification_url}")
            # try:
            #     # Add headers to potentially bypass any caching/proxy issues
            #     headers = {
            #         'Cache-Control': 'no-cache',
            #         'User-Agent': 'Microsoft-Graph-Notification-Service/1.0'
            #     }
                
            #     # Increased timeout and added verify=False for testing
            #     validation_response = requests.get(
            #         f"{notification_url}?validationToken=TestToken",
            #         headers=headers,
            #         timeout=60,  # Increased timeout
            #         verify=False  # Warning: Only for testing, remove in production
            #     )
                
            #     logger.info(f"Validation response status: {validation_response.status_code}")
            #     logger.info(f"Validation response content: {validation_response.text[:200]}")  # Log first 200 chars
                
            #     if validation_response.status_code != 200:
            #         error_msg = f"Notification endpoint validation failed. Status: {validation_response.status_code}, Response: {validation_response.text[:200]}"
            #         logger.error(error_msg)
            #         raise HTTPException(
            #             status_code=400,
            #             detail=error_msg
            #         )
            # except requests.exceptions.RequestException as e:
            #     error_msg = f"Notification endpoint is not accessible: {str(e)}"
            #     logger.error(f"Validation request failed: {error_msg}")
            #     logger.error(f"Full error details: {repr(e)}")
            #     raise HTTPException(
            #         status_code=400,
            #         detail=error_msg
            #     )

            # Create subscription payload with increased validation timeout
            subscription = {
                "changeType": "created",
                "notificationUrl": notification_url,
                "resource": "/me/mailfolders('inbox')/messages",
                "expirationDateTime": (datetime.utcnow() + timedelta(days=3)).isoformat() + 'Z',
                "clientState": user_id,
                "latestSupportedTlsVersion": "v1_2",  # Add this
                "notificationQueryOptions": "validationTimeoutInMinutes=5"  # Increase validation timeout
            }
            print("--------------subscription payload",subscription)

            # Create subscription
            logger.info("Sending subscription request to Microsoft Graph API")
            logger.debug(f"Subscription payload: {subscription}")
            
            # Create the subscription with increased timeout and retry logic
            max_retries = 3
            retry_delay = 5
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    print("**********headers",headers,"subscription",subscription)
                    logger.info("**********headers",headers,"subscription",subscription)
                    response = requests.post(
                        'https://graph.microsoft.com/v1.0/subscriptions',
                        headers=headers,
                        json=subscription,
                        timeout=300  # 5 minutes timeout
                    )
                    print("**********response",response,response.text)
                    logger.info("**********response",response)
                    if response.status_code == 201:
                        break
                    elif response.status_code == 400 and "ValidationError" in response.text:
                        logger.warning(f"Validation attempt {attempt + 1} failed, retrying...")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay * (attempt + 1))
                            continue
                    last_error = response
                    break
                except requests.exceptions.RequestException as e:
                    last_error = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay * (attempt + 1))
                        continue
                    break
            
            if isinstance(last_error, requests.Response):
                response = last_error
                print("response----->", response,response.text)
                logger.debug(f"Graph API response status: {response.status_code}")
                logger.debug(f"Graph API response: {response.text}")

                if response.status_code != 201:
                    error_msg = f"Failed to create subscription. Status: {response.status_code}, Response: {response.text}"
                    logger.error(error_msg)
                    
                    # If it's a validation error, provide more helpful information
                    if response.status_code == 400 and "ValidationError" in response.text:
                        error_msg += "\n\nValidation failed. Please ensure your notification URL is publicly accessible and responds correctly to validation requests."
                        error_msg += "\nTry testing your endpoint with: curl -v '" + notification_url + "?validationToken=TestToken'"
                    
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=error_msg
                    )

            if isinstance(last_error, Exception):
                error_msg = f"Network error creating subscription: {str(last_error)}"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)

            subscription_data = response.json()
            
            # Store subscription info as a new document with explicit UTC timezone
            expiration = datetime.fromisoformat(subscription_data["expirationDateTime"].rstrip('Z')).replace(tzinfo=timezone.utc)
            await self.db.subscriptions.insert_one({
                "user_id": user_id,
                "subscription_id": subscription_data["id"],
                "expiration_date": expiration.strftime("%Y-%m-%dT%H:%M:%S.000Z"),  # Fixed format with 3 decimal places
                "created_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z"),  # Fixed format
                "notification_url": notification_url,
                "status": "active"
            })

            return subscription_data
            
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create subscription: {str(e)}")

    async def handle_notification(self, notification_data: dict):
        """Handle incoming email notification"""
        try:
            logger.info(f"Received notification: {notification_data}")
            
            # Check if notification data has the expected structure
            if not isinstance(notification_data, dict) or 'value' not in notification_data:
                logger.error("Invalid notification format")
                raise ValueError("Invalid notification format")
            
            # Process each notification in the value array
            for notification in notification_data.get("value", []):
                # Extract client state (user_id) from the notification
                user_id = notification.get("clientState")
                if not user_id:
                    logger.warning("Missing user ID in notification")
                    continue
                    
                # Extract resource data and message ID
                resource_data = notification.get("resourceData", {})
                message_id = resource_data.get("id")
                
                if not message_id:
                    logger.warning("Missing message ID in notification")
                    continue
                    
                logger.info(f"Processing notification for user {user_id}, message {message_id}")
                
                try:
                    # Process the message with attachments using OutlookService
                    email_doc = await self.outlook_service.process_outlook_message(
                        db=self.db,
                        user_id=user_id,
                        message={"id": message_id}
                    )
                    
                    if email_doc:
                        # Notify connected clients about new email via Socket.IO
                        from app.websocket.connection_manager import manager
                        await manager.send_to_user(
                            user_id,
                            'new_email',
                            email_doc
                        )
                        logger.info(f"Email with attachments processed and notification sent for user {user_id}")
                    
                except Exception as e:
                    logger.error(f"Error processing notification for message {message_id}: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Continue processing other notifications even if one fails
                    continue

            return {"status": "success", "message": "Notification processed"}

        except Exception as e:
            logger.error(f"Error handling notification: {str(e)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"Failed to process notification: {str(e)}")


    async def delete_subscription(self, user_id: str):
        """Delete all existing subscriptions for a user"""
        try:
            # Get all subscription details for the user
            subscriptions = await self.db.subscriptions.find({"user_id": user_id}).to_list(None)
            if not subscriptions:
                logger.warning(f"No subscriptions found for user {user_id}")
                return {"status": "success", "message": "No subscriptions found"}

            # Get valid access token
            access_token = await self.outlook_service.get_valid_token(self.db, user_id)
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            deleted_count = 0
            failed_count = 0
            for subscription in subscriptions:
                try:
                    # Delete subscription from Microsoft Graph
                    response = requests.delete(
                        f'https://graph.microsoft.com/v1.0/subscriptions/{subscription["subscription_id"]}',
                        headers=headers
                    )

                    if response.status_code in [204, 404]:  # 204: Success, 404: Already deleted
                        deleted_count += 1
                    else:
                        failed_count += 1
                        logger.error(f"Failed to delete subscription {subscription['subscription_id']}: {response.text}")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error deleting subscription {subscription['subscription_id']}: {str(e)}")

            # Remove all subscriptions from database for this user
            delete_result = await self.db.subscriptions.delete_many({"user_id": user_id})
            
            message = f"Successfully deleted {deleted_count} subscriptions"
            if failed_count > 0:
                message += f", {failed_count} deletions failed"
            
            return {
                "status": "success", 
                "message": message,
                "deleted_count": deleted_count,
                "failed_count": failed_count,
                "total_removed_from_db": delete_result.deleted_count
            }

        except Exception as e:
            logger.error(f"Error deleting subscription: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to delete subscription: {str(e)}")

    async def monitor_subscriptions(self, db):
        """Monitor all subscriptions and renew them before expiration"""
        while True:
            try:
                print("Monitoring subscriptions...")

                # Monitor Outlook subscriptions
                async for subscription in db.subscriptions.find({}):
                    try:
                        expiration_str = subscription["expiration_date"]
                        
                        # Always convert to UTC timezone-aware datetime
                        try:
                            if 'Z' in expiration_str:
                                # Remove Z and add UTC timezone
                                expiration = datetime.fromisoformat(expiration_str.rstrip('Z')).replace(tzinfo=timezone.utc)
                            else:
                                # If no Z, still treat as UTC
                                expiration = datetime.fromisoformat(expiration_str).replace(tzinfo=timezone.utc)
                        except ValueError:
                            # Handle invalid format by parsing with a more flexible approach
                            if '.' in expiration_str:
                                # Truncate microseconds to 6 digits if needed
                                parts = expiration_str.split('.')
                                if len(parts) == 2:
                                    base = parts[0]
                                    micro = parts[1].rstrip('Z')[:6]  # Take up to 6 digits
                                    expiration_str = f"{base}.{micro}"
                                    if 'Z' in parts[1]:
                                        expiration_str += 'Z'
                            
                            # Try parsing again
                            if 'Z' in expiration_str:
                                expiration = datetime.fromisoformat(expiration_str.rstrip('Z')).replace(tzinfo=timezone.utc)
                            else:
                                expiration = datetime.fromisoformat(expiration_str).replace(tzinfo=timezone.utc)
                        
                        # Get current time in UTC
                        now = datetime.now(timezone.utc)
                        time_until_expiry = expiration - now
                        
                        # Renew if expiring within 12 hours
                        if time_until_expiry <= timedelta(hours=24):
                            logger.info(f"Renewing subscription for user {subscription['user_id']}")
                            print(f"Renewing Outlook subscription for user {subscription['user_id']}")
                            await self.renew_subscription(subscription['user_id'])
                    except Exception as e:
                        logger.error(f"Error processing Outlook subscription {subscription.get('subscription_id')}: {str(e)}")

                # Monitor Gmail subscriptions
                # async for gmail_subscription in db.gmail_subscriptions.find({}):
                #     try:
                #         expiration_str = gmail_subscription["expiration"]
                #         print("gmail expiration_str----->", expiration_str)
                #         # Parse expiration date and renew if needed (similar to Outlook)
                #         try:
                #             if 'Z' in expiration_str:
                #                 # Remove Z and add UTC timezone
                #                 expiration = datetime.fromisoformat(expiration_str.rstrip('Z')).replace(tzinfo=timezone.utc)
                #             else:
                #                 # If no Z, still treat as UTC
                #                 expiration = datetime.fromisoformat(expiration_str).replace(tzinfo=timezone.utc)
                #         except ValueError:
                #             # Handle invalid format by parsing with a more flexible approach
                #             if '.' in expiration_str:
                #                 # Truncate microseconds to 6 digits if needed
                #                 parts = expiration_str.split('.')
                #                 if len(parts) == 2:
                #                     base = parts[0]
                #                     micro = parts[1].rstrip('Z')[:6]  # Take up to 6 digits
                #                     expiration_str = f"{base}.{micro}"
                #                     if 'Z' in parts[1]:
                #                         expiration_str += 'Z'

                #             # Try parsing again
                #             if 'Z' in expiration_str:
                #                 expiration = datetime.fromisoformat(expiration_str.rstrip('Z')).replace(tzinfo=timezone.utc)
                #             else:
                #                 expiration = datetime.fromisoformat(expiration_str).replace(tzinfo=timezone.utc)

                #         # Get current time in UTC
                #         now = datetime.now(timezone.utc)
                #         time_until_expiry = expiration - now

                #         if time_until_expiry <= timedelta(hours=24):
                #             user_id = gmail_subscription['user_id']
                #             print(f"Renewing Gmail subscription for user {user_id}")
                #             logger.info(f"Renewing Gmail subscription for user {user_id}")
                #             await self.gmail_notification_service.renew_subscription(user_id)

                #     except Exception as e:
                #         logger.error(f"Error processing Gmail subscription {gmail_subscription.get('id')}: {str(e)}")
                #         continue


                await asyncio.sleep(43200)  # 12 hours in seconds
                print("-------------------------------------------------------------------------------------")

            except Exception as e:
                logger.error(f"Error in subscription monitor: {str(e)}")

    async def renew_subscription(self, user_id: str):
        """Renew subscription before expiration"""
        try:
            subscription = await self.db.subscriptions.find_one({"user_id": user_id})
            if not subscription:
                return
                
            access_token = await self.outlook_service.get_valid_token(self.db, user_id)
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }


            # Update expiration date with proper formatting
            new_expiration = (datetime.utcnow() + timedelta(days=2)).strftime("%Y-%m-%dT%H:%M:%S.000Z")
            response = requests.patch(
                f'https://graph.microsoft.com/v1.0/subscriptions/{subscription["subscription_id"]}',
                headers=headers,
                json={"expirationDateTime": new_expiration}
            )
            print("response----->", response,response.text)
            if response.status_code == 200:
                await self.db.subscriptions.update_one(
                    {"user_id": user_id},
                    {"$set": {"expiration_date": new_expiration}}
                )

        except Exception as e:
            logger.error(f"Error renewing subscription: {str(e)}")
