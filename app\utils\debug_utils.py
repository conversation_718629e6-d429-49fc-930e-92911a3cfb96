import logging
import traceback
import sys
import json
import os
import os.path
from datetime import datetime
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

def log_exception(e: Exception, context: str = "", user_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Log detailed exception information and return a structured error response
    
    Args:
        e: The exception that was raised
        context: Additional context about where the error occurred
        user_id: Optional user ID for tracking
        
    Returns:
        A dictionary with error details that can be returned as a response
    """
    error_id = datetime.now().strftime("%Y%m%d%H%M%S")
    error_message = str(e)
    error_type = type(e).__name__
    
    # Get the full traceback
    exc_type, exc_value, exc_traceback = sys.exc_info()
    stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)
    
    # Log detailed error information
    logger.error(f"Error ID: {error_id}")
    logger.error(f"Context: {context}")
    if user_id:
        logger.error(f"User ID: {user_id}")
    logger.error(f"Error Type: {error_type}")
    logger.error(f"Error Message: {error_message}")
    logger.error("Stack Trace:")
    for line in stack_trace:
        logger.error(line.rstrip())
    
    # Create a structured error response
    error_response = {
        "status": "error",
        "error_id": error_id,
        "error_type": error_type,
        "message": f"An error occurred: {error_message}",
        "detail": error_message
    }
    
    if user_id:
        error_response["user_id"] = user_id
    
    return error_response

def safe_decrypt(fernet, encrypted_data: str, default_value: Optional[str] = None) -> Optional[str]:
    """
    Safely decrypt data with error handling
    
    Args:
        fernet: The Fernet encryption object
        encrypted_data: The encrypted string to decrypt
        default_value: Value to return if decryption fails
        
    Returns:
        Decrypted string or default_value if decryption fails
    """
    if not encrypted_data:
        return default_value
        
    try:
        return fernet.decrypt(encrypted_data.encode()).decode()
    except Exception as e:
        logger.error(f"Decryption error: {str(e)}")
        return default_value

def format_datetime(dt: datetime) -> str:
    """
    Format a datetime object to a consistent ISO format string
    
    Args:
        dt: The datetime object to format
        
    Returns:
        A properly formatted ISO datetime string
    """
    return dt.strftime("%Y-%m-%dT%H:%M:%S.000Z")

def check_google_cloud_setup() -> Dict[str, Any]:
    """
    Check Google Cloud setup for Gmail notifications
    
    Returns:
        A dictionary with status and details about the Google Cloud setup
    """
    result = {
        "status": "unknown",
        "details": {},
        "missing_env_vars": []
    }
    
    # Check required environment variables
    required_vars = [
        "GOOGLE_APPLICATION_CREDENTIALS",
        "GOOGLE_CLOUD_PROJECT_ID",
        "GMAIL_PUBSUB_TOPIC",
        "GMAIL_PUBSUB_SUBSCRIPTION"
    ]
    
    for var in required_vars:
        if not os.environ.get(var):
            result["missing_env_vars"].append(var)
    
    if result["missing_env_vars"]:
        result["status"] = "error"
        result["details"]["message"] = "Missing required environment variables"
        return result
    
    # Check service account credentials file
    creds_path = os.environ.get("GOOGLE_APPLICATION_CREDENTIALS")
    if not os.path.exists(creds_path):
        result["status"] = "error"
        result["details"]["message"] = f"Service account credentials file not found at: {creds_path}"
        return result
    
    # Try to initialize Pub/Sub client
    try:
        from google.cloud import pubsub_v1
        project_id = os.environ.get("GOOGLE_CLOUD_PROJECT_ID")
        topic_name = os.environ.get("GMAIL_PUBSUB_TOPIC")
        
        publisher = pubsub_v1.PublisherClient.from_service_account_file(creds_path)
        topic_path = publisher.topic_path(project_id, topic_name)
        
        # Try to get topic
        try:
            publisher.get_topic(request={"topic": topic_path})
            result["status"] = "success"
            result["details"]["topic_exists"] = True
            result["details"]["topic_path"] = topic_path
        except Exception as e:
            result["status"] = "error"
            result["details"]["message"] = f"Topic does not exist or service account doesn't have access: {str(e)}"
            result["details"]["topic_path"] = topic_path
            return result
            
    except Exception as e:
        result["status"] = "error"
        result["details"]["message"] = f"Error initializing Pub/Sub client: {str(e)}"
        return result
    
    return result

def log_api_request(request_data: Dict[str, Any], user_id: Optional[str] = None) -> None:
    """
    Log API request details for debugging
    
    Args:
        request_data: Dictionary containing request details
        user_id: Optional user ID for tracking
    """
    try:
        # Create a safe copy of the request data for logging
        safe_data = {k: v for k, v in request_data.items() if k not in ['authorization', 'password', 'token']}
        
        log_entry = {
            "timestamp": format_datetime(datetime.now()),
            "request_data": safe_data
        }
        
        if user_id:
            log_entry["user_id"] = user_id
            
        logger.debug(f"API Request: {json.dumps(log_entry)}")
    except Exception as e:
        logger.error(f"Error logging API request: {str(e)}")
