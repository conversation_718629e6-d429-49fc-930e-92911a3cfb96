import logging
from typing import List, Dict, Any
from fastapi import HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime

logger = logging.getLogger(__name__)

class CategoryService:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db

    async def get_categories(self, user_id: str) -> List[Dict[str, Any]]:
        """Fetch categories from user's configuration"""
        try:
            config = await self.db.configurations.find_one({"user_id": user_id})
            if not config or "email_responder" not in config:
                raise HTTPException(status_code=404, detail="Categories configuration not found")
            
            return config["email_responder"].get("categories", [])
        except Exception as e:
            logger.error(f"Error fetching categories: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch categories")

    async def classify_email(self, email_content: str, categories: List[Dict[str, Any]], user_id: str) -> str:
        """
        Classify email content into predefined categories using Claude AI
        
        Args:
            email_content: Content of the email to classify
            categories: List of available categories with their descriptions
            user_id: ID of the user for fetching Claude prompts
        """
        try:
            from app.services.claude_service import ClaudeService
            claude_service = ClaudeService(self.db)
            
            # Format categories for Claude
            category_info = "\n".join([
                f"- {cat['name']}: {cat['description']}" 
                for cat in categories
            ])
            
            # Get classification from Claude with category context
            classification = await claude_service.classify_category(
                email_content, 
                user_id,
                category_info
            )
            
            # Validate and normalize classification
            classification = classification.lower().strip()
            category_names = [cat["name"].lower() for cat in categories]
            
            # Return the classification if valid, otherwise unknown
            return classification if classification in category_names else "unknown"
        except Exception as e:
            logger.error(f"Error classifying email: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to classify email")

    async def update_email_category(self, email_id: str, category: str) -> bool:
        """Update email record with assigned category"""
        try:
            result = await self.db.emails.update_one(
                {"_id": email_id},
                {
                    "$set": {
                        "category": category,
                        "stage": "classify_category",
                        "status": "inprocess",
                        "updated_date": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating email category: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to update email category")
