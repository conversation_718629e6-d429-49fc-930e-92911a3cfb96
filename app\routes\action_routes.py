from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.utils.security import decode_jwt_token
from app.models.action_model import ActionCreate
from app.services.action_service import ActionService
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/actions", tags=["actions"])

@router.post("/", status_code=201)
async def create_action(
    action: ActionCreate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Create a new custom action"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        result = await ActionService.create_action(action, user_id, db)
        
        return {
            "status_code": 201,
            "status": "OK",
            "message": "Action created successfully",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error creating action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/")
async def get_actions(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Get all actions for the current user"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        actions = await ActionService.get_actions(user_id, db)
        
        return {
            "status_code": 200,
            "status": "OK",
            "data": actions
        }
        
    except Exception as e:
        logger.error(f"Error fetching actions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{action_id}")
async def update_action(
    action_id: str,
    action: ActionCreate,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Update an existing action"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        result = await ActionService.update_action(action_id, action, user_id, db)
        
        return {
            "status_code": 200,
            "status": "OK",
            "message": "Action updated successfully",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error updating action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{action_id}")
async def delete_action(
    action_id: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Delete an action"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        result = await ActionService.delete_action(action_id, user_id, db)
        
        return {
            "status_code": 200,
            "status": "OK",
            "message": "Action deleted successfully",
            "data": {"deleted": result}
        }
        
    except Exception as e:
        logger.error(f"Error deleting action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{action_name}/execute/{email_id}")
async def execute_action(
    action_name: str,
    email_id: str,
    custom_payload: Optional[Dict[str, Any]] = None,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Execute a specific action using either custom payload or data from processed email
    
    Args:
        action_name: Name of the action to execute
        email_id: ID of the email associated with the action
        custom_payload: Optional custom payload to use instead of stored email data
        db: Database connection
        authorization: Authorization header
    """
    try:
        # print("hitting")
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        # print("customem payload", custom_payload)
        result = await ActionService.execute_action(action_name, email_id, user_id, db, custom_payload)
        
        return {
            "status_code": 200,
            "status": "OK",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error executing action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
