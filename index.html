<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outlook Authentication</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold mb-6 text-gray-800">Outlook Authentication 02</h1>
        
        <!-- Auth Button -->
        <button 
            id="signInButton"
            onclick="signInWithMicrosoft()"
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
        >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 21 21" class="mr-2">
                <rect x="1" y="1" width="9" height="9" fill="#f25022"/>
                <rect x="1" y="11" width="9" height="9" fill="#00a4ef"/>
                <rect x="11" y="1" width="9" height="9" fill="#7fba00"/>
                <rect x="11" y="11" width="9" height="9" fill="#ffb900"/>
            </svg>
            Sign in with Microsoft 01
        </button>

        <!-- Token Display Section (initially hidden) -->
        <div id="tokenSection" class="mt-6 hidden">
            <h2 class="text-lg font-semibold mb-2 text-gray-800">Access Token</h2>
            <div class="relative">
                <textarea 
                    id="accessTokenDisplay" 
                    class="w-full p-2 border rounded-md h-32 text-xs font-mono bg-gray-50" 
                    readonly
                ></textarea>
                <button 
                    onclick="copyToken()" 
                    class="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs py-1 px-2 rounded"
                >
                    Copy
                </button>
            </div>
            <div class="mt-4">
                <p class="text-sm text-gray-600">Token expires in: <span id="tokenExpiry" class="font-medium"></span></p>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="status" class="mt-4 text-sm"></div>
    </div>

    <script>
        /**
         * Function to show status messages on the UI
         * @param {string} message - The status message to display
         * @param {boolean} isError - Whether the message is an error
         */
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `mt-4 text-sm ${isError ? 'text-red-600' : 'text-green-600'}`;
        }

        /**
         * Function to initiate the Microsoft sign-in process
         */
        function signInWithMicrosoft() {
            showStatus('Redirecting to Microsoft login...');
            // Redirect to the backend auth endpoint
            window.location.href = 'http://localhost:8000/users/outlook/auth';
        }

        /**
         * Function to copy the access token to clipboard
         */
        function copyToken() {
            const tokenElement = document.getElementById('accessTokenDisplay');
            tokenElement.select();
            document.execCommand('copy');
            showStatus('Token copied to clipboard!');
        }

        /**
         * Function to check token info and display it
         */
        async function checkTokenInfo() {
            try {
                const response = await fetch('http://localhost:8000/token-info');
                if (!response.ok) {
                    throw new Error('Failed to fetch token info');
                }
                
                const data = await response.json();
                
                if (data.has_access_token) {
                    // Show the token section
                    document.getElementById('tokenSection').classList.remove('hidden');
                    
                    // Display the token
                    document.getElementById('accessTokenDisplay').value = data.acess_token;
                    
                    // Display expiry info
                    const expiryMinutes = Math.floor(data.time_until_expiry / 60);
                    const expirySeconds = data.time_until_expiry % 60;
                    document.getElementById('tokenExpiry').textContent = 
                        `${expiryMinutes} minutes and ${expirySeconds} seconds`;
                    
                    showStatus('Authentication successful! Access token retrieved.');
                    
                    // Hide the sign in button
                    document.getElementById('signInButton').classList.add('hidden');
                }
            } catch (error) {
                console.error('Error checking token info:', error);
            }
        }

        // Check for callback parameters in URL
        const urlParams = new URLSearchParams(window.location.search);
        // if (urlParams.has('code')) {
        //     showStatus('Processing authentication...');
        //     // Wait a moment for the backend to process the token
        //     setTimeout(checkTokenInfo, 2000);
        // } else {
        //     // Check if we already have a token
        //     checkTokenInfo();
        // }
    </script>
</body>
</html>
