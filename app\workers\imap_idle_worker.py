import asyncio
import logging
from app.services.background_task_manager import BackgroundTaskManager
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Optional
from app.config.database import database
from bson import ObjectId

logger = logging.getLogger(__name__)

class ImapIdleWorker:
    def __init__(self):
        self.db = database
        self.task_manager = BackgroundTaskManager()
        self.running = False
        self._worker_task: Optional[asyncio.Task] = None
        
        # Ensure database is properly initialized
        if self.db is None:
            logger.error("Database connection is None in ImapIdleWorker initialization")
    
    async def start(self):
        """Start the IMAP IDLE worker"""
        print("print function")
        if self.running:
            return
            
        self.running = True
        self._worker_task = asyncio.create_task(self._run())
        print("strtex")
        logger.info("Started IMAP IDLE worker")
    
    async def stop(self):
        """Stop the IMAP IDLE worker"""
        if not self.running:
            return
            
        self.running = False
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        await self.task_manager.cleanup()
        logger.info("Stopped IMAP IDLE worker")
    
    async def _run(self):
        """Main worker loop"""
        try:
            # Keep track of users we're already monitoring by user_id
            monitored_users = set()
            
            # Keep track of email addresses we're monitoring and their associated user_ids
            monitored_emails = {}  # email -> user_id mapping
            
            while self.running:
                try:
                    # Get all active users with IMAP configuration
                    users = await self.db.users.find({
                        "email_config.imap_config": {"$exists": True},
                        "monitoring_active": True  # Only get users explicitly marked as active
                    }).to_list(length=None)
                    # print("users", users)
                    # Track current user IDs and emails for cleanup
                    current_user_ids = set()
                    current_emails = {}  # email -> user_id mapping
                    
                    # Log the number of users found
                    total_users = len(users)
                    new_users_count = 0
                    logger.info(f"Found {total_users} users with IMAP configuration")
                    
                    # First pass: build current email mapping and handle new users
                    for user in users:
                        user_id = str(user["_id"])
                        current_user_ids.add(user_id)
                        
                        # Skip users that are not marked as active for monitoring
                        if "monitoring_active" in user and user["monitoring_active"] is False:
                            logger.info(f"Skipping inactive user {user_id}")
                            continue
                        
                        email_config = user["email_config"]["imap_config"]
                        email_address = email_config.get("email", "").lower()
                        
                        if not email_address:
                            logger.warning(f"User {user_id} has invalid email configuration (missing email address)")
                            continue
                            
                        # Store the current mapping of email to user_id
                        current_emails[email_address] = user_id
                        
                        # Check if this email is already monitored but by a different user
                        # Also check for case-insensitive matches
                        if email_address in monitored_emails and monitored_emails[email_address] != user_id:
                            old_user_id = monitored_emails[email_address]
                            logger.info(f"Email {email_address} is now associated with user {user_id} instead of {old_user_id}")
                            
                            # Stop monitoring the old user
                            try:
                                logger.info(f"Stopping monitoring for previous user {old_user_id} with same email")
                                await self.task_manager.stop_email_monitoring(old_user_id)
                                monitored_users.discard(old_user_id)
                                
                                # Mark old user as inactive in the database
                                await self.db.users.update_one(
                                    {"_id": ObjectId(old_user_id)},
                                    {"$set": {"monitoring_active": False}}
                                )
                                logger.info(f"Marked user {old_user_id} as inactive for monitoring in database")
                                
                                # Find all other users with the same email (case-insensitive) and mark them inactive too
                                await self.db.users.update_many(
                                    {
                                        "_id": {"$ne": ObjectId(user_id)},
                                        "email_config.imap_config.email": {"$regex": f"^{email_address}$", "$options": "i"}
                                    },
                                    {"$set": {"monitoring_active": False}}
                                )
                                logger.info(f"Marked all other users with email {email_address} as inactive (case-insensitive)")
                                
                                # Don't remove from monitored_emails yet as we'll update it below
                            except Exception as e:
                                logger.error(f"Error stopping monitoring for previous user {old_user_id}: {str(e)}")
                    
                    # Second pass: start monitoring for new users or users with changed email
                    for user in users:
                        user_id = str(user["_id"])
                        
                        # Skip users that are not marked as active for monitoring
                        if "monitoring_active" in user and user["monitoring_active"] is False:
                            continue
                            
                        email_config = user["email_config"]["imap_config"]
                        email_address = email_config.get("email", "").lower()
                        
                        if not email_address:
                            continue
                            
                        # Start monitoring if:
                        # 1. User is not monitored yet, or
                        # 2. Email is not monitored yet, or
                        # 3. Email is monitored but by a different user
                        should_monitor = (
                            user_id not in monitored_users or
                            email_address not in monitored_emails or
                            monitored_emails.get(email_address) != user_id
                        )
                        
                        # Ensure this user is marked as active for monitoring
                        if should_monitor:
                            # Mark this user as active in the database
                            await self.db.users.update_one(
                                {"_id": ObjectId(user_id)},
                                {"$set": {"monitoring_active": True}}
                            )
                        
                        if should_monitor:
                            new_users_count += 1
                            logger.info(f"Starting monitoring for user: {user_id} (email: {email_address}, username: {user.get('username', 'unknown')})")
                            try:
                                await self.task_manager.start_email_monitoring(
                                    user_id, email_config, self.db
                                )
                                monitored_users.add(user_id)
                                monitored_emails[email_address] = user_id
                                logger.info(f"Successfully started monitoring for user {user_id}")
                            except Exception as e:
                                logger.error(
                                    f"Error starting monitoring for user {user_id}: {str(e)}"
                                )
                    
                    if new_users_count > 0:
                        logger.info(f"Added {new_users_count} new users to monitoring")
                    
                    # Stop monitoring for users that no longer exist or have removed IMAP config
                    users_to_remove = monitored_users - current_user_ids
                    for user_id in users_to_remove:
                        logger.info(f"Stopping monitoring for removed user: {user_id}")
                        try:
                            await self.task_manager.stop_email_monitoring(user_id)
                            monitored_users.remove(user_id)
                        except Exception as e:
                            logger.error(
                                f"Error stopping monitoring for user {user_id}: {str(e)}"
                            )
                    
                    # Update monitored_emails to only contain current mappings
                    emails_to_remove = set(monitored_emails.keys()) - set(current_emails.keys())
                    for email in emails_to_remove:
                        if email in monitored_emails:
                            del monitored_emails[email]
                    
                    # Wait before next check
                    await asyncio.sleep(60)  # Check every minute for new users
                    
                except Exception as e:
                    logger.error(f"Error in worker loop: {str(e)}")
                    await asyncio.sleep(60)  # Wait before retry
                    
        except asyncio.CancelledError:
            logger.info("Worker loop cancelled")
        except Exception as e:
            logger.error(f"Fatal error in worker loop: {str(e)}")
