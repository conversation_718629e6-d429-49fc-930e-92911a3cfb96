import logging
import secrets
from datetime import datetime, timedelta, timezone
from app.schemas.user_schema import UserCreate
from app.utils.security import get_password_hash, validate_password, verify_password, create_access_token, fernet
from app.config.constant import DEFAULT_EMAIL_RESPONDER
from app.config.agent_config import AGENT_TOOL_MAPPING, SUPPORTED_TOOLS
from app.utils.email_utils import EmailUtils
from bson import ObjectId
from fastapi import HTTPException
from cryptography.fernet import Fernet
# from app.utils.security import get_encryption_key

logger = logging.getLogger(__name__)

class UserService:
    @staticmethod
    async def create_user(user: UserCreate, db):
        try:
            # Check if username already exists
            if await db.users.find_one({"username": user.username}):
                raise HTTPException(status_code=400, detail="Username already taken")
            
            # Validate password
            is_valid, error_msg = validate_password(user.password)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_msg)
            
            # Create user dict with basic required fields
            user_dict = {
                "username": user.username,
                "full_name": user.full_name,
                "password": get_password_hash(user.password)
            }

            # Add email config if provided
            if user.email_config:
                user_dict["email_config"] = {
                    "provider": user.email_config.provider
                }
                
                # Handle Gmail configuration
                if user.email_config.gmail_config:
                    try:
                        encrypted_password = fernet.encrypt(user.email_config.gmail_config.password.encode())
                        user_dict["email_config"]["gmail_config"] = {
                            "email": user.email_config.gmail_config.email,
                            "password": encrypted_password.decode(),
                            "host": user.email_config.gmail_config.host,
                            "port": user.email_config.gmail_config.port
                        }
                    except Exception as e:
                        logger.error(f"Error encrypting Gmail password: {str(e)}")
                        raise HTTPException(status_code=500, detail="Failed to encrypt email password")

            # Handle Outlook configuration if email_config exists
            if user.email_config and user.email_config.outlook_config:
                try:
                    user_dict["email_config"]["outlook_config"] = {
                        "email": user.email_config.outlook_config.email,
                        "access_token": user.email_config.outlook_config.access_token,
                        "refresh_token": user.email_config.outlook_config.refresh_token,
                        "expires_in": user.email_config.outlook_config.expires_in,
                        "token_timestamp": datetime.now(timezone.utc).timestamp()
                    }
                except Exception as e:
                    logger.error(f"Error configuring Outlook: {str(e)}")
                    raise HTTPException(status_code=500, detail="Failed to configure Outlook email")
            
            # Insert user into users collection
            user_result = await db.users.insert_one(user_dict)
            if not user_result.inserted_id:
                raise HTTPException(status_code=500, detail="Failed to create user")
            
            user_id = str(user_result.inserted_id)
            
            

            logger.info(f"User created successfully with id: {user_id}")
            
            return {
                "status_code": 201,
                "status": "OK",
                "message": "User created successfully",
                "data": {
                    "username": user.username,
                    "full_name": user.full_name,
                    "_id": user_id
                }
            }
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise HTTPException(status_code=500, detail="Error creating user")

    

    @staticmethod
    async def update_user(update_data: dict, user_id: str, db):
        """Update user details and email configuration"""
        try:
            # print("update_data:", update_data)
            # Get current user data
            current_user = await db.users.find_one({"_id": ObjectId(user_id)})
            if not current_user:
                raise HTTPException(status_code=404, detail="User not found")

            # Prepare update dictionary
            update_dict = {}

            # Handle basic fields
            if "username" in update_data:
                # Check if new username is already taken
                if await db.users.find_one({"username": update_data["username"], "_id": {"$ne": ObjectId(user_id)}}):
                    raise HTTPException(status_code=400, detail="Username already taken")
                update_dict["username"] = update_data["username"]

            if "full_name" in update_data:
                update_dict["full_name"] = update_data["full_name"]

            # Handle email configuration
            if "email_config" in update_data:
                new_email_config = update_data["email_config"]
                current_email_config = {}  # Start fresh
                
                # Set the new provider
                if "provider" in new_email_config:
                    current_email_config["provider"] = new_email_config["provider"]
                
                # Handle IMAP configuration updates
                if "imap_config" in new_email_config:
                    imap_update = {}
                    imap_config = new_email_config["imap_config"]
                    
                    if imap_config:
                        for field in ["email", "password", "imap_server", "imap_port", "smtp_server", "smtp_port", "use_ssl"]:
                            if field in imap_config:
                                imap_update[field] = imap_config[field]
                        
                        # Encrypt password if provided
                        if "password" in imap_update:
                            imap_update["password"] = fernet.encrypt(imap_update["password"].encode()).decode()
                        
                        current_email_config["imap_config"] = {
                            **(current_email_config.get("imap_config", {})),
                            **imap_update
                        }

                # Handle Gmail configuration updates
                if "gmail_config" in new_email_config:
                    gmail_update = {}
                    gmail_config = new_email_config["gmail_config"]
                    
                    if gmail_config:
                        for field in ["email", "access_token", "refresh_token", "token_uri", 
                                    "client_id", "client_secret", "scopes", "expires_in"]:
                            if field in gmail_config:
                                gmail_update[field] = gmail_config[field]
                        
                        current_email_config["gmail_config"] = {
                            **(current_email_config.get("gmail_config", {})),
                            **gmail_update
                        }
                
                # Handle Outlook configuration updates
                if "outlook_config" in new_email_config:
                    outlook_update = {}
                    outlook_config = new_email_config["outlook_config"]
                    
                    if outlook_config:
                        if "email" in outlook_config:
                            outlook_update["email"] = outlook_config["email"]
                        if "access_token" in outlook_config:
                            # outlook_update["access_token"] = fernet.encrypt(outlook_config["access_token"].encode()).decode()
                            outlook_update["access_token"] = outlook_config["access_token"]
                        if "refresh_token" in outlook_config:
                            # outlook_update["refresh_token"] = fernet.encrypt(outlook_config["refresh_token"].encode()).decode()
                            outlook_update["refresh_token"] = outlook_config["refresh_token"]
                        if "expires_in" in outlook_config:
                            outlook_update["expires_in"] = outlook_config["expires_in"]
                        if "token_timestamp" in outlook_config:
                            outlook_update["token_timestamp"] = outlook_config["token_timestamp"]
                        
                        current_email_config["outlook_config"] = {
                            **(current_email_config.get("outlook_config", {})),
                            **outlook_update
                        }
                
                # Update the email configuration
                update_dict["email_config"] = current_email_config

            if not update_dict:
                raise HTTPException(status_code=400, detail="No valid update data provided")

            # Set monitoring_active to true for this user
            update_dict["monitoring_active"] = True
            
            # Check if there's an email address in the update
            email_address = None
            if "email_config" in update_dict:
                if "imap_config" in update_dict["email_config"] and "email" in update_dict["email_config"]["imap_config"]:
                    email_address = update_dict["email_config"]["imap_config"]["email"].lower()
            
            # If we have an email address, mark other users with the same email as inactive
            duplicate_users = []
            if email_address:
                logger.info(f"Marking other users with email {email_address} as inactive for monitoring")
                # Find all users with the same email address
                users_with_same_email = await db.users.find(
                    {
                        "_id": {"$ne": ObjectId(user_id)},
                        "email_config.imap_config.email": {"$regex": f"^{email_address}$", "$options": "i"}
                    }
                ).to_list(length=None)
                
                # Store their IDs for later use
                duplicate_users = [str(user["_id"]) for user in users_with_same_email]
                
                # Mark them as inactive
                if duplicate_users:
                    logger.info(f"Found {len(duplicate_users)} users with duplicate email: {email_address}")
                    await db.users.update_many(
                        {
                            "_id": {"$ne": ObjectId(user_id)},
                            "email_config.imap_config.email": {"$regex": f"^{email_address}$", "$options": "i"}
                        },
                        {"$set": {"monitoring_active": False}}
                    )
            
            # Update user
            result = await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": update_dict}
            )
            
            # Restart IMAP idle worker for this user if email config was updated
            if "email_config" in update_dict:
                try:
                    from app.services.background_task_manager import BackgroundTaskManager
                    task_manager = BackgroundTaskManager()
                    
                    # First stop monitoring for all duplicate users
                    for dup_user_id in duplicate_users:
                        try:
                            await task_manager.stop_email_monitoring(dup_user_id)
                            logger.info(f"Stopped monitoring for duplicate user {dup_user_id}")
                        except Exception as e:
                            logger.error(f"Error stopping monitoring for duplicate user {dup_user_id}: {str(e)}")
                    
                    # Then restart monitoring for the current user
                    await task_manager.restart_email_monitoring(user_id, db)
                    logger.info(f"Restarted email monitoring for user {user_id}")
                except Exception as e:
                    logger.error(f"Error restarting email monitoring: {str(e)}")
                    # Don't fail the update if monitoring restart fails

            if result.modified_count == 0:
                if result.matched_count > 0:
                    raise HTTPException(status_code=304, detail="No changes were made to the user data")
                raise HTTPException(status_code=404, detail="User not found")

            # Get updated user data
            updated_user = await db.users.find_one(
                {"_id": ObjectId(user_id)},
                {"password": 0}  # Exclude password from response
            )

            # Convert ObjectId to string
            if updated_user and "_id" in updated_user:
                updated_user["_id"] = str(updated_user["_id"])
            
            # Remove sensitive data from response if email config exists
            if "email_config" in updated_user:
                provider = updated_user["email_config"].get("provider")
                
                if "imap_config" in updated_user["email_config"]:
                    # Remove IMAP sensitive data
                    imap_config = updated_user["email_config"]["imap_config"]
                    imap_config.pop("password", None)
                
                if provider == "gmail" and "gmail_config" in updated_user["email_config"]:
                    # Remove Gmail sensitive data
                    gmail_config = updated_user["email_config"]["gmail_config"]
                    gmail_config.pop("access_token", None)
                    gmail_config.pop("refresh_token", None)
                    gmail_config.pop("client_secret", None)
                
                elif provider == "outlook" and "outlook_config" in updated_user["email_config"]:
                    # Remove Outlook tokens
                    outlook_config = updated_user["email_config"]["outlook_config"]
                    outlook_config.pop("access_token", None)
                    outlook_config.pop("refresh_token", None)
            
            return updated_user

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            raise HTTPException(status_code=500, detail="Error updating user")

    @staticmethod
    async def get_user(user_id: str, db):
        try:
            user_dict = await db.users.find_one(
                {"_id": ObjectId(user_id)},
                {"password": 0}  # Exclude password from the response
            )
            if not user_dict:
                logger.warning(f"User not found with id: {user_id}")
                raise HTTPException(status_code=404, detail="User not found")
            
            # Convert ObjectId to string before creating UserInDB instance
            user_dict["_id"] = str(user_dict["_id"])
            
            from app.models.user_model import UserInDB
            user = UserInDB(**user_dict)
            # Create email config without password
            email_config = user_dict.get("email_config", {}).copy()
            if "password" in email_config:
                del email_config["password"]
                
            return {
                "id": str(user_dict["_id"]),
                "username": user.username,
                "full_name": user.full_name,
                "email_config": email_config
            }
        except Exception as e:
            logger.error(f"Error retrieving user: {str(e)}")
            raise HTTPException(status_code=500, detail="Error retrieving user")

    @staticmethod
    async def login_user(user_credentials, db):
        try:
            # Find user by email
            user = await db.users.find_one({"username": user_credentials.username})
            # print("user", user)
            if not user:
                logger.warning(f"Login attempt failed: User not found with username {user_credentials.username}")
                raise HTTPException(
                    status_code=401,
                    detail="Invalid credentials"
                )

            # Verify password
            if not verify_password(user_credentials.password, user["password"]):
                logger.warning(f"Login attempt failed: Invalid password for user {user_credentials.username}")
                raise HTTPException(
                    status_code=401,
                    detail="Invalid credentials"
                )

            # Create access token
            access_token = create_access_token(data={"sub": str(user["_id"])})
            logger.info(f"User {user_credentials.username} logged in successfully")
            
            return {
                    "access_token": access_token,
                    "token_type": "Bearer",
                    "status_code": 200,
                    "status": "OK",
                    "message": "Login successful"
                }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error during login: {str(e)}")
            raise HTTPException(status_code=500, detail="Login failed")

    @staticmethod
    async def forget_password(email: str, db):
        """
        Generate and send password reset token to user's email

        Args:
            email: Email address to send reset token to
            db: Database connection

        Returns:
            Dictionary with status and message

        Raises:
            HTTPException: If user not found or email sending fails
        """
        try:
            logger.info(f"Processing forget password request for email: {email}")

            # Find user by email in different email configurations
            user = None

            # Search in gmail_config
            user = await db.users.find_one({
                "email_config.gmail_config.email": {"$regex": f"^{email}$", "$options": "i"}
            })

            # If not found in gmail_config, search in outlook_config
            if not user:
                user = await db.users.find_one({
                    "email_config.outlook_config.email": {"$regex": f"^{email}$", "$options": "i"}
                })

            # If not found in outlook_config, search in imap_config
            if not user:
                user = await db.users.find_one({
                    "email_config.imap_config.email": {"$regex": f"^{email}$", "$options": "i"}
                })

            if not user:
                logger.warning(f"Forget password attempt failed: User not found with email {email}")
                # For security reasons, don't reveal if email exists or not
                return {
                    "status_code": 200,
                    "status": "OK",
                    "message": "If the email exists in our system, a password reset link has been sent."
                }

            # Generate secure reset token
            reset_token = secrets.token_urlsafe(32)

            # Set token expiration (1 hour from now)
            expiration_time = datetime.now(timezone.utc) + timedelta(hours=1)

            # Store reset token in database
            await db.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "reset_token": reset_token,
                        "reset_token_expires": expiration_time
                    }
                }
            )

            # Send password reset email
            try:
                email_result = EmailUtils.send_password_reset_email(
                    recipient_email=email,
                    reset_token=reset_token
                )
                logger.info(f"Password reset email sent successfully to {email}")
            except Exception as email_error:
                logger.error(f"Failed to send password reset email to {email}: {str(email_error)}")
                # Clean up the reset token if email sending fails
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {
                        "$unset": {
                            "reset_token": "",
                            "reset_token_expires": ""
                        }
                    }
                )
                raise HTTPException(
                    status_code=500,
                    detail="Failed to send password reset email. Please try again later."
                )

            return {
                "status_code": 200,
                "status": "OK",
                "message": "If the email exists in our system, a password reset link has been sent."
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing forget password request: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to process password reset request")

    @staticmethod
    async def reset_password(email: str, token: str, new_password: str, db):
        """
        Reset user password using the provided reset token

        Args:
            email: Email address of the user
            token: Reset token from forget password request
            new_password: New password to set
            db: Database connection

        Returns:
            Dictionary with status and message

        Raises:
            HTTPException: If token is invalid, expired, or user not found
        """
        try:
            logger.info(f"Processing password reset request for email: {email}")

            # Find user by email in different email configurations
            user = None

            # Search in gmail_config
            user = await db.users.find_one({
                "email_config.gmail_config.email": {"$regex": f"^{email}$", "$options": "i"}
            })

            # If not found in gmail_config, search in outlook_config
            if not user:
                user = await db.users.find_one({
                    "email_config.outlook_config.email": {"$regex": f"^{email}$", "$options": "i"}
                })

            # If not found in outlook_config, search in imap_config
            if not user:
                user = await db.users.find_one({
                    "email_config.imap_config.email": {"$regex": f"^{email}$", "$options": "i"}
                })

            if not user:
                logger.warning(f"Password reset attempt failed: User not found with email {email}")
                raise HTTPException(status_code=404, detail="Invalid reset token or email")

            # Check if reset token exists
            if "reset_token" not in user or not user["reset_token"]:
                logger.warning(f"Password reset attempt failed: No reset token found for email {email}")
                raise HTTPException(status_code=400, detail="Invalid reset token")

            # Verify the reset token matches
            if user["reset_token"] != token:
                logger.warning(f"Password reset attempt failed: Invalid token for email {email}")
                raise HTTPException(status_code=400, detail="Invalid reset token")

            # Check if token has expired
            if "reset_token_expires" not in user or not user["reset_token_expires"]:
                logger.warning(f"Password reset attempt failed: No expiration time found for email {email}")
                raise HTTPException(status_code=400, detail="Invalid reset token")

            current_time = datetime.now(timezone.utc)
            token_expires = user["reset_token_expires"]

            # Handle both datetime objects and timestamps
            if isinstance(token_expires, (int, float)):
                token_expires = datetime.fromtimestamp(token_expires, tz=timezone.utc)
            elif hasattr(token_expires, 'replace'):
                # Ensure timezone awareness
                if token_expires.tzinfo is None:
                    token_expires = token_expires.replace(tzinfo=timezone.utc)

            if current_time > token_expires:
                logger.warning(f"Password reset attempt failed: Token expired for email {email}")
                # Clean up expired token
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {
                        "$unset": {
                            "reset_token": "",
                            "reset_token_expires": ""
                        }
                    }
                )
                raise HTTPException(status_code=400, detail="Reset token has expired")

            # Validate the new password
            is_valid, error_msg = validate_password(new_password)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_msg)

            # Hash the new password
            hashed_password = get_password_hash(new_password)

            # Update user password and remove reset token
            result = await db.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "password": hashed_password
                    },
                    "$unset": {
                        "reset_token": "",
                        "reset_token_expires": ""
                    }
                }
            )

            if result.modified_count == 0:
                logger.error(f"Failed to update password for user with email {email}")
                raise HTTPException(status_code=500, detail="Failed to update password")

            logger.info(f"Password reset successful for email: {email}")

            return {
                "status_code": 200,
                "status": "OK",
                "message": "Password has been reset successfully"
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing password reset request: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to process password reset request")
