from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
from app.config.database import get_db
from app.services.email_service import EmailService
from app.utils.security import decode_jwt_token
from datetime import datetime
import logging

router = APIRouter(prefix="/drafts", tags=["drafts"])
logger = logging.getLogger(__name__)

@router.post("/{email_id}")
async def generate_response(
    email_id: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Generate response and create draft for an email
    """
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
            
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        
        # Get email details including action executions
        email = await db.emails.find_one({"_id": ObjectId(email_id)})
        if not email:
            raise HTTPException(status_code=404, detail="Email not found")
            
        # Initialize Claude service
        from app.services.claude_service import ClaudeService
        claude_service = ClaudeService(db)
        
        # Get email category and sentiment
        category = email.get("category", "unknown")
        sentiment = email.get("sentiment", {})
        
        # Build context with action executions
        context = {
            "email_data": email
        }
        
        # Generate response using Claude
        response_text = await claude_service.generate_response(
            email["body"],
            category,
            sentiment,
            context,
            user_id
        )
        
        # Store response in email collection
        current_time = datetime.utcnow()
        await db.emails.update_one(
            {"_id": ObjectId(email_id)},
            {
                "$set": {
                    "response": response_text,
                    "status": "drafted",
                    "stage": "response_generated",
                    "updated_date": current_time
                }
            }
        )

        # Create draft on email server and in database
        logger.info(f"Attempting to create draft for email_id: {email_id}")
        
        # Get user's email configuration
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        if not user or "email_config" not in user:
            raise HTTPException(status_code=404, detail="User email configuration not found")
            
        email_config = user["email_config"]
        
        # Create draft using EmailService
        try:
            # Log draft creation parameters
            logger.debug(f"Draft creation parameters:")
            logger.debug(f"- Email ID: {email_id}")
            logger.debug(f"- User ID: {user_id}")
            logger.debug(f"- Response text length: {len(response_text)}")
            logger.debug(f"- First 100 chars of response: {response_text[:100]}...")
                
            # Get user details for additional context
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if user and "email_config" in user:
                logger.debug(f"User email provider: {user['email_config'].get('provider', 'unknown')}")
                
            draft_id = await EmailService.create_draft(
                db,
                user_id,
                email_id,
                response_text,
                update_existing=False
            )
            logger.info(f"Draft created successfully with ID: {draft_id}")
        except Exception as e:
            logger.error("Draft creation failed with detailed error info:")
            error_details = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "error_args": getattr(e, 'args', []),
                "response_length": len(response_text),
                "user_id": user_id,
                "email_id": email_id
            }
            
            # Log email details if available
            try:
                email = await db.emails.find_one({"_id": ObjectId(email_id)})
                if email:
                    error_details.update({
                        "email_subject": email.get('subject', 'N/A'),
                        "email_sender": email.get('sender', 'N/A'),
                        "email_provider": user.get('email_config', {}).get('provider', 'N/A'),
                        "email_status": email.get('status', 'N/A')
                    })
            except Exception as email_error:
                logger.error(f"Failed to fetch email details: {str(email_error)}")
            
            # Log all error details in a structured way
            for key, value in error_details.items():
                logger.error(f"{key}: {value}")
                
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to create draft: {str(e)}"
            )
        
        # Get the created draft details
        draft = await db.drafts.find_one({"_id": ObjectId(draft_id)})
        
        return {
            "status_code": 201,
            "status": "OK", 
            "message": "Response generated and draft created successfully",
            "data": {
                "draft_id": draft_id,
                "draft_server_id": draft.get("draft_server_id"),
                "response": response_text,
                "action_executions": email.get("action_executions")
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating draft: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{email_id}/regenerate")
async def regenerate_draft(
    email_id: str,
    feedback: dict,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Regenerate draft response for an email and update it in the email server
    """
    try:
        logger.info(f"Starting draft regeneration for email_id: {email_id}")
        logger.debug(f"Received feedback: {feedback}")
        
        if not authorization:
            logger.error("Authorization header missing")
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        if not feedback or "content" not in feedback:
            logger.error("Invalid feedback format received")
            raise HTTPException(status_code=400, detail="Feedback content is required")
        
        logger.debug("Decoding JWT token")
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        logger.info(f"User ID extracted from token: {user_id}")
        
        logger.info("Calling EmailService.regenerate_draft")
        result = await EmailService.regenerate_draft(db, user_id, email_id, feedback["content"])
        logger.info("Draft regeneration completed successfully")
        
        return {
            "status_code": 200,
            "status": "OK",
            "message": "Draft response regenerated successfully",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating draft: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{email_id}")
async def update_draft(
    email_id: str,
    draft_content: dict,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Update draft response with manually edited content
    """
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        result = await EmailService.update_draft(
            db, 
            user_id, 
            email_id, 
            draft_content.get("content"),
            create_new=True
        )
        
        return {
            "status_code": 200,
            "status": "OK",
            "message": "Draft response updated successfully",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating draft: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{email_id}")
async def delete_draft(
    email_id: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """
    Delete draft response from email server and update database
    """
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        user_id = decode_jwt_token(authorization.replace("Bearer ", ""))
        result = await EmailService.delete_draft(db, user_id, email_id)
        
        return {
            "status_code": 200,
            "status": "OK",
            "message": "Draft deleted successfully",
            "data": {"success": result}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting draft: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
