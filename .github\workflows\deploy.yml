name: Deploy Python to EC2 with Docker

on:
  workflow_dispatch:
  push:
    branches:
      - ft_integration
  pull_request:
    branches:
      - ft_integration

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/ft_integration'

    steps:
      - name: Deploy to EC2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USERNAME }}
          key: ${{ secrets.EC2_SSH_PRIVATE_KEY }}
          script: |

            # Clone or pull latest deployment branch
            cd process-builder/deploy
            ls -lah
            if [ -d 'email-responder-backend' ]; then
              # cd email-responder-backend
              # git fetch origin ft_integration
              # git reset --hard origin/ft_integration
              rm -rf email-responder-backend
              git clone --branch ft_integration https://${{ secrets.ACCESS_TOKEN }}@github.com/${{ github.repository }}.git email-responder-backend
              cd email-responder-backend
            else
              git clone --branch ft_integration https://${{ secrets.ACCESS_TOKEN }}@github.com/${{ github.repository }}.git email-responder-backend
              cd email-responder-backend
            fi
            
            # Build and deploy with Docker
            docker build -t email-responder-backend .
            docker build -t email-responder-worker -f Dockerfile.worker .
            cd ../../
            ls -lah
            docker-compose down
            docker-compose up -d
            docker-compose ps

  close-issues:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/ft_integration'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Close Issues and Update Project Board
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.ACCESS_TOKEN }}
          script: |
            const repo = context.repo;
            
            // Get all commits in this push
            const { data: commits } = await github.rest.repos.listCommits({
              owner: repo.owner,
              repo: repo.repo,
              sha: context.sha
            });
            
            // Extract unique issue numbers from all commit messages
            const issueSet = new Set();
            for (const commit of commits) {
              const matches = commit.commit.message.match(/#(\d+)/g) || [];
              matches.forEach(match => {
                issueSet.add(parseInt(match.substring(1)));
              });
            }
            
            // Close each unique issue
            for (const issueNumber of issueSet) {
              try {
                // Close the issue
                await github.rest.issues.update({
                  owner: repo.owner,
                  repo: repo.repo,
                  issue_number: issueNumber,
                  state: 'closed'
                });
                
                // Add a comment mentioning the closing commit
                await github.rest.issues.createComment({
                  owner: repo.owner,
                  repo: repo.repo,
                  issue_number: issueNumber,
                  body: `Closed by deployment of commit ${context.sha}`
                });
              
                console.log(`Closed issue #${issueNumber}`);
              } catch (error) {
                console.error(`Error processing issue #${issueNumber}:`, error);
              }
            }
