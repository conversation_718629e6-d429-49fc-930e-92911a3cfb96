import asyncio
import logging
from imapclient import IMAPClient
from datetime import datetime, timezone
from typing import Dict, Optional
from app.utils.security import fernet
from fastapi import H<PERSON><PERSON>Exception
from bson import ObjectId

logger = logging.getLogger(__name__)

class ImapIdleService:
    def __init__(self):
        self._connections: Dict[str, IMAPClient] = {}
        self._idle_tasks: Dict[str, asyncio.Task] = {}
        self.db = None
        self._recently_processed_emails = {}  # user_id -> {message_id: timestamp}
        
    async def start_idle_connection(self, user_id: str, email_config: dict, db=None):
        """Start IMAP IDLE connection for a user"""
        try:
            if user_id in self._connections:
                await self.stop_idle_connection(user_id)
            
            # Store database connection
            self.db = db
                
            # Decrypt password
            decrypted_password = fernet.decrypt(email_config["password"].encode()).decode()
            
            # Determine IMAP server and settings
            email_domain = email_config["email"].lower().split('@')[-1]
            is_gmail = "gmail.com" == email_domain or "googlemail.com" == email_domain
            imap_server = "imap.gmail.com" if is_gmail else email_config.get("imap_server", email_config.get("host"))
            
            # Create connection with SSL for Gmail
            connection = IMAPClient(imap_server, use_uid=True, ssl=True)
            
            # Special handling for Gmail
            if is_gmail:
                # Gmail requires SSL and specific login approach
                connection.login(email_config["email"], decrypted_password)
                
                # Gmail requires 'INBOX' in all caps
                connection.select_folder('INBOX')
                
                # For Gmail, set up a search criteria to find recent emails
                # This helps with Gmail's IDLE implementation which can be unreliable
                logger.info(f"Set up Gmail-specific handling for {user_id}")
                
                # Initialize last check time for this user
                if not hasattr(self, '_last_gmail_check'):
                    self._last_gmail_check = {}
                self._last_gmail_check[user_id] = datetime.now().timestamp()
            else:
                # Standard IMAP login for other providers
                connection.login(email_config["email"], decrypted_password)
                connection.select_folder('INBOX')
            
            self._connections[user_id] = connection
            
            # Start IDLE task
            self._idle_tasks[user_id] = asyncio.create_task(
                self._idle_loop(user_id, email_config)
            )
            print(f"Started IMAP IDLE connection for user {user_id}")
            
            logger.info(f"Started IMAP IDLE connection for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error starting IMAP IDLE connection: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to start IMAP monitoring: {str(e)}"
            )
    
    async def stop_idle_connection(self, user_id: str):
        """Stop IMAP IDLE connection for a user"""
        try:
            # Cancel idle task if exists
            if user_id in self._idle_tasks:
                self._idle_tasks[user_id].cancel()
                del self._idle_tasks[user_id]
            
            # Close connection if exists
            if user_id in self._connections:
                try:
                    self._connections[user_id].close()
                    self._connections[user_id].logout()
                except:
                    pass
                del self._connections[user_id]
            
            # Clean up recently processed emails for this user
            if user_id in self._recently_processed_emails:
                del self._recently_processed_emails[user_id]
                
            logger.info(f"Stopped IMAP IDLE connection for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error stopping IMAP IDLE connection: {str(e)}")
    
    async def _debug_connection(self, user_id: str):
        """Debug IMAP connection issues"""
        if user_id not in self._connections:
            logger.error(f"No connection found for user {user_id}")
            return
            
        connection = self._connections[user_id]
        try:
            # Check connection status
            status = await asyncio.get_event_loop().run_in_executor(
                None, connection.noop
            )
            logger.info(f"Connection status for {user_id}: {status}")
            
            # List folders
            folders = await asyncio.get_event_loop().run_in_executor(
                None, connection.list_folders
            )
            logger.info(f"Available folders for {user_id}: {folders}")
            
            # Check INBOX status
            select_info = await asyncio.get_event_loop().run_in_executor(
                None, lambda: connection.select_folder('INBOX')
            )
            logger.info(f"INBOX status for {user_id}: {select_info}")
            
            # Get message count
            msg_count = select_info[b'EXISTS']
            logger.info(f"Message count in INBOX for {user_id}: {msg_count}")
            
            return True
        except Exception as e:
            logger.error(f"Debug connection error for {user_id}: {str(e)}")
            return False
    
    async def _idle_loop(self, user_id: str, email_config: dict):
        """Main IDLE loop for monitoring emails"""
        try:
            # Check if user is still active for monitoring
            if self.db is not None:
                user = await self.db.users.find_one({"_id": ObjectId(user_id)})
                if user is not None and "monitoring_active" in user and user["monitoring_active"] is False:
                    logger.info(f"User {user_id} is now inactive, stopping IDLE loop")
                    await self.stop_idle_connection(user_id)
                    return
            
            connection = self._connections[user_id]
            
            while True:
                try:
                    # Check if user is still active before continuing the loop
                    if self.db is not None:
                        user = await self.db.users.find_one({"_id": ObjectId(user_id)})
                        if user is not None and "monitoring_active" in user and user["monitoring_active"] is False:
                            logger.info(f"User {user_id} is now inactive, stopping IDLE loop")
                            await self.stop_idle_connection(user_id)
                            return
                    
                    logger.info(f"Starting idle loop for user {user_id}")
                    # Check connection is still alive before starting IDLE
                    try:
                        # Perform a NOOP command to check connection
                        await asyncio.get_event_loop().run_in_executor(
                            None, connection.noop
                        )
                    except Exception as e:
                        logger.warning(f"Connection check failed for {user_id}: {str(e)}")
                        # Reconnect and continue
                        await self.start_idle_connection(user_id, email_config, self.db)
                        continue
                    
                    # Start IDLE mode
                    idle_start = await asyncio.get_event_loop().run_in_executor(
                        None, connection.idle)
                    
                    # Wait for new email notifications
                    try:
                        # For Gmail, use shorter timeout to prevent connection drops
                        is_gmail = "gmail.com" in email_config["email"].lower()
                        timeout = 20 if is_gmail else 30  # Gmail needs more frequent refreshes
                        
                        # Wait for IDLE responses
                        responses = await asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: connection.idle_check(timeout=timeout)
                        )
                        print(f"IDLE check responses for {email_config['email']}: {responses}")
                        
                        # For Gmail, we need to check for new emails even if no EXISTS response
                        # Gmail sometimes doesn't send EXISTS notifications properly
                        email_domain = email_config["email"].lower().split('@')[-1]
                        is_gmail = "gmail.com" == email_domain or "googlemail.com" == email_domain
                        
                        # Check for new mail notifications
                        has_new_mail = any(
                            response[1] == b'EXISTS' 
                            for response in (responses or [])
                        )
                        
                        # For Gmail, periodically check for new emails regardless of IDLE response
                        if is_gmail:
                            # Get current time
                            current_time = datetime.now().timestamp()
                            
                            # Initialize last check time if not exists
                            if not hasattr(self, '_last_gmail_check'):
                                self._last_gmail_check = {}
                            
                            # Check every 30 seconds for Gmail accounts
                            if (user_id not in self._last_gmail_check or 
                                current_time - self._last_gmail_check.get(user_id, 0) > 30):
                                
                                print(f"Forcing Gmail check for {email_config['email']}")
                                has_new_mail = True  # Force check for new emails
                                self._last_gmail_check[user_id] = current_time
                        
                        if has_new_mail:
                            print("new email detected")
                            # Process new emails
                            from app.services.email_service import EmailService
                            from app.websocket.connection_manager import manager
                            from app.services.imap_service import ImapService
                            
                            try:
                                # Use ImapService.fetch_emails instead of _fetch_latest_email
                                print(f"Fetching new emails for user {user_id}")
                                
                                # Create email_config dictionary for ImapService
                                email_domain = email_config["email"].lower().split('@')[-1]
                                is_gmail = "gmail.com" == email_domain or "googlemail.com" == email_domain
                                
                                imap_config = {
                                    "imap_config": {
                                        "email": email_config["email"].lower(),  # Normalize email to lowercase
                                        "password": email_config["password"],
                                        "imap_server": "imap.gmail.com" if is_gmail else email_config.get("imap_server", email_config.get("host")),
                                        "imap_port": 993 if is_gmail else email_config.get("imap_port", 993),
                                        "use_ssl": True,  # Always use SSL for Gmail
                                        "is_gmail": is_gmail  # Add flag to indicate Gmail account
                                    }
                                }
                                
                                # For Gmail, fetch more emails to ensure we don't miss any
                                email_domain = email_config["email"].lower().split('@')[-1]
                                is_gmail = "gmail.com" == email_domain or "googlemail.com" == email_domain
                                
                                # Fetch more emails for Gmail (3 instead of 1)
                                max_results = 3 if is_gmail else 1
                                new_emails = await ImapService.fetch_emails(imap_config, max_results=max_results)
                                
                                if new_emails is not None and len(new_emails) > 0 and self.db is not None:
                                    for new_email in new_emails:
                                        # Add required fields
                                        new_email["status"] = "unprocessed"
                                        
                                        # Check if email already exists globally before storing
                                        message_id = new_email.get('message_id')
                                        existing_email = None
                                        if message_id is not None:
                                            existing_email = await self.db.emails.find_one({
                                                "message_id": message_id
                                            })
                                        
                                        # Check if we've recently processed this email for this user
                                        message_id = new_email.get('message_id')
                                        current_time = datetime.now().timestamp()
                                        
                                        # Initialize user's recently processed emails dict if needed
                                        if user_id not in self._recently_processed_emails:
                                            self._recently_processed_emails[user_id] = {}
                                            
                                        # Check if we've seen this message recently (within last 60 seconds)
                                        recently_seen = False
                                        if message_id in self._recently_processed_emails[user_id]:
                                            last_seen = self._recently_processed_emails[user_id][message_id]
                                            if current_time - last_seen < 60:  # 60 second cooldown
                                                recently_seen = True
                                                logger.info(f"Ignoring recently processed email {message_id} for user {user_id}")
                                        
                                        # Only process if not recently seen
                                        if not recently_seen:
                                            # Update the recently processed timestamp
                                            self._recently_processed_emails[user_id][message_id] = current_time
                                            
                                            # Clean up old entries (older than 5 minutes)
                                            cleanup_time = current_time - 300  # 5 minutes
                                            for msg_id in list(self._recently_processed_emails[user_id].keys()):
                                                if self._recently_processed_emails[user_id][msg_id] < cleanup_time:
                                                    del self._recently_processed_emails[user_id][msg_id]
                                            
                                            if existing_email is None:
                                                # Store the email
                                                print(f"Storing new email for user {user_id}: {new_email.get('subject', '')}")
                                                await EmailService.store_new_email(
                                                    self.db, user_id, new_email
                                                )
                                                print(f"Email stored successfully for user {user_id}")
                                            else:
                                                print(f"Skipping duplicate email for user {user_id}: {new_email.get('subject', '')}")
                                            
                                            # Fetch the stored email from database to get complete data
                                            stored_email = await self.db.emails.find_one({
                                                "message_id": new_email.get('message_id'),
                                                "user_id": user_id
                                            })
                                            
                                            if stored_email:
                                                # Convert ObjectId to string for JSON serialization
                                                stored_email['_id'] = str(stored_email['_id'])
                                                
                                                # Convert datetime objects to ISO format strings
                                                if 'date' in stored_email and isinstance(stored_email['date'], datetime):
                                                    stored_email['date'] = stored_email['date'].isoformat()
                                                if 'fetched_date' in stored_email and isinstance(stored_email['fetched_date'], datetime):
                                                    stored_email['fetched_date'] = stored_email['fetched_date'].isoformat()
                                                
                                                # Notify connected clients via websocket with stored data
                                                await manager.send_to_user(
                                                    user_id,
                                                    'new_email',
                                                    stored_email
                                                )
                                        logger.info(f"New email notification sent to user {user_id}")
                                elif self.db is None:
                                    logger.error("Database connection not available")
                                    print("Database connection not available")
                                else:
                                    logger.info("No new emails found to process")
                                    print("No new emails found to process")
                            except Exception as e:
                                logger.error(f"Error processing new emails: {str(e)}")
                                print(f"Error processing new emails: {str(e)}")
                    finally:
                        # End IDLE mode
                        await asyncio.get_event_loop().run_in_executor(
                            None,
                            connection.idle_done
                        )
                    
                except (ConnectionError, TimeoutError) as e:
                    # Check if user is still active before reconnecting
                    if self.db is not None:
                        user = await self.db.users.find_one({"_id": ObjectId(user_id)})
                        if user is not None and "monitoring_active" in user and user["monitoring_active"] is False:
                            logger.info(f"User {user_id} is now inactive, not reconnecting")
                            await self.stop_idle_connection(user_id)
                            return
                    
                    # For Gmail, we need to handle connection errors more gracefully
                    email_domain = email_config["email"].lower().split('@')[-1]
                    is_gmail = "gmail.com" == email_domain or "googlemail.com" == email_domain
                    
                    if is_gmail:
                        logger.info(f"Gmail connection error for user {user_id}: {str(e)}, attempting to reconnect")
                        # Close the old connection properly
                        try:
                            if user_id in self._connections:
                                try:
                                    self._connections[user_id].idle_done()
                                except:
                                    pass
                                try:
                                    self._connections[user_id].logout()
                                except:
                                    pass
                        except Exception as logout_error:
                            logger.warning(f"Error during Gmail connection cleanup: {str(logout_error)}")
                    else:
                        logger.info(f"Connection error for user {user_id}: {str(e)}, attempting to reconnect")
                    
                    # Wait a bit before reconnecting (longer for Gmail to avoid rate limits)
                    await asyncio.sleep(15 if is_gmail else 2)  # Longer delay for Gmail to avoid rate limits
                    
                    # Remove old connection from the dictionary
                    if user_id in self._connections:
                        del self._connections[user_id]
                    
                    # For Gmail, try to fetch emails directly before reconnecting
                    # This ensures we don't miss emails during reconnection
                    if is_gmail and self.db is not None:
                        try:
                            logger.info(f"Fetching Gmail emails directly during reconnection for {user_id}")
                            from app.services.gmail_service import GmailService
                            
                            gmail_service = GmailService()
                            await gmail_service.fetch_recent_emails(self.db, user_id, max_results=5)
                            logger.info(f"Successfully fetched Gmail emails during reconnection for {user_id}")
                        except Exception as fetch_error:
                            logger.error(f"Error fetching Gmail emails during reconnection: {str(fetch_error)}")
                    
                    # Start a new connection
                    await self.start_idle_connection(user_id, email_config, self.db)
                    break
                    
                except Exception as e:
                    logger.error(f"Error in IDLE loop: {str(e)}")
                    await asyncio.sleep(5)  # Wait before retry
                    
        except asyncio.CancelledError:
            logger.info(f"IDLE loop cancelled for user {user_id}")
        except Exception as e:
            logger.error(f"Fatal error in IDLE loop: {str(e)}")
    
    
