import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON><PERSON>xt
from fastapi import HTTP<PERSON>x<PERSON>
from cryptography.fernet import Fernet, InvalidToken
import re
import os
from dotenv import load_dotenv

load_dotenv()

# Create a Fernet key for encryption/decryption
FERNET_KEY = os.getenv("FERNET_KEY", Fernet.generate_key())
# print(FERNET_KEY.decode())
fernet = Fernet(FERNET_KEY)

# Configure logging
logger = logging.getLogger(__name__)

# JWT Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 3600

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")



def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

def decrypt_password(encrypted_password: str, allow_reencrypt: bool = False) -> str:
    """
    Decrypt an encrypted password
    
    Args:
        encrypted_password: The encrypted password string
        allow_reencrypt: If True, attempts to re-encrypt if token is invalid
    """
    try:
        logger.debug(f"Starting password decryption process")
        logger.debug(f"Encrypted password type: {type(encrypted_password)}")
        logger.debug(f"Encrypted password length: {len(encrypted_password) if encrypted_password else 'None'}")
        
        if not encrypted_password:
            logger.error("Empty encrypted password provided")
            raise ValueError("Empty encrypted password")
            
        # Ensure we're working with bytes
        if isinstance(encrypted_password, str):
            try:
                encrypted_bytes = encrypted_password.encode()
                logger.debug(f"Successfully encoded string to bytes, length: {len(encrypted_bytes)}")
            except Exception as encode_error:
                logger.error(f"Error encoding password string to bytes: {str(encode_error)}")
                raise ValueError(f"Password encoding failed: {str(encode_error)}")
        else:
            encrypted_bytes = encrypted_password
            logger.debug("Using provided bytes directly")
            
        try:
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            logger.debug("Fernet decryption successful")
        except InvalidToken as token_error:
            logger.error(f"Invalid Fernet token error: {str(token_error)}")
            logger.error(f"Token length: {len(encrypted_bytes)}")
            
            if allow_reencrypt:
                try:
                    # Attempt to treat the encrypted_password as plain text
                    logger.info("Attempting to re-encrypt password")
                    new_encrypted = fernet.encrypt(encrypted_password.encode())
                    return decrypt_password(new_encrypted.decode(), allow_reencrypt=False)
                except Exception as re_encrypt_error:
                    logger.error(f"Re-encryption failed: {str(re_encrypt_error)}")
                    
            raise HTTPException(
                status_code=500, 
                detail="Invalid encryption token - password needs to be re-encrypted"
            )
            
        try:
            decrypted_password = decrypted_bytes.decode()
            logger.debug("Successfully decoded decrypted bytes to string")
            return decrypted_password
        except UnicodeDecodeError as decode_error:
            logger.error(f"Error decoding decrypted bytes: {str(decode_error)}")
            raise ValueError(f"Password decode failed: {str(decode_error)}")
            
    except HTTPException:
        raise
    except ValueError as ve:
        logger.error(f"Validation error in decrypt_password: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Unexpected error in decrypt_password: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.exception(e)
        raise HTTPException(
            status_code=500, 
            detail=f"Password decryption failed: {type(e).__name__} - {str(e)}"
        )

def validate_password(password: str) -> tuple[bool, Optional[str]]:
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r"[A-Z]", password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r"[a-z]", password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r"\d", password):
        return False, "Password must contain at least one number"
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        return False, "Password must contain at least one special character"
    return True, None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    current_time = datetime.utcnow()
    
    if expires_delta:
        expire = current_time + expires_delta
    else:
        expire = current_time + timedelta(days=30)
    
    to_encode.update({
        "exp": expire,
        "iat": current_time  # Add issued at time
    })
    
    logger.info(f"Creating token with: Current time (UTC): {current_time}")
    logger.info(f"Token will expire at (UTC): {expire}")
    logger.info(f"Token expiration delta: {expire - current_time}")
    print("SECRET_KEY",SECRET_KEY)
    
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    logger.info(f"Token created successfully. First 20 chars: {encoded_jwt[:20]}...")
    return encoded_jwt


def decode_jwt_token(token: str) -> str:
    """Decode JWT token and return user_id"""
    try:
        current_time = datetime.utcnow()
        logger.info(f"Attempting to decode token at (UTC): {current_time}")
        logger.info(f"Token to decode (first 20 chars): {token[:20]}...")
        logger.info(f"Using SECRET_KEY (first 5 chars): {SECRET_KEY[:5]}...")
        logger.info(f"Using ALGORITHM: {ALGORITHM}")
        
        # First decode without verification to check expiration time
        unverified_payload = jwt.decode(token,SECRET_KEY, options={"verify_signature": False})
        exp_timestamp = datetime.fromtimestamp(unverified_payload.get("exp"))
        iat_timestamp = datetime.fromtimestamp(unverified_payload.get("iat"))
        
        logger.info(f"Token was issued at (UTC): {iat_timestamp}")
        logger.info(f"Token expires at (UTC): {exp_timestamp}")
        logger.info(f"Time until expiration: {exp_timestamp - current_time}")
        
        # Now do the actual verification
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        logger.info(f"Token successfully decoded. Payload: {payload}")
        
        user_id = payload.get("sub")
        if user_id is None:
            logger.error("No 'sub' claim found in token payload")
            raise HTTPException(status_code=401, detail="Invalid token: Missing user ID claim")
        
        logger.info(f"Successfully extracted user_id: {user_id}")
        return user_id
        
    except JWTError as e:
        logger.error(f"JWT decode error: {str(e)}")
        logger.error(f"Full token for debugging: {token}")
        raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during token decode: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error details: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Token validation failed: {str(e)}")
