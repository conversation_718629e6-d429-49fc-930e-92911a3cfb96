from typing import Dict, Any, Optional
from datetime import datetime, timezone # Added timezone
from bson import ObjectId
import re

def build_base_query(user_id: str, current_email: str, exclude: Optional[str] = None) -> Dict[str, Any]:
    """Build the base query filter for emails"""
    # <AUTHOR> <EMAIL>"
    import re
    
    def extract_email(addr: str) -> str:
        """Helper to extract email from address with display name"""
        email_match = re.search(r'<([^>]+)>|([^\s]+)', addr)
        return email_match.group(1) or email_match.group(2) if email_match else addr
    
    # Clean current email
    clean_email = extract_email(current_email)
    
    # Build recipient query that matches both plain email and display name format
    query = {
        "$or": [
            {"recipient": {"$regex": f"^{re.escape(clean_email)}$", "$options": "i"}},  # Exact email match
            {"recipient": {"$regex": f".*<{re.escape(clean_email)}>.*", "$options": "i"}}  # Display name format match
        ]
    }
    
    if exclude:
        # Handle multiple comma-separated email addresses to exclude
        exclude_list = [addr.strip() for addr in exclude.split(',')]
        
        # For multiple email addresses, use $nor operator
        if len(exclude_list) > 1:
            nor_conditions = []
            for email_addr in exclude_list:
                clean_exclude = extract_email(email_addr)
                nor_conditions.append({
                    "$or": [
                        {"sender": {"$regex": f"^{re.escape(clean_exclude)}$", "$options": "i"}},
                        {"sender": {"$regex": f".*<{re.escape(clean_exclude)}>.*", "$options": "i"}}
                    ]
                })
            query["$nor"] = nor_conditions
        else:
            # For a single email address, use $not with $or
            clean_exclude = extract_email(exclude_list[0])
            query["$nor"] = [{
                "$or": [
                    {"sender": {"$regex": f"^{re.escape(clean_exclude)}$", "$options": "i"}},
                    {"sender": {"$regex": f".*<{re.escape(clean_exclude)}>.*", "$options": "i"}}
                ]
            }]
    
    return query


def add_status_filter(query: Dict[str, Any], status: Optional[str]) -> None:
    """Add status filter to query"""
    if status:
        if status == "All":
            query["status"] = {"$ne": "unprocessed"}
        else:
            query["status"] = status
def add_source_filter(query: Dict[str, Any], source: Optional[str]) -> None:
    """Add source filter to query"""
    if source:
        query["source"] = source

def add_conv_filter(query: Dict[str, Any], conversation_id: Optional[str]) -> None:
    """Add conversation ID filter to query"""
    if conversation_id:
        query["conversation_id"] = conversation_id

def add_search_filter(query: Dict[str, Any], search: Optional[str]) -> None:
    """Add text search filter to query"""
    if search:
        search_regex = {"$regex": search, "$options": "i"}
        if "$and" not in query:
            query["$and"] = []
        query["$and"].append({
            "$or": [
                {"subject": search_regex},
                {"body": search_regex},
                {"sender": search_regex}
            ]
        })

def add_agent_filters(query: Dict[str, Any], agent_filters: Dict[str, Optional[str]]) -> None:
    """Add agent output filters to query"""
    active_filters = {k: v for k, v in agent_filters.items() if v is not None}
    
    if active_filters:
        query["response"] = {"$exists": True}
        agent_conditions = []
        
        for agent_name, agent_output in active_filters.items():
            agent_outputs = [output.strip() for output in agent_output.split(',')]
            output_conditions = []
            
            for output in agent_outputs:
                output_conditions.append({
                    "response.steps": {
                        "$elemMatch": {
                            "agent": agent_name,
                            "output": {"$regex": output, "$options": "i"}
                        }
                    }
                })
            
            agent_condition = {"$or": output_conditions} if len(output_conditions) > 1 else output_conditions[0]
            agent_conditions.append(agent_condition)
        
        if agent_conditions:
            if "$and" not in query:
                query["$and"] = []
            query["$and"].extend(agent_conditions)

def build_aggregation_pipeline(query: Dict[str, Any], status: Optional[str], start_idx: int, page_size: int) -> list:
    """Build MongoDB aggregation pipeline"""
    fallback_date = datetime(1, 1, 1, tzinfo=timezone.utc) # A very early date for robust sorting

    pipeline = [
        {"$match": query},
        {
            "$addFields": {
                "sortable_date": {
                    "$switch": {
                        "branches": [
                            {
                                "case": {"$eq": [{"$type": "$date"}, "date"]},  # Check if $date is BSON Date
                                "then": "$date"
                            },
                            {
                                "case": {"$eq": [{"$type": "$date"}, "string"]},  # Check if $date is BSON String
                                "then": {
                                    "$dateFromString": {
                                        "dateString": "$date",
                                        "onError": fallback_date,  # Fallback for unparseable strings
                                    }
                                }
                            }
                        ],
                        "default": fallback_date  # Fallback if $date is neither Date nor String, or is null
                    }
                }
            }
        }
    ]
    
    # Add sort based on status and the new sortable_date
    if status == "All":
        pipeline.append({"$sort": {"updated_at": -1, "sortable_date": -1}}) # Primary sort by updated_at, secondary by email date
    else:
        pipeline.append({"$sort": {"sortable_date": -1}}) # Sort by email date
    
    # Add pagination
    pipeline.extend([
        {"$skip": start_idx},
        {"$limit": page_size}
    ])
    # Optionally, you might want to remove the sortable_date field from the final output
    # pipeline.append({"$project": {"sortable_date": 0}})
    return pipeline
