import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from app.workflows.lang_graph import EmailProcessingGraph
from datetime import datetime, timedelta
from bson import ObjectId
import logging
import sys
import traceback

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add file handler for persistent logging
import os

# Create logs directory if it doesn't exist
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
os.makedirs(log_dir, exist_ok=True)

# Setup file handler with Windows-compatible path
log_file = os.path.join(log_dir, 'worker_debug.log')
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
))
logger.addHandler(file_handler)


# Use dedicated worker MongoDB connection with retry logic
from app.config.db_pool import workflow_client
import motor.motor_asyncio
import time

async def get_db_with_retry(max_retries=5, retry_delay=5):
    """Get database connection with retry logic"""
    for attempt in range(max_retries):
        try:
            # Test connection
            await workflow_client.server_info()
            logger.info("Successfully connected to MongoDB")
            return workflow_client[os.getenv("DB_NAME", "email_responder")]
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"Failed to connect to MongoDB after {max_retries} attempts")
                raise
            logger.warning(f"MongoDB connection attempt {attempt + 1} failed, retrying in {retry_delay}s")
            await asyncio.sleep(retry_delay)

# Configure worker-specific connection pool
workflow_client.get_io_loop = asyncio.get_running_loop
db = None  # Will be initialized in main()

async def process_workflow_task(workflow_data):
    """Process a single workflow task"""
    try:
        workflow_id = workflow_data["workflow_id"]
        user_id = workflow_data["user_id"]
        email_ids = workflow_data["email_ids"]
        workflow_config = workflow_data["config"]

        # Initialize graph processor
        graph = EmailProcessingGraph(db)
        
        if workflow_config["processing_type"] == "concurrent":
            sem = asyncio.Semaphore(workflow_config["processing_limit"])
            tasks = []
            
            for email_id in email_ids:
                initial_state = {
                    "workflow_id": workflow_id,
                    "user_id": user_id,
                    "current_email_id": email_id,
                    "email": {},
                    "classification": None,
                    "sentiment": None,
                    "context": None,
                    "response": None,
                    "draft": None,
                    "error": None
                }
                
                tasks.append(
                    process_email_with_semaphore(sem, graph, initial_state)
                )
            
            await asyncio.gather(*tasks)
            
        else:
            for email_id in email_ids:
                initial_state = {
                    "workflow_id": workflow_id,
                    "user_id": user_id,
                    "current_email_id": email_id,
                    "email": {},
                    "classification": None,
                    "sentiment": None,
                    "context": None,
                    "response": None,
                    "draft": None,
                    "error": None
                }
                
                await graph.process_email(initial_state)

        # Update workflow status
        await db.workflows.update_one(
            {"_id": ObjectId(workflow_id)},
            {"$set": {
                "status": "completed",
                "updated_at": datetime.utcnow()
            }}
        )

    except Exception as e:
        logger.error(f"Error processing workflow {workflow_id}: {str(e)}")
        await db.workflows.update_one(
            {"_id": ObjectId(workflow_id)},
            {"$set": {
                "status": "error",
                "error": str(e),
                "updated_at": datetime.utcnow()
            }}
        )

async def process_email_with_semaphore(sem: asyncio.Semaphore, graph: EmailProcessingGraph, state: dict):
    """Process a single email with semaphore for concurrency control"""
    async with sem:
        try:
            await graph.process_email(state)
        except Exception as e:
            logger.error(f"Error processing email {state['current_email_id']}: {str(e)}")
            await graph.db.workflows.update_one(
                {"_id": ObjectId(state["workflow_id"])},
                {"$set": {
                    f"errors.{state['current_email_id']}": str(e),
                    "updated_at": datetime.utcnow()
                }}
            )

async def main():
    try:
        logger.info("Starting workflow worker...")
        
        # Initialize database connection with retry
        global db
        db = await get_db_with_retry()
        
        while True:
            try:
                # Find and claim the next available workflow
                now = datetime.utcnow()
                result = await db.workflows.find_one_and_update(
                    {
                        "status": "queued",
                        "next_attempt_at": {"$lte": now},
                        "processing_attempt": {"$lt": 3}  # Max 3 attempts
                    },
                    {
                        "$set": {
                            "status": "processing",
                            "processing_started_at": now,
                            "updated_at": now
                        },
                        "$inc": {"processing_attempt": 1}
                    },
                    sort=[("queued_at", 1)],  # Process oldest first
                    return_document=True
                )
                
                if result:
                    workflow_data = {
                        "workflow_id": str(result["_id"]),
                        "user_id": result["user_id"],
                        "email_ids": [str(id) for id in result["email_ids"]],
                        "config": result["config"]
                    }
                    
                    # Process the workflow with timeout
                    try:
                        await asyncio.wait_for(
                            process_workflow_task(workflow_data),
                            timeout=300  # 5 minute timeout per workflow
                        )
                    except asyncio.TimeoutError:
                        logger.error(f"Workflow {workflow_data['workflow_id']} timed out")
                        await db.workflows.update_one(
                            {"_id": ObjectId(workflow_data["workflow_id"])},
                            {
                                "$set": {
                                    "status": "queued",  # Reset to queued for retry
                                    "error": "Workflow processing timed out",
                                    "next_attempt_at": now + timedelta(minutes=5),  # Retry after 5 minutes
                                    "updated_at": now
                                }
                            }
                        )
                else:
                    # No work available, sleep briefly
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error in worker main loop: {str(e)}")
                await asyncio.sleep(1)
            
            # Prevent memory leaks
            if 'gc' not in locals():
                import gc
            gc.collect()
    except Exception as e:
        logger.critical(f"Fatal error in worker process: {str(e)}")
        logger.critical(f"Stack trace: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Worker shutting down gracefully...")
    except Exception as e:
        logger.critical(f"Fatal error in worker process: {str(e)}")
        logger.critical(f"Stack trace: {traceback.format_exc()}")
        sys.exit(1)
