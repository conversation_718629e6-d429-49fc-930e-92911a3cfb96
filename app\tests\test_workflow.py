import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.main import app
from app.workflows.states import WorkflowState, WorkflowTransition
from app.workflows.workflow_manager import WorkflowManager
from unittest.mock import Mock, patch
from datetime import datetime

client = TestClient(app)

@pytest.fixture
def mock_db():
    return Mock()

@pytest.fixture
def workflow_manager(mock_db):
    return WorkflowManager(mock_db)

@pytest.mark.asyncio
async def test_create_workflow(workflow_manager, mock_db):
    # Setup
    user_id = "test_user"
    email_ids = ["email1", "email2"]
    mock_db.workflows.insert_one.return_value.inserted_id = "test_workflow_id"
    
    # Execute
    workflow_id = await workflow_manager.create_workflow(user_id, email_ids)
    
    # Assert
    assert workflow_id == "test_workflow_id"
    mock_db.workflows.insert_one.assert_called_once()
    
@pytest.mark.asyncio
async def test_invalid_state_transition(workflow_manager, mock_db):
    # Setup
    workflow_id = "test_workflow"
    mock_db.workflows.find_one.return_value = {
        "_id": workflow_id,
        "user_id": "test_user",
        "email_ids": ["email1"],
        "current_state": WorkflowState.COMPLETED,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }
    
    # Execute & Assert
    with pytest.raises(HTTPException) as exc_info:
        await workflow_manager.transition_state(
            workflow_id,
            WorkflowTransition.FETCH_EMAILS
        )
    assert exc_info.value.status_code == 400
    
@pytest.mark.asyncio
async def test_concurrent_workflow_processing(workflow_manager, mock_db):
    # Setup
    user_id = "test_user"
    email_ids = ["email1", "email2", "email3"]
    workflow_id = "test_workflow"
    
    # Mock configuration
    mock_db.configurations.find_one.return_value = {
        "workflow_config": {
            "processing_type": "concurrent",
            "concurrent_limit": 2
        }
    }
    
    # Mock email processing
    mock_db.workflows.insert_one.return_value.inserted_id = workflow_id
    
    # Execute
    await workflow_manager.process_workflow(workflow_id, user_id, email_ids)
    
    # Assert
    mock_db.workflows.update_one.assert_called()
    # Verify concurrent processing happened in batches of 2
    assert mock_db.workflows.update_one.call_count >= 2

@pytest.mark.asyncio
async def test_workflow_error_handling(workflow_manager, mock_db):
    # Setup
    workflow_id = "test_workflow"
    error_message = "Test error"
    mock_db.workflows.find_one.return_value = {
        "_id": workflow_id,
        "user_id": "test_user",
        "email_ids": ["email1"],
        "current_state": WorkflowState.INITIATED,
        "retry_count": 0,
        "max_retries": 3,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }
    
    # Execute
    await workflow_manager.handle_error(workflow_id, error_message)
    
    # Assert
    mock_db.workflows.update_one.assert_called_once()
    assert mock_db.workflows.update_one.call_args[0][0] == {"_id": workflow_id}
