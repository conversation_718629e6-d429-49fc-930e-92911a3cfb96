from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Query, HTTPException, <PERSON><PERSON>
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.services.outlook_service import OutlookService
from app.services.gmail_service import GmailService
from app.utils.security import decode_jwt_token
from fastapi.responses import RedirectResponse, JSONResponse

router = APIRouter(prefix="", tags=["authentication"])
outlook_service = OutlookService()
gmail_service = GmailService()

@router.get("/auth/outlook")
async def outlook_auth(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Initiate Outlook OAuth flow"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    auth_url = await outlook_service.get_auth_url(user_id)
    # Return the URL as JSON instead of a redirect response
    return {"auth_url": auth_url}

@router.get("/auth/gmail")
async def gmail_auth(
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Initiate Gmail OAuth flow"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    token = authorization.replace("Bearer ", "")
    user_id = decode_jwt_token(token)
    
    auth_url = await gmail_service.get_auth_url(user_id)
    return {"auth_url": auth_url}

@router.get("/api/auth/outlook/callback")
async def outlook_callback(
    code: str = Query(...),
    state: str = Query(...),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """Handle Outlook OAuth callback"""
    return await outlook_service.handle_auth_callback(code, state, db)

@router.get("/api/auth/gmail/callback")
async def gmail_callback(
    code: str = Query(...),
    state: str = Query(...),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """Handle Gmail OAuth callback"""
    return await gmail_service.handle_auth_callback(code, state, db)
