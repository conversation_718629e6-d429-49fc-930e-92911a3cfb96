import pytest
from fastapi.testclient import TestClient
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from app.main import app
from app.services.email_service import EmailService
from unittest.mock import Mock, patch
from datetime import datetime
from bson import ObjectId

client = TestClient(app)

@pytest.fixture
def mock_db():
    return Mock()

@pytest.mark.asyncio
async def test_regenerate_draft(mock_db):
    # Setup
    user_id = "test_user"
    email_id = "test_email"
    
    mock_db.emails.find_one.return_value = {
        "_id": ObjectId(email_id),
        "user_id": user_id,
        "body": "Test email body",
        "category": "support",
        "sentiment": {"label": "neutral", "score": 0.0},
        "context": {}
    }
    
    # Execute
    result = await EmailService.regenerate_draft(mock_db, user_id, email_id)
    
    # Assert
    assert "draft_id" in result
    assert "response" in result
    mock_db.emails.find_one.assert_called_once()

@pytest.mark.asyncio
async def test_update_draft(mock_db):
    # Setup
    user_id = "test_user"
    email_id = "test_email"
    new_content = "Updated draft content"
    
    mock_db.emails.find_one.return_value = {
        "_id": ObjectId(email_id),
        "user_id": user_id
    }
    
    # Execute
    result = await EmailService.update_draft(mock_db, user_id, email_id, new_content)
    
    # Assert
    assert "draft_id" in result
    assert result["response"] == new_content
    mock_db.emails.find_one.assert_called_once()

@pytest.mark.asyncio
async def test_draft_not_found(mock_db):
    # Setup
    user_id = "test_user"
    email_id = "nonexistent_email"
    mock_db.emails.find_one.return_value = None
    
    # Execute & Assert
    with pytest.raises(HTTPException) as exc_info:
        await EmailService.regenerate_draft(mock_db, user_id, email_id)
    assert exc_info.value.status_code == 404
