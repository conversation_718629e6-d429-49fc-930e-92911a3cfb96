from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.responses import RedirectResponse, JSONResponse
import requests
from msal import ConfidentialClientApplication
import uvicorn
from datetime import datetime
from pydantic import BaseModel

app = FastAPI()

# OAuth configuration
# client_id = '454cc359-6bce-4340-835b-f85207eed9e0'
# client_secret = '****************************************'
# tenant_id = '4ab5cf8f-741a-47ff-8ae3-44267d28a372'
client_id ='7beda09d-3346-4a6c-b0fe-59d2ba8f90cc'
client_secret = '****************************************'
tenant_id = 'f8cdef31-a31e-4b4a-93e4-5f571e91255a'
redirect_uri = 'http://localhost:5501'
authority = "https://login.microsoftonline.com/common"

# Use the working scopes
scopes = ["Mail.Read", "Mail.ReadWrite", "Mail.Send"]

msal_app = ConfidentialClientApplication(
    client_id,
    authority=authority,
    client_credential=client_secret
)

# In-memory token store (replace with database in production)
token_store = {}

@app.get("/auth")
def auth():
    auth_url = msal_app.get_authorization_request_url(
        scopes,
        redirect_uri=redirect_uri,
        prompt="select_account"  # This will help ensure we get refresh token
    )
    return RedirectResponse(auth_url)

@app.get("/auth/callback")
async def auth_callback(request: Request):
    code = request.query_params.get('code')
    
    # Request both access and refresh tokens
    token_response = msal_app.acquire_token_by_authorization_code(
        code,
        scopes=scopes,
        redirect_uri=redirect_uri
    )
    
    if "error" in token_response:
        raise HTTPException(
            status_code=400, 
            detail=f"Error: {token_response.get('error')}, Description: {token_response.get('error_description')}"
        )

    # Store tokens
    token_store["access_token"] = token_response.get("access_token")
    token_store["refresh_token"] = token_response.get("refresh_token")  # MSAL will handle refresh token automatically
    token_store["expires_in"] = token_response.get("expires_in")
    token_store["timestamp"] = datetime.now().timestamp()

    return JSONResponse(content={
        "message": "Authentication successful",
        "token_type": token_response.get("token_type"),
        "expires_in": token_response.get("expires_in"),
        "has_refresh_token": "refresh_token" in token_response
    })

def get_valid_token() -> str:
    """Get a valid access token, refreshing if necessary."""
    if not token_store.get("access_token"):
        raise HTTPException(status_code=401, detail="No token available. Please authenticate first.")

    # Check if token is expired (with 5-minute buffer)
    current_time = datetime.now().timestamp()
    token_age = current_time - token_store.get("timestamp", 0)
    
    if token_age >= (token_store.get("expires_in", 0) - 300):  # 5-minute buffer
        refresh_token = token_store.get("refresh_token")
        if not refresh_token:
            raise HTTPException(status_code=401, detail="No refresh token available. Please authenticate again.")

        # Try to refresh the token
        token_response = msal_app.acquire_token_silent(
            scopes,
            account=None,
            force_refresh=True
        )

        if not token_response:
            # If silent token acquisition fails, try using refresh token
            token_response = msal_app.acquire_token_by_refresh_token(
                refresh_token,
                scopes=scopes
            )

        if not token_response or "error" in token_response:
            raise HTTPException(status_code=401, detail="Failed to refresh token. Please authenticate again.")

        # Update stored tokens
        token_store["access_token"] = token_response.get("access_token")
        token_store["refresh_token"] = token_response.get("refresh_token", refresh_token)
        token_store["expires_in"] = token_response.get("expires_in")
        token_store["timestamp"] = current_time

    return token_store["access_token"]

@app.get("/emails")
async def get_emails():
    access_token = get_valid_token()
    url = 'https://graph.microsoft.com/v1.0/me/messages'
    headers = {
    'Authorization': f'Bearer {access_token}',
    'Content-Type': 'application/json'
}
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        raise HTTPException(
            status_code=response.status_code,
            detail=f"Graph API Error: {response.text}"
        )
    return response.json()

# Add a token info endpoint for debugging
@app.get("/token-info")
async def token_info():
    if not token_store:
        return {"message": "No tokens stored"}
    
    current_time = datetime.now().timestamp()
    token_age = current_time - token_store.get("timestamp", 0)
    
    return {
        "has_access_token": bool(token_store.get("access_token")),
        "has_refresh_token": bool(token_store.get("refresh_token")),
        "acess_token": token_store.get("access_token"),
        "token_age_seconds": int(token_age),
        "expires_in": token_store.get("expires_in"),
        "time_until_expiry": int(token_store.get("expires_in", 0) - token_age) if token_store.get("expires_in") else None
    }

class EmailReplyRequest(BaseModel):
    message_id: str
    reply_content: str

@app.post("/emails/reply")
async def send_email_reply(request_data: EmailReplyRequest):
    access_token = get_valid_token()
    
    url = f'https://graph.microsoft.com/v1.0/me/messages/{request_data.message_id}/reply'
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "comment": request_data.reply_content
    }
    
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code != 202:
        raise HTTPException(
            status_code=response.status_code,
            detail=f"Graph API Error: {response.text}"
        )

    return {"message": "Reply sent successfully"}

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=8000)