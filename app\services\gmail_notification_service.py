import logging
import async<PERSON>
import traceback
from datetime import datetime, timedelta, timezone
from fastapi import HTT<PERSON>Exception
from google.oauth2.credentials import Credentials
from app.services.attachment_service import attachment_service
from google.cloud import pubsub_v1
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from bson import ObjectId
import json
import os
import traceback
from app.utils.security import fernet
from app.utils.debug_utils import log_exception, safe_decrypt, log_api_request

logger = logging.getLogger(__name__)

class GmailNotificationService:
    def __init__(self, db):
        self.db = db
        self.project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID")
        self.topic_name = os.getenv("GMAIL_PUBSUB_TOPIC")
        self.client_id = os.getenv("GMAIL_CLIENT_ID")
        self.client_secret = os.getenv("GMAIL_CLIENT_SECRET")
        self.redirect_uri = os.getenv("GMAIL_REDIRECT_URI")
        
        # Validate required configuration
        required_vars = {
            "GOOGLE_CLOUD_PROJECT_ID": self.project_id,
            "GMAIL_PUBSUB_TOPIC": self.topic_name,
            "GMAIL_CLIENT_ID": self.client_id,
            "GMAIL_CLIENT_SECRET": self.client_secret,
            "GMAIL_REDIRECT_URI": self.redirect_uri
        }
        
        missing_vars = [k for k, v in required_vars.items() if not v]
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")

    async def _get_user_gmail_config(self, user_id: str) -> dict:
        """Get user's Gmail configuration"""
        user = await self.db.users.find_one({"_id": ObjectId(user_id)})
        if not user or "email_config" not in user or "gmail_config" not in user["email_config"]:
            raise HTTPException(status_code=400, detail="User gmail configuration not found")
        
        gmail_config = user["email_config"]["gmail_config"]
        if not gmail_config.get("email"):
            raise HTTPException(status_code=400, detail="User email not found in gmail configuration")
        
        return gmail_config

    async def _cleanup_existing_subscriptions(self, user_email: str):
        """Clean up any existing subscriptions for the email"""
        users_with_email = await self.db.users.find({
            "email_config.gmail_config.email": user_email
        }).to_list(None)
        
        for other_user in users_with_email:
            existing_subs = await self.db.gmail_subscriptions.find({"user_id": str(other_user["_id"])}).to_list(None)
            if existing_subs:
                await self.delete_subscription(str(other_user["_id"]))
        
        await asyncio.sleep(2)  # Allow time for cleanup

    async def _create_gmail_credentials(self, gmail_config: dict) -> Credentials:
        """Create Gmail credentials object"""
        try:
            decrypted_access_token = fernet.decrypt(gmail_config["access_token"].encode()).decode()
            decrypted_refresh_token = fernet.decrypt(gmail_config["refresh_token"].encode()).decode() if gmail_config.get("refresh_token") else None
            
            if not decrypted_refresh_token:
                raise HTTPException(
                    status_code=401,
                    detail="No refresh token available. Please re-authenticate with Gmail."
                )

            scopes = [
                'https://www.googleapis.com/auth/gmail.readonly',
                'https://www.googleapis.com/auth/gmail.modify',
                'https://www.googleapis.com/auth/gmail.metadata',
                'https://www.googleapis.com/auth/pubsub'
            ]
            
            return Credentials(
                token=decrypted_access_token,
                refresh_token=decrypted_refresh_token,
                token_uri=gmail_config.get("token_uri", "https://oauth2.googleapis.com/token"),
                client_id=gmail_config.get("client_id", self.client_id),
                client_secret=gmail_config.get("client_secret", self.client_secret),
                scopes=scopes
            )
        except Exception as e:
            raise HTTPException(
                status_code=401,
                detail=f"Error creating credentials: {str(e)}"
            )

    async def _verify_gmail_access(self, service, user_id: str, max_retries: int = 2) -> None:
        """Verify Gmail API access with retry mechanism"""
        for attempt in range(max_retries):
            try:
                profile = service.users().getProfile(userId='me').execute()
                await self.db.users.update_one(
                    {"_id": ObjectId(user_id)},
                    {"$set": {"email_config.gmail_config.token_status": "active"}}
                )
                return
            except Exception as e:
                if "invalid_grant" in str(e):
                    await self.db.users.update_one(
                        {"_id": ObjectId(user_id)},
                        {"$set": {"email_config.gmail_config.token_status": "expired"}}
                    )
                    raise HTTPException(
                        status_code=401,
                        detail="Your Gmail authorization has expired. Please re-authenticate with Gmail."
                    )
                if attempt == max_retries - 1:
                    raise

    async def _store_subscription(self, user_id: str, response: dict) -> dict:
        """Store subscription information in database"""
        expiration_timestamp_ms = int(response["expiration"])
        expiration_time = datetime.fromtimestamp(expiration_timestamp_ms / 1000, tz=timezone.utc)

        subscription_data = {
            "user_id": str(user_id),
            "history_id": response["historyId"],
            "expiration": expiration_time.isoformat(timespec='milliseconds').replace('+00:00', 'Z'),
            "created_at": datetime.now(timezone.utc).isoformat(timespec='milliseconds').replace('+00:00', 'Z')
        }

        # Upsert subscription
        await self.db.gmail_subscriptions.update_one(
            {"user_id": user_id},
            {"$set": subscription_data},
            upsert=True
        )

        return {
            "status": "success",
            "message": "Gmail subscription created successfully"
        }

    async def create_subscription(self, user_id: str):
        """Create Gmail push notification subscription"""
        try:
            logger.info(f"Starting Gmail subscription creation for user {user_id}")
            print(f"Starting Gmail subscription creation for user {user_id}")
            process_id = datetime.now().strftime("%Y%m%d%H%M%S")

            # Get Gmail configuration and clean up existing subscriptions
            gmail_config = await self._get_user_gmail_config(user_id)
            await self._cleanup_existing_subscriptions(gmail_config["email"])

            # Check token status and refresh if needed
            if gmail_config.get("token_status") == "expired":
                return {
                    "status": "error",
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": "Gmail authentication required",
                    "detail": "Your Gmail authorization has expired. Please re-authenticate with Gmail."
                }

            # Refresh token using Gmail service
            from app.services.gmail_service import GmailService
            gmail_service = GmailService()
            await gmail_service.refresh_token(self.db, user_id)
            print(" Token refreshed")

            # Get fresh Gmail configuration
            gmail_config = (await self._get_user_gmail_config(user_id))
            print("Gmail config:", gmail_config)

            # Create and verify credentials
            creds = await self._create_gmail_credentials(gmail_config)
            print("Credentials created")
            
            # Build Gmail service
            service = build('gmail', 'v1', credentials=creds)
            print("Gmail service built")
            await self._verify_gmail_access(service, user_id)
            print("Gmail access verified")

            # Set up watch request
            topic_path = f'projects/{self.project_id}/topics/{self.topic_name}'
            print("Topic path:", topic_path)

            # Handle webhook or Pub/Sub subscription
            webhook_url = os.getenv("GMAIL_WEBHOOK_URL", "http://localhost:8000/webhook")
            use_webhook = os.getenv("USE_GMAIL_WEBHOOK", "false").lower() == "true"
            
            if use_webhook:
                response = await self._setup_webhook_subscription(user_id, webhook_url)
            else:
                print("Setting up Pub/Sub subscription")
                response = await self._setup_pubsub_subscription(service, topic_path)

            # Store subscription data
            return await self._store_subscription(user_id, response)

        except Exception as e:
            return self._handle_subscription_error(e)

    async def _setup_webhook_subscription(self, user_id: str, webhook_url: str) -> dict:
        """Set up webhook-based subscription"""
        logger.info(f"Using webhook URL for Gmail notifications: {webhook_url}")
        
        # Fetch recent emails to initialize
        from app.services.gmail_service import GmailService
        gmail_service = GmailService()
        await gmail_service.fetch_recent_emails(self.db, user_id, max_results=20)
        
        return {
            "historyId": "webhook",
            "expiration": str(int((datetime.now(timezone.utc) + timedelta(days=7)).timestamp() * 1000))
        }

    async def _verify_pubsub_permissions(self, topic_path: str) -> None:
        """Verify Pub/Sub permissions and topic existence"""
        try:
            print("Verifying Pub/Sub permissions")
            # Create a Publisher client
            publisher = pubsub_v1.PublisherClient()
            print("Publisher client created")
            # Try to get the topic to verify it exists
            publisher.get_topic(request={"topic": topic_path})
            print("Topic exists")
            # Try to publish a test message
            test_data = "test".encode("utf-8")
            publisher.publish(topic_path, test_data)
            print("Pub/Sub permissions verified")
            
            logger.info(f"Successfully verified Pub/Sub topic access: {topic_path}")
        except Exception as e:
            print("Pub/Sub permissions verification failed", e)
            error_message = str(e)
            if "NOT_FOUND" in error_message:
                raise HTTPException(
                    status_code=404,
                    detail=f"Pub/Sub topic not found: {topic_path}. Please create the topic first."
                )
            elif "PERMISSION_DENIED" in error_message:
                raise HTTPException(
                    status_code=403,
                    detail="Permission denied accessing Pub/Sub topic. Please ensure:\n"
                          "1. The Gmail API is enabled\n"
                          "2. The service account has 'Pub/Sub Publisher' role\n"
                          "3. The topic has proper IAM permissions set"
                )
            raise HTTPException(
                status_code=500,
                detail=f"Error verifying Pub/Sub access: {error_message}"
            )

    async def _setup_pubsub_subscription(self, service, topic_path: str) -> dict:
        """Set up Pub/Sub-based subscription"""
        try:
            # First verify Pub/Sub permissions
            await self._verify_pubsub_permissions(topic_path)
            
            watch_request = {
                'topicName': topic_path,
                'labelIds': ['INBOX'],
                'labelFilterAction': 'include'
            }

            print(" Sending watch request")
            response = service.users().watch(userId='me', body=watch_request).execute()
            print("Watch request successful. History ID:", response.get('historyId'))
            logger.info(f"Watch request successful. History ID: {response.get('historyId')}")
            return response
            
        except HTTPException:
            # Re-raise HTTP exceptions from _verify_pubsub_permissions
            raise
        except Exception as e:
            print(f" Watch request failed: {str(e)}")
            error_message = str(e)
            
            if "User not authorized" in error_message:
                raise HTTPException(
                    status_code=403,
                    detail="Gmail API lacks permission to publish to Pub/Sub topic. Please ensure:\n"
                          "1. The Gmail API is enabled in Google Cloud Console\n"
                          "2. The Gmail API has permission to publish to the topic\n"
                          "3. The user has granted necessary Gmail API scopes"
                )
            elif "Not Found" in error_message or "404" in error_message:
                raise HTTPException(
                    status_code=404,
                    detail="Gmail API or Pub/Sub topic not found. Please verify:\n"
                          "1. The Gmail API is enabled\n"
                          "2. The Pub/Sub topic exists\n"
                          "3. The project ID and topic name are correct"
                )
            
            raise HTTPException(
                status_code=500,
                detail=f"Error watching Gmail mailbox: {error_message}"
            )

    def _handle_subscription_error(self, e: Exception) -> dict:
        """Handle errors during subscription creation"""
        error_message = str(e)
        logger.error(f"Error creating Gmail subscription: {error_message}")
        
        if isinstance(e, dict) and "status" in e and e["status"] == "error":
            return e
            
        if "invalid_grant" in error_message.lower() or "authentication" in error_message.lower():
            return {
                "status": "error",
                "code": "AUTHENTICATION_REQUIRED",
                "message": "Gmail authentication required",
                "detail": "Your Gmail authorization has expired. Please re-authenticate with Gmail."
            }
        
        return {
            "status": "error",
            "code": "SUBSCRIPTION_ERROR",
            "message": "Failed to create subscription",
            "detail": f"Failed to create subscription: {error_message}"
        }

    async def handle_notification(self, message_data: dict):
        """Handle incoming Gmail notification"""
        try:
            # Check if this is a webhook notification or a Pub/Sub message
            if "message" in message_data and "data" in message_data["message"]:
                # This is a Pub/Sub message
                logger.info("Processing Pub/Sub notification")
                return await self.handle_pubsub_notification(message_data)
            else:
                # This might be a direct webhook notification
                logger.info("Processing webhook notification")
                return await self.handle_webhook_notification(message_data)

        except Exception as e:
            logger.error(f"Error handling Gmail notification: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
            
    async def handle_pubsub_notification(self, message_data: dict):
        print("Handling Pub/Sub notification")
        """Handle incoming Gmail notification from Pub/Sub"""
        try:
            # Extract data from Pub/Sub message
            data = json.loads(message_data["message"]["data"])
            user_id = data["emailAddress"]
            history_id = data["historyId"]

            # Get user's Gmail credentials
            user = await self.db.users.find_one({"_id": user_id})
            if not user:
                logger.error(f"User not found for notification: {user_id}")
                return

            creds = Credentials(
                token=user["email_config"]["gmail_config"]["access_token"],
                refresh_token=user["email_config"]["gmail_config"]["refresh_token"],
                token_uri="https://oauth2.googleapis.com/token",
                client_id=user["email_config"]["gmail_config"]["client_id"],
                client_secret=user["email_config"]["gmail_config"]["client_secret"]
            )

            # Build Gmail service
            service = build('gmail', 'v1', credentials=creds)

            # Get history since last update
            history_list = service.users().history().list(
                userId='me',
                startHistoryId=history_id
            ).execute()

            # Process new messages
            for history in history_list.get("history", []):
                for message_added in history.get("messagesAdded", []):
                    message = message_added["message"]
                    
                    # Get full message details with attachments
                    msg = service.users().messages().get(
                        userId='me',
                        id=message["id"],
                        format='full'
                    ).execute()

                    try:
                        # Process email with attachments
                        email_data, attachments = await attachment_service.process_email_attachments(
                            email_message=msg,
                            service=service,
                            message_id=message["id"],
                            provider='gmail'
                        )

                        # Prepare email document for database
                        headers = {h['name'].lower(): h['value'] for h in msg['payload'].get('headers', [])}
                        email_doc = {
                            'user_id': user_id,
                            'message_id': msg['id'],
                            'thread_id': msg.get('threadId'),
                            'subject': headers.get('subject', '(No subject)'),
                            'from_email': headers.get('from', ''),
                            'to': headers.get('to', ''),
                            'cc': headers.get('cc', ''),
                            'bcc': headers.get('bcc', ''),
                            'date': datetime.utcnow(),
                            'body': email_data.get('text', ''),
                            'html_body': email_data.get('html', ''),
                            'attachments': [{
                                'filename': att['filename'],
                                's3_key': att['s3_key'],
                                'content_type': att['mime_type'],
                                'size': att['size'],
                                'is_inline': att['is_inline'],
                                'content_id': att.get('content_id')
                            } for att in attachments],
                            'inline_images': {
                                cid: {
                                    's3_key': img['s3_key'],
                                    'content_type': img['content_type'],
                                    'size': img['size'],
                                    'url': img['url']
                                } for cid, img in email_data.get('inline_images', {}).items()
                            },
                            'processed': False,
                            'created_at': datetime.utcnow()
                        }

                        # Store email in database
                        result = await self.db.emails.insert_one(email_doc)
                        email_doc['_id'] = str(result.inserted_id)

                        # Notify connected clients
                        from app.websocket.connection_manager import manager
                        await manager.send_to_user(
                            user_id,
                            'new_email',
                            email_doc
                        )
                    except Exception as e:
                        logger.error(f"Error processing email attachments: {str(e)}")
                        logger.error(traceback.format_exc())
                        # Store the email without attachments if processing fails
                        try:
                            from app.services.email_service import EmailService
                            await EmailService.store_new_email(self.db, user_id, msg)
                        except Exception as store_error:
                            logger.error(f"Failed to store email without attachments: {str(store_error)}")

            # Update history ID
            await self.db.gmail_subscriptions.update_one(
                {"user_id": user_id},
                {"$set": {"history_id": history_id}}
            )

            return {"status": "success"}

        except Exception as e:
            logger.error(f"Error handling Pub/Sub notification: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
            
    async def handle_webhook_notification(self, data: dict):
        """Handle incoming Gmail notification from webhook"""
        try:
            logger.info("Processing webhook notification data")
            
            # Find all users with Gmail configuration
            users = await self.db.users.find(
                {"email_config.provider": "gmail"},
                {"_id": 1}
            ).to_list(None)
            
            if not users:
                logger.warning("No Gmail users found")
                return {"status": "success", "message": "No Gmail users found"}
            
            logger.info(f"Found {len(users)} Gmail users")
            
            # Process each user
            from app.services.gmail_service import GmailService
            gmail_service = GmailService()
            
            for user in users:
                user_id = str(user["_id"])
                try:
                    # Fetch recent emails for this user
                    await gmail_service.fetch_recent_emails(self.db, user_id, max_results=20)
                except Exception as user_error:
                    logger.error(f"Error processing user {user_id}: {str(user_error)}")

            return {"status": "success", "message": f"Processed {len(users)} users"}

        except Exception as e:
            logger.error(f"Error handling webhook notification: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def renew_subscription(self, user_id: str):
        """Renew Gmail push notification subscription"""
        try:
            # Delete existing subscription
            await self.delete_subscription(user_id)
            
            # Create new subscription
            return await self.create_subscription(user_id)

        except Exception as e:
            logger.error(f"Error renewing Gmail subscription: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_subscription(self, user_id: str):
        """Delete Gmail push notification subscription"""
        try:
            # Convert string user_id to ObjectId if needed
            user_obj_id = ObjectId(user_id) if not isinstance(user_id, ObjectId) else user_id
            
            user = await self.db.users.find_one({"_id": user_obj_id})
            if not user:
                logger.warning(f"User not found for ID: {user_id}")
                return {"status": "success", "message": "No user found"}
                
            if "email_config" not in user or "gmail_config" not in user["email_config"]:
                logger.warning(f"Gmail configuration not found for user: {user_id}")
                return {"status": "success", "message": "No Gmail configuration found"}

            try:
                # Create credentials with decrypted tokens
                decrypted_access_token = fernet.decrypt(user["email_config"]["gmail_config"]["access_token"].encode()).decode()
                decrypted_refresh_token = fernet.decrypt(user["email_config"]["gmail_config"]["refresh_token"].encode()).decode() if user["email_config"]["gmail_config"].get("refresh_token") else None
                
                if not decrypted_refresh_token:
                    logger.warning("No refresh token available for stopping watch")
                    # Continue to delete database entry even if we can't stop the watch
                else:
                    creds = Credentials(
                        token=decrypted_access_token,
                        refresh_token=decrypted_refresh_token,
                        token_uri=user["email_config"]["gmail_config"].get("token_uri", "https://oauth2.googleapis.com/token"),
                        client_id=user["email_config"]["gmail_config"].get("client_id", self.client_id),
                        client_secret=user["email_config"]["gmail_config"].get("client_secret", self.client_secret),
                        scopes=user["email_config"]["gmail_config"].get("scopes", self.scopes)
                    )

                    # Stop watching mailbox
                    service = build('gmail', 'v1', credentials=creds)
                    try:
                        service.users().stop(userId='me').execute()
                        logger.info(f"Successfully stopped Gmail watch for user {user_id}")
                    except Exception as stop_error:
                        logger.warning(f"Error stopping Gmail watch: {str(stop_error)}")
                        # Continue to delete database entry even if stop fails
            except Exception as cred_error:
                logger.error(f"Error creating credentials for delete: {str(cred_error)}")
                # Continue to delete database entry even if we can't stop the watch

            # Delete subscription from database
            result = await self.db.gmail_subscriptions.delete_one({"user_id": user_id})
            logger.info(f"Deleted {result.deleted_count} subscription(s) from database")

            return {
                "status": "success",
                "message": "Subscription deleted successfully",
                "deleted_count": result.deleted_count
            }

        except Exception as e:
            logger.error(f"Error deleting Gmail subscription: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
