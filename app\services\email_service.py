import logging
import base64
import time
from datetime import datetime, timezone
import requests
from fastapi import HTT<PERSON>Exception
from app.utils.security import fernet
from app.services.outlook_service import OutlookService
from app.services.imap_service import ImapService
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.utils.security import decrypt_password, fernet
from typing import List, Optional, Dict, Any
from bson import ObjectId
import aiohttp
from googleapiclient.discovery import build
import os
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
from fastapi import HTTPException
from pathlib import Path

logger = logging.getLogger(__name__)

class EmailService:

    @staticmethod
    def serialize_mongo_document(doc):
        """
        Recursively convert MongoDB document to a JSON-serializable dictionary
        """
        if doc is None:
            return None
        
        # Create a copy to avoid modifying the original document
        serialized = doc.copy() if hasattr(doc, 'copy') else dict(doc)
        
        # Convert ObjectId to string
        if '_id' in serialized:
            serialized['_id'] = str(serialized['_id'])
        
        # Convert datetime to ISO format string
        # Handle fetched_date with created_at as fallback
        date_fields = ['date']
        if 'fetched_date' in serialized and serialized['fetched_date']:
            date_fields.append('fetched_date')
        elif 'created_at' in serialized and serialized['created_at']:
            # Use created_at as fallback for fetched_date
            serialized['fetched_date'] = serialized['created_at']
            date_fields.append('fetched_date')
        
        for key in date_fields:
        # for key in ['date', 'fetched_date']:
            if key in serialized and serialized[key]:
                if hasattr(serialized[key], 'isoformat'):
                    serialized[key] = serialized[key].isoformat()
        
        # Recursively handle nested dictionaries and lists
        for key, value in serialized.items():
            if isinstance(value, dict):
                serialized[key] = EmailService.serialize_mongo_document(value)
            elif isinstance(value, list):
                serialized[key] = [EmailService.serialize_mongo_document(item) if isinstance(item, dict) else item for item in value]
            # Handle email.header.Header objects
            elif str(type(value)) == "<class 'email.header.Header'>":
                serialized[key] = str(value)
            # Handle any other non-serializable objects
            elif not isinstance(value, (str, int, float, bool, type(None))):
                serialized[key] = str(value)
        
        return serialized

    @staticmethod
    async def fetch_emails(
        db: AsyncIOMotorDatabase, 
        user_id: str, 
        page: int = 1, 
        page_size: int = 10, 
        refresh: bool = False, 
        status: str = None,
        source: Optional[str] = None,
        category: Optional[str] = None,
        sentiment: Optional[str] = None,
        urgency: Optional[str] = None,
        search: Optional[str] = None,
        exclude: Optional[str] = None,
        conversation_id: Optional[str] = None,
        include_signed_urls: bool = True,
        
    ):
        """
        Fetch emails with pagination and optional refresh from email server
        
        Args:
            db: Database instance
            user_id: ID of the user
            page: Page number (1-based)
            page_size: Number of items per page
            refresh: Whether to refresh from email server
            status: Filter by status
            category: Filter by category
            sentiment: Filter by sentiment
            urgency: Filter by urgency
            search: Search term
            exclude: Exclude emails matching this pattern
            include_signed_urls: Whether to include signed URLs for attachments
            conversation_id: Filter by conversation ID            
        Returns:
            Dict containing emails and pagination info
        """
        print("conversation_id: -----", conversation_id)
        start_time = time.time()
        print(f"[TIMING] fetch_emails started at {start_time}")
        print("source: ", source)
        
        try:
            # Step 1: Get user and validate email config
            step_start = time.time()
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 1 - Get user: {step_duration:.3f}s")
            print("user: ", user)
            
            if not user or "email_config" not in user:
                raise HTTPException(status_code=404, detail="User email configuration not found")

            # Step 2: Determine email provider and address
            step_start = time.time()
            email_provider = user["email_config"].get("provider", "gmail")
            user_email = None
        
            if email_provider == "outlook" and "outlook_config" in user["email_config"]:
                user_email = user["email_config"]["outlook_config"].get("email")
            elif email_provider == "gmail" and "gmail_config" in user["email_config"]:
                user_email = user["email_config"]["gmail_config"].get("email")
            elif email_provider == "imap" and "imap_config" in user["email_config"]:
                user_email = user["email_config"]["imap_config"].get("email")
            
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 2 - Determine email provider ({email_provider}): {step_duration:.3f}s")
                
            if not user_email:
                raise HTTPException(status_code=404, detail="User email address not found in configuration")

            # Step 3: Build query filters
            step_start = time.time()
            from app.utils.email_query_builder import (
                build_base_query, add_status_filter, add_search_filter,
                add_agent_filters, build_aggregation_pipeline, add_source_filter, add_conv_filter
            )
            
            query_filter = build_base_query(user_id, user_email, exclude)
            add_status_filter(query_filter, status)
            add_source_filter(query_filter, source)
            add_search_filter(query_filter, search)
            add_conv_filter(query_filter,conversation_id)
            add_agent_filters(query_filter, {
                "Classification Specialist": category,
                "Sentiment Analysis Specialist": sentiment,
                "Urgency Type Specialist": urgency
            })
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 3 - Build query filters: {step_duration:.3f}s")

            # Step 4: Handle refresh if requested
            if refresh:
                refresh_start = time.time()
                print(f"[TIMING] Starting email refresh at {refresh_start}")
                print("Refreshing emails from server...")
                
                if user.get("email_config", {}).get("provider") == "outlook":
                    refresh_duration = time.time() - refresh_start
                    print(f"[TIMING] Outlook refresh redirect: {refresh_duration:.3f}s")
                    return await EmailService._fetch_outlook_emails(
                        db, user_id, page, page_size, refresh, status
                    )
                else:
                    await EmailService._refresh_emails_from_server(db, user)
                    refresh_duration = time.time() - refresh_start
                    print(f"[TIMING] Step 4 - Email refresh completed: {refresh_duration:.3f}s")

            # Step 5: Execute aggregation pipeline
            step_start = time.time()
            start_idx = (page - 1) * page_size
            pipeline = build_aggregation_pipeline(query_filter, status, start_idx, page_size)
            print("Executing aggregation pipeline...", pipeline)
            
            pipeline_start = time.time()
            cursor = db.emails.aggregate(pipeline, allowDiskUse=True)
            pipeline_duration = time.time() - pipeline_start
            print(f"[TIMING] Aggregation pipeline execution: {pipeline_duration:.3f}s")
            
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 5 - Execute aggregation pipeline: {step_duration:.3f}s")

            # Step 6: Get total count
            step_start = time.time()
            total_emails = await db.emails.count_documents(query_filter)
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 6 - Get total count ({total_emails}): {step_duration:.3f}s")
            
            # Step 7: Get latest email
            step_start = time.time()
            latest_email = await db.emails.find_one(
                {"user_id": user_id},
                sort=[("date", -1)]
            )
            latest_fetched_date = None
            if latest_email:
                # Try to get fetched_date, if not found try created_at as fallback
                latest_fetched_date = latest_email.get("fetched_date") or latest_email.get("created_at")
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 7 - Get latest email: {step_duration:.3f}s")
            
            # Step 8: Serialize emails
            step_start = time.time()
            emails = []
            async for email in cursor:
                email_dict = EmailService.serialize_mongo_document(email)
                
                # Add signed URLs for attachments if requested
                if include_signed_urls and email_dict.get('attachments'):
                    from app.services.attachment_service import AttachmentService
                    attachment_service = AttachmentService()
                    
                    for attachment in email_dict['attachments']:
                        if 's3_key' in attachment and not attachment.get('signed_url'):
                            attachment['signed_url'] = await attachment_service.get_signed_url(
                                attachment['s3_key']
                            )
                
                emails.append(email_dict)
            
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 8 - Serialize {len(emails)} emails: {step_duration:.3f}s")
            
            # Step 9: Format response
            step_start = time.time()
            response = {
                "emails": emails,
                "total": total_emails,
                "page": page,
                "page_size": page_size,
                "has_more": (start_idx + page_size) < total_emails,
                "refreshed": refresh,
                "latest_fetched_date": latest_fetched_date.isoformat() if latest_fetched_date else None,
                "signed_urls_expire_in": int(os.getenv('SIGNED_URL_EXPIRATION', 3600))
            }
            step_duration = time.time() - step_start
            print(f"[TIMING] Step 9 - Format response: {step_duration:.3f}s")
            
            total_duration = time.time() - start_time
            print(f"[TIMING] fetch_emails completed in {total_duration:.3f}s")
            
            return response
            
        except HTTPException:
            total_duration = time.time() - start_time
            print(f"[TIMING] fetch_emails failed with HTTPException after {total_duration:.3f}s")
            raise
        except Exception as e:
            total_duration = time.time() - start_time
            print(f"[TIMING] fetch_emails failed with Exception after {total_duration:.3f}s")
            logger.error(f"Error fetching emails: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error fetching emails: {str(e)}")

    @staticmethod
    async def _refresh_emails_from_server(db: AsyncIOMotorDatabase, user: dict) -> None:
        """Helper method to refresh emails from mail server"""
        email_config = user["email_config"]
        email_provider = email_config.get("provider", "gmail")
        
        try:
            if email_provider == "outlook":
                # Outlook implementation would go here
                # This is unchanged from the original implementation
                pass
            elif "imap_config" in email_config:
                # Use ImapService for IMAP accounts
                emails = await ImapService.fetch_emails(email_config, max_results=50)
                # print("len(emails)",len(emails))
                
                # Get existing message IDs globally (across all users)
                existing_ids = set(await db.emails.distinct(
                    "message_id"
                ))
                # print("existing_ids",len(existing_ids))
                
                # Process and store new emails
                for email_data in emails:
                    if email_data["message_id"] in existing_ids:
                        continue
                    
                    # Add user_id to email data
                    email_data["user_id"] = str(user["_id"])
                    email_data["status"] = "unprocessed"
                    
                    # Upsert email
                    await db.emails.update_one(
                        {"message_id": email_data["message_id"]},
                        {
                            "$setOnInsert": {k: v for k, v in email_data.items() if k != "fetched_date"},
                            "$set": {"fetched_date": email_data["fetched_date"]}
                        },
                        upsert=True
                    )
            else:
                # Gmail implementation using Google API
                from app.services.gmail_service import GmailService
                gmail_service = GmailService()
                access_token = await gmail_service.get_valid_token(db, str(user["_id"]))

                # Build Gmail service
                from google.oauth2.credentials import Credentials
                creds = Credentials(token=access_token)
                service = build('gmail', 'v1', credentials=creds)

                # Fetch unread messages
                results = service.users().messages().list(userId='me', q='is:unread', maxResults=100).execute()
                messages = results.get('messages', [])

                # Get existing message IDs globally (across all users)
                existing_ids = set(await db.emails.distinct(
                    "message_id"
                ))

                # Process new emails
                current_time = datetime.utcnow()
                for message in messages:
                    msg_id = message['id']

                    if msg_id in existing_ids:
                        continue

                    # Get the message details
                    msg = service.users().messages().get(userId='me', id=msg_id, format='full').execute()

                    # Parse email data
                    headers = msg['payload']['headers']
                    subject = next((h['value'] for h in headers if h['name'] == 'Subject'), '')
                    sender = next((h['value'] for h in headers if h['name'] == 'From'), '')
                    date_str = next((h['value'] for h in headers if h['name'] == 'Date'), '')
                    in_reply_to = next((h['value'] for h in headers if h['name'] == 'In-Reply-To'), None)
                    references = next((h['value'] for h in headers if h['name'] == 'References'), None)

                    # Decode the email body
                    body = ''
                    if 'parts' in msg['payload']:
                        for part in msg['payload']['parts']:
                            if part['mimeType'] == 'text/plain':
                                body = base64.urlsafe_b64decode(part['body']['data'].encode('utf-8')).decode('utf-8')
                                break
                    else:
                        if msg['payload']['mimeType'] == 'text/plain':
                            body = base64.urlsafe_b64decode(msg['payload']['body']['data'].encode('utf-8')).decode('utf-8')

                    # Store email
                    email_data = {
                        "message_id": msg_id,
                        "thread_id": msg['threadId'],
                        "subject": subject,
                        "sender": sender,
                        "recipient": email_config["gmail_config"]["email"],
                        "date": EmailService._parse_email_date(date_str),
                        "fetched_date": current_time,
                        "body": body,
                        "is_read": False,
                        "folder": "INBOX",
                        "status": "unprocessed",
                        "user_id": str(user["_id"]),
                        "headers": {h['name'].lower(): h['value'] for h in headers if h['name'].lower() not in ["subject", "from", "to", "date", "body"]},
                        "in_reply_to": in_reply_to,
                        "references": references
                    }

                    # Upsert email
                    await db.emails.update_one(
                        {"message_id": email_data["message_id"]},
                        {
                            "$setOnInsert": {k: v for k, v in email_data.items() if k != "fetched_date"},
                            "$set": {"fetched_date": email_data["fetched_date"]}
                        },
                        upsert=True
                    )

        except Exception as e:
            logger.error(f"Error refreshing emails: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to refresh emails: {str(e)}")
    @staticmethod
    def _parse_email_date(date_str: str) -> datetime:
        """Helper method to parse email date strings"""
        date_formats = [
            "%a, %d %b %Y %H:%M:%S %z",  # RFC 2822
            "%a, %d %b %Y %H:%M:%S %Z",  # With timezone name
            "%a, %d %b %Y %H:%M:%S GMT", # GMT format
            "%a, %d %b %Y %H:%M:%S +0000", # Explicit +0000
            "%d %b %Y %H:%M:%S %z",      # Without weekday
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        logger.warning(f"Could not parse date: {date_str}")
        return datetime.utcnow()
            
    @staticmethod
    async def regenerate_draft(db: AsyncIOMotorDatabase, user_id: str, email_id: str, feedback: str) -> dict:
        """Regenerate draft response using Claude AI with feedback and update existing draft"""
        try:
            logger.info(f"Starting draft regeneration process for email_id: {email_id}")
            
            # Get original email and draft
            logger.debug(f"Fetching email document with id: {email_id} and user_id: {user_id}")
            email = await db.emails.find_one({"_id": ObjectId(email_id), "user_id": user_id})
            if not email:
                logger.error(f"Email not found for id: {email_id}")
                raise HTTPException(status_code=404, detail="Email not found")
            logger.debug(f"Found email: {email.get('subject', 'No subject')}")

            logger.debug(f"Fetching draft document for email_id: {email_id}")
            draft = await db.drafts.find_one({"email_id": email_id, "user_id": user_id})
            if not draft:
                logger.error(f"No draft found for email_id: {email_id}")
                raise HTTPException(status_code=404, detail="No existing draft found to regenerate")
            logger.debug("Found existing draft")

            try:
                # Use Claude service to generate new response
                logger.info("Initializing Claude service")
                from app.services.claude_service import ClaudeService
                claude_service = ClaudeService(db)
                
                # Get email category and sentiment
                category = email.get("category", "unknown")
                sentiment = email.get("sentiment", {})
                logger.debug(f"Email category: {category}, sentiment: {sentiment}")
                
                # Include feedback and previous response in context
                context = {
                    **email.get("context", {}),
                    "previous_response": draft.get("response_text", ""),
                    "category_template": email.get("category_template", "")
                }
                logger.debug(f"Built context for response generation: {context}")
                
                # Get previous response from emails collection
                previous_response = email.get("response")
                if not previous_response:
                    logger.warning("No previous response found in email document")
                
                # Generate new response incorporating feedback and previous response
                logger.info("Generating new response with Claude AI")
                # Include full state context
                # Build context with available data
                full_context = {
                    "email": email,
                    "classification": category,
                    "sentiment": sentiment,
                    "context": context,
                    "action": None,  # These fields are not available in this context
                    "data_validation": None,
                    "has_missing_fields": False,
                    "workflow_id": None,
                    "processing_id": None
                }
                new_response = await claude_service.generate_response(
                    email["body"],
                    category,
                    sentiment,
                    full_context,
                    user_id,
                    feedback=feedback,
                    previous_response=previous_response
                )
                logger.debug("Successfully generated new response")
            except Exception as e:
                logger.error(f"Error in Claude service: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to generate response: {str(e)}")

            try:
                # Update existing draft in email server
                logger.info("Updating draft in email server")
                draft_id = await EmailService.update_draft(
                    db, 
                    user_id, 
                    email_id, 
                    new_response,
                    create_new=False
                )
                logger.info("Draft successfully updated")

                return {
                    "draft_id": draft_id,
                    "response": new_response
                }
            except Exception as e:
                logger.error(f"Error updating draft: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to update draft: {str(e)}")

        except Exception as e:
            logger.error(f"Error regenerating draft: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to regenerate draft: {str(e)}")

    @staticmethod
    async def update_draft(
    db: AsyncIOMotorDatabase,
    user_id: str,
    email_id: str,
    new_content: str,
    create_new: bool = False
) -> dict:
        """Update draft with version history tracking"""
        """Update existing draft or create new draft with content."""
        try:
            # Verify email exists and belongs to user
            email = await db.emails.find_one({"_id": ObjectId(email_id), "user_id": user_id})
            if not email:
                raise HTTPException(status_code=404, detail="Email not found")

            # Verify user email configuration
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if not user or "email_config" not in user:
                raise HTTPException(status_code=404, detail="User email configuration not found")

            email_config = user["email_config"]
            draft_server_id = None

            if not create_new:
                # Verify existing draft
                existing_draft = await db.drafts.find_one({"email_id": email_id, "user_id": user_id})
                if not existing_draft:
                    raise HTTPException(status_code=404, detail="No existing draft found to update")

                draft_server_id = existing_draft.get("draft_server_id")

            # Handle based on email provider
            email_provider = email_config.get("provider", "gmail")
            
            if email_provider == "outlook":
                # Outlook implementation would go here
                # This is unchanged from the original implementation
                pass
            else:
                # Use ImapService for IMAP/Gmail
                if not create_new and draft_server_id and "server_uid" in draft_server_id:
                    # Update existing draft
                    draft_server_id = await ImapService.update_draft(
                        email_config,
                        draft_server_id,
                        email.get("sender"),
                        f"Re: {email.get('subject', '')}",
                        new_content
                    )
                else:
                    # Create a new draft
                    draft_server_id = await ImapService.create_draft(
                        email_config,
                        email.get("sender"),
                        f"Re: {email.get('subject', '')}",
                        new_content,
                        email.get('message_id'),
                        email.get('message_id')
                    )

            # Update or insert draft in database with version history
            current_time = datetime.utcnow()
            
            # Handle draft updates
            if not create_new and draft_server_id:
                # Get existing response history or initialize it
                existing_responses = existing_draft.get("response_history", [])
                
                # Add current response to history
                response_entry = {
                    "content": new_content,
                    "timestamp": current_time,
                    "version": len(existing_responses) + 1
                }
                
                # Update draft document
                await db.drafts.update_one(
                    {"_id": existing_draft["_id"]},
                    {
                        "$set": {
                            "response_text": new_content,
                            "draft_server_id": draft_server_id,
                            "updated_at": current_time,
                        },
                        "$push": {
                            "response_history": response_entry
                        }
                    }
                )
                draft_id = str(existing_draft["_id"])
            else:
                # Create new draft
                draft_id = await EmailService.create_draft(
                    db, user_id, email_id, new_content, update_existing=False
                )
            
            # Update email document with new response
            await db.emails.update_one(
                {"_id": ObjectId(email_id)},
                {
                    "$set": {
                        "response": new_content,
                        "updated_at": current_time,
                        "status": "drafted"
                    }
                }
            )

            return {
                "draft_id": draft_id,
                "draft_server_id": draft_server_id,
                "response": new_content,
            }

        except Exception as e:
            logger.error(f"Error updating draft: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update draft: {str(e)}")

    @staticmethod
    async def create_draft(
        db: AsyncIOMotorDatabase, 
        user_id: str, 
        email_id: str, 
        response_text: str,
        update_existing: bool = False
    ):
        """
        Update existing draft or create new draft with content.
        Maintains response history and updates email collection.
        """
        try:
            logger.info(f"Starting draft creation process:")
            logger.info(f"- Email ID: {email_id}")
            logger.info(f"- User ID: {user_id}")
            logger.info(f"- Update existing: {update_existing}")
            logger.debug(f"- Response text length: {len(response_text)}")
            
            # Get user's configuration with detailed logging
            logger.debug("Fetching user configuration")
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                logger.error(f"User not found for ID: {user_id}")
                raise HTTPException(
                    status_code=404, 
                    detail="User not found. Please check your authentication."
                )
            logger.debug(f"User: {user}")
            
            # Validate email configuration
            email_config = user.get('email_config')
            print("email_config: ", email_config)
            if not email_config:
                logger.error("Email configuration section missing")
                raise HTTPException(
                    status_code=400,
                    detail="Email configuration is incomplete. Please update your email settings."
                )
            
            required_fields = ['email', 'password']
            missing_fields = [field for field in required_fields if not email_config.get(field)]
            if missing_fields:
                logger.error(f"Missing required email configuration fields: {missing_fields}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing required email configuration: {', '.join(missing_fields)}"
                )

            # Get original email details
            logger.debug("Fetching email details")
            email = await db.emails.find_one({"_id": ObjectId(email_id)})
            if not email:
                logger.error(f"Email not found with ID: {email_id}")
                raise HTTPException(status_code=404, detail="Email not found")
            logger.debug(f"Email subject: {email.get('subject', 'N/A')}")

            email_provider = user.get("email_provider", "gmail")
            draft_id = None

            if email_provider == "outlook":
                # Create Outlook draft using Microsoft Graph API
                outlook_service = OutlookService()
                access_token = await outlook_service.get_valid_token(db, user_id)
                
                # Get email configuration
                email_config = user.get("email_config", {})
                if not email_config or "email" not in email_config:
                    raise HTTPException(status_code=404, detail="Email configuration not found")
                
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                draft_message = {
                    "subject": f"Re: {email.get('subject', '')}",
                    "toRecipients": [{"emailAddress": {"address": email.get('sender')}}],
                    "body": {
                        "content": response_text,
                        "contentType": "text"
                    },
                    "conversationId": email.get('message_id'),
                    "internetMessageId": f"<{email_config['email']}.{datetime.now().timestamp()}@{email_config['email'].split('@')[1]}>",
                    "inReplyToId": email.get('message_id')
                }
                
                response = requests.post(
                    'https://graph.microsoft.com/v1.0/me/messages/draft',
                    headers=headers,
                    json=draft_message
                )
                
                if response.status_code != 201:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create Outlook draft: {response.text}"
                    )
                
                draft_id = response.json().get('id')

            else:
                # Gmail/IMAP draft creation using ImapService
                try:
                    # Create draft using ImapService
                    draft_server_id = await ImapService.create_draft(
                        email_config,
                        email.get('sender'),
                        f"Re: {email.get('subject', '')}",
                        response_text,
                        email.get('message_id'),
                        email.get('message_id')
                    )
                    
                    # Add custom metadata to draft_server_id
                    draft_server_id["X-Email-ID"] = str(email.get('_id'))
                    draft_server_id["X-Draft-Type"] = "response"
                    
                    draft_id = draft_server_id
                    
                except Exception as e:
                    logger.error(f"Error creating IMAP draft: {str(e)}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create draft: {str(e)}"
                    )

            if update_existing:
                # Get existing draft
                existing_draft = await db.drafts.find_one({
                    "email_id": email_id,
                    "user_id": user_id
                })
                if not existing_draft:
                    raise HTTPException(
                        status_code=404,
                        detail="No existing draft found to update"
                    )
                # Update existing draft on email server
                if email_provider == "outlook":
                    # Update existing Outlook draft
                    headers = {
                        'Authorization': f'Bearer {access_token}',
                        'Content-Type': 'application/json'
                    }
                    
                    # Update existing draft using PATCH
                    update_url = f'https://graph.microsoft.com/v1.0/me/messages/{existing_draft["draft_server_id"]}'
                    update_data = {
                        "subject": f"Re: {email.get('subject', '')}",
                        "toRecipients": [{"emailAddress": {"address": email.get('sender')}}],
                        "body": {
                            "content": response_text,
                            "contentType": "text"
                        }
                    }
                    
                    update_response = requests.patch(update_url, headers=headers, json=update_data)
                    if update_response.status_code != 200:
                        raise HTTPException(
                            status_code=500,
                            detail=f"Failed to update Outlook draft: {update_response.text}"
                        )
                    
                    draft_id = existing_draft["draft_server_id"]
                    
                else:
                    # Use ImapService for IMAP/Gmail draft update
                    try:
                        # Get the draft server ID from the existing draft
                        draft_server_id = existing_draft.get("draft_server_id", {})
                        
                        # Update the draft using ImapService
                        updated_draft_server_id = await ImapService.update_draft(
                            email_config,
                            draft_server_id,
                            email.get('sender'),
                            f"Re: {email.get('subject', '')}",
                            response_text
                        )
                        
                        # Set the draft_id to the updated server ID
                        draft_id = updated_draft_server_id
                        
                    except Exception as e:
                        logger.error(f"Error updating IMAP draft: {str(e)}")
                        raise HTTPException(
                            status_code=500,
                            detail=f"Failed to update draft on mail server: {str(e)}"
                        )

                # Update existing draft record
                await db.drafts.update_one(
                    {"_id": existing_draft["_id"]},
                    {
                        "$set": {
                            "response_text": response_text,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                local_draft_id = str(existing_draft["_id"])
            else:
                # Create new draft record
                draft_data = {
                    "user_id": user_id,
                    "email_id": email_id,
                    "response_text": response_text,
                    "status": "draft",
                    "draft_server_id": {
                        "email_provider": "gmail",
                        "folder": draft_id.get("folder", "[Gmail]/Drafts"),
                        "server_uid": draft_id.get("server_uid")
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                    "response_history": [{
                        "content": response_text,
                        "timestamp": datetime.now(timezone.utc),
                        "version": 1
                    }]
                }
                logger.info(f"Creating draft record with data: {draft_data}")

                result = await db.drafts.insert_one(draft_data)
                local_draft_id = str(result.inserted_id)

            # Update email status
            await db.emails.update_one(
                {"_id": ObjectId(email_id)},
                {
                    "$set": {
                        "status": "drafted",
                        "draft_id": local_draft_id,
                        "draft_server_id": draft_id,
                        "updated_date": datetime.now(timezone.utc)
                    }
                }
            )

            # No need to manually close IMAP connection as ImapService handles it

            return local_draft_id

        except Exception as e:
            logger.error(f"Error creating draft: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create draft: {str(e)}")

    @staticmethod
    async def store_new_email(db: AsyncIOMotorDatabase, user_id: str, email_data: dict):
        """Store new email from notification if it doesn't already exist"""
        try:
            print("email_data", email_data)
            # Determine if the email is from Gmail or Outlook based on the presence of certain keys
            if "id" in email_data or "messageId" in email_data:
                # Outlook format
                message_id = email_data.get("id") or email_data.get("messageId")
                sender = email_data.get("from", {}).get("emailAddress", {}).get("address", "")
                sender_name = email_data.get("from", {}).get("emailAddress", {}).get("name", "")
                recipient = email_data.get("toRecipients", [{}])[0].get("emailAddress", {}).get("address", "")
                body = email_data.get("body", {}).get("content", "")
                date = email_data.get("receivedDateTime")
                is_read = email_data.get("isRead", False)
                subject = email_data.get("subject", "")
                thread_id = email_data.get("threadId", "")
                in_reply_to = email_data.get("in_reply_to", None)
                references = email_data.get("references",  None)
                # labels = []  # Outlook doesn't provide labels
            else:
                # Gmail format
                message_id = email_data.get("message_id")
                sender = email_data.get("sender")
                sender_name = email_data.get("sender")  # Gmail data doesn't directly provide sender name
                recipient = email_data.get("recipient")
                body = email_data.get("body")
                date = email_data.get("date")
                is_read = not ("UNREAD" in email_data.get("labels", []))
                subject = email_data.get("subject", "")
                thread_id = email_data.get("thread_id", "")
                in_reply_to = email_data.get("in_reply_to", None)
                references = email_data.get("references",  None)
                # labels = email_data.get("labels", [])

            print("Message ID:", message_id)

            # Check if email already exists globally (for any user)
            existing_email = await db.emails.find_one({
                "message_id": message_id
            })

            print(f"Existing email: {existing_email}")
            if existing_email:
                logger.info(f"Skipping duplicate email with message_id: {message_id} (already exists in database)")
                return None

            # Create email document
            email_doc = {
                "message_id": message_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "subject": subject,
                "sender": sender,
                "sender_name": sender_name,
                "recipient": recipient,
                "date": date,
                "fetched_date": datetime.utcnow(),
                "body": body,
                "is_read": is_read,
                "folder": "INBOX",
                "status": "unprocessed",
                "in_reply_to": in_reply_to,
                "references": references,
                "attachments": email_data.get("attachments", []),
                "inline_images": email_data.get("inline_images", {}),
                # "labels": labels
            }

            # Insert new email
            result = await db.emails.insert_one(email_doc)
            email_doc['_id'] = str(result.inserted_id)
            logger.info(f"Stored new email with message_id: {message_id}")
            print(f"Stored new email with message_id: {message_id}")
            # Return the stored email document
            return email_doc

        except Exception as e:
            logger.error(f"Error storing new email: {str(e)}")
            raise

    @staticmethod
    async def _fetch_outlook_emails(db: AsyncIOMotorDatabase, user_id: str, page: int, page_size: int, refresh: bool, status: str):
        """Fetch emails from Outlook using Microsoft Graph API"""
        try:
            print("Starting Outlook email fetch-------")
            logger.info(f"Starting Outlook email fetch for user {user_id}")
            logger.info(f"Parameters: page={page}, page_size={page_size}, refresh={refresh}, status={status}")
            
            outlook_service = OutlookService()
            logger.info("Getting valid access token")
            access_token = await outlook_service.get_valid_token(db, user_id)
            print(f"Access token received (length: {len(access_token) if access_token else 0})")
            logger.info(f"Access token received (length: {len(access_token) if access_token else 0})")

            if refresh:
                print("Refreshing Outlook emails-------",)
                # Fetch new emails from Outlook
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json',
                    'Prefer': 'outlook.body-content-type="text"'  
                }
                
                # Use Microsoft Graph API to fetch unread messages
                url = 'https://graph.microsoft.com/v1.0/me/messages?$filter=isRead eq false&$select=id,subject,from,toRecipients,receivedDateTime,isRead,body'

                logger.info(f"Making Graph API request for unread messages to: {url}")
                logger.debug(f"Using headers: {headers}")
                
                response = requests.get(url, headers=headers)
                # print("Response received from Graph API",)
                print(f"Response status code: {response.status_code}, response text: {response.text}")
                logger.info(f"Graph API response status: {response.status_code}")
                
                if response.status_code != 200:
                    error_content = response.text
                    logger.error(f"Graph API error response: {error_content}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Graph API Error: {error_content}"
                    )

                response_data = response.json()
                logger.debug(f"Graph API response data: {response_data}")
                emails_data = response_data.get('value', [])
                logger.info(f"Retrieved {len(emails_data)} emails from Graph API")
                
                # Get existing message IDs for this user
                existing_ids = set(await db.emails.distinct(
                    "message_id",
                    {"user_id": user_id}
                ))
                logger.info(f"Found {len(existing_ids)} existing emails in database")

                # Process and store only new emails
                for email in emails_data:
                    message_id = email.get('id')
                    
                    # Skip if email already exists
                    if message_id in existing_ids:
                        logger.debug(f"Skipping existing email with message_id: {message_id}")
                        continue
                        
                    logger.info(f"Processing new email with message_id: {message_id}")
                    
                    email_data = {
                        "message_id": message_id,
                        "subject": email.get('subject', ''),
                        "sender": email.get('from', {}).get('emailAddress', {}).get('address', ''),
                        "recipient": email.get('toRecipients', [{}])[0].get('emailAddress', {}).get('address', ''),
                        "date": email.get('receivedDateTime'),
                        "fetched_date": datetime.utcnow(),
                        "body": email.get('body', {}).get('content', ''), 
                        "is_read": email.get('isRead', False),
                        "folder": "INBOX",
                        "status": "unprocessed",
                        "user_id": user_id
                    }

                    # Check if email already exists
                    existing_email = await db.emails.find_one({"message_id": message_id})
                    if existing_email:
                        logger.info(f"Skipping existing email with message_id: {message_id}")
                        continue
                        
                    # Insert new email
                    await db.emails.insert_one(email_data)
                    logger.info(f"Inserted new email with message_id: {message_id}")

            # Fetch emails from database with pagination
            
            
            # Get user's current email address
            
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            
            if not user or "email_config" not in user or "email" not in user["email_config"]["outlook_config"]:
                raise HTTPException(status_code=404, detail="User email configuration not found")
              
            current_email_address = user["email_config"]["outlook_config"].get("email")

            total_emails = await db.emails.count_documents({"user_id": user_id,"recipient": current_email_address,"status": "unprocessed"})
            start_idx = (page - 1) * page_size
            
            
            # Build query filter with recipient
            query_filter = {
                "user_id": user_id,
                "recipient": current_email_address,
            }
            
            if status:
                query_filter["status"] = "unprocessed"

            db_emails = await db.emails.find(query_filter).sort("date", -1).skip(start_idx).limit(page_size).to_list(length=page_size)
            # db_emails = await db.emails.find(query_filter).to_list(length=page_size)
            print("db_emails----------",db_emails)
            
            # Get latest updated_date
            latest_email = await db.emails.find_one(
                {"user_id": user_id},
                sort=[("fetched_date", -1)]
            )
            latest_updated_date = latest_email["fetched_date"] if latest_email else None
            
            # Convert emails to serializable format
            serialized_emails = []
            for email in db_emails:
                email_dict = dict(email)
                # Convert ObjectId fields to strings
                if "_id" in email_dict:
                    email_dict["_id"] = str(email_dict["_id"])
                if "user_id" in email_dict:
                    email_dict["user_id"] = str(email_dict["user_id"])
                # Ensure dates are datetime objects
                if "date" in email_dict and email_dict["date"]:
                    email_dict["date"] = email_dict["date"].isoformat() if hasattr(email_dict["date"], 'isoformat') else email_dict["date"]
                if "fetched_date" in email_dict and email_dict["fetched_date"]:
                    email_dict["fetched_date"] = email_dict["fetched_date"].isoformat() if hasattr(email_dict["fetched_date"], 'isoformat') else email_dict["fetched_date"]
                serialized_email = EmailService.serialize_mongo_document(email_dict)
                if serialized_email:
                    serialized_emails.append(serialized_email)
            
            has_more = (start_idx + page_size) < total_emails

            # Convert latest_updated_date to ISO format if it exists
            if latest_updated_date and hasattr(latest_updated_date, 'isoformat'):
                latest_updated_date = latest_updated_date.isoformat()
            
            return {
                "emails": serialized_emails,
                "total": total_emails,
                "page": page,
                "page_size": page_size,
                "has_more": has_more,
                "refreshed": refresh,
                "latest_updated_date": latest_updated_date
            }

        except Exception as e:
            logger.error(f"Error fetching Outlook emails: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error fetching Outlook emails: {str(e)}")

    @staticmethod
    async def delete_draft(db: AsyncIOMotorDatabase, user_id: str, email_id: str) -> bool:
        """Delete draft message from mail server and update database"""
        try:
            # Get draft details
            draft = await db.drafts.find_one({"email_id": email_id, "user_id": user_id})
            if not draft:
                raise HTTPException(status_code=404, detail="Draft not found")

            # Get user's email configuration
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            if not user or "email_config" not in user:
                raise HTTPException(status_code=404, detail="User email configuration not found")

            email_config = user["email_config"]
            email_provider = email_config.get("provider", "gmail")

            if email_provider == "outlook":
                # Delete Outlook draft using Microsoft Graph API
                outlook_service = OutlookService()
                access_token = await outlook_service.get_valid_token(db, user_id)
                
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                draft_id = draft.get("draft_server_id")
                if not draft_id:
                    raise HTTPException(status_code=404, detail="Draft ID not found")
                
                response = requests.delete(
                    f'https://graph.microsoft.com/v1.0/me/messages/{draft_id}',
                    headers=headers
                )
                
                if response.status_code not in [204, 404]:  # 204: Deleted, 404: Already deleted
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to delete Outlook draft: {response.text}"
                    )

            else:
                # Use ImapService for Gmail/IMAP draft deletion
                draft_server_id = draft.get("draft_server_id", {})
                success = await ImapService.delete_draft(email_config, draft_server_id)
                
                if not success:
                    logger.warning(f"Could not find draft to delete on mail server: {draft_server_id}")

            # Update database records
            await db.drafts.delete_one({"_id": draft["_id"]})
            await db.emails.update_one(
                {"_id": ObjectId(email_id)},
                {
                    "$set": {
                        "status": "unprocessed",
                        "updated_date": datetime.utcnow()
                    },
                    "$unset": {
                        "draft_id": "",
                        "draft_server_id": ""
                    }
                }
            )

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting draft: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to delete draft: {str(e)}")
    @staticmethod
    async def send_reply(db, reply_data: dict) -> dict:
        """Send email reply or save as draft using appropriate email service"""
        try:
            # Handle direct message vs reply
            if reply_data.get("email_id", None):
                print("reply_data", reply_data)

                email = await db.emails.find_one({"_id": ObjectId(reply_data["email_id"])})
                if not email:
                    raise HTTPException(status_code=404, detail="Email not found")
                subject = f"Re: {email['subject']}"
                recipient_address = email["sender"]
                in_reply_to = email.get("message_id")
                references = email.get("references", in_reply_to)
            else:
                subject = reply_data.get("subject")
                recipient_address = reply_data.get("to")
                in_reply_to = reply_data.get("in_reply_to")
                references = reply_data.get("references")
                
                if not all([subject, recipient_address]):
                    raise HTTPException(
                        status_code=400, 
                        detail="Subject and recipient address required for direct message"
                    )

            # Get user details
            user = await db.users.find_one({"_id": ObjectId(reply_data["user_id"])})
            if not user or "email_config" not in user:
                raise HTTPException(status_code=404, detail="Email configuration not found")

            email_config = user["email_config"]
            email_provider = email_config.get("provider", "gmail")

            if email_provider == "outlook":
                # Outlook implementation
                # Get valid access token
                outlook_service = OutlookService()
                access_token = await outlook_service.get_valid_token(db, reply_data["user_id"])

                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }

                # Prepare the message
                message = {
                    "subject": subject,
                    "importance": "normal",
                    "body": {
                        "contentType": "text",
                        "content": reply_data["body"]
                    },
                    "toRecipients": [
                        {
                            "emailAddress": {
                                "address": recipient_address
                            }
                        }
                    ]
                }
                print("message", message)
                async with aiohttp.ClientSession() as session:
                    if reply_data["email_id"] == None or reply_data["is_draft"] == True:
                        # Save as draft
                        print("Saving as draft:-------00")
                        if reply_data.get("is_draft") == True:
                    # Save as draft
                            url = 'https://graph.microsoft.com/v1.0/me/messages'
                            message["isDraft"] = True  # Mark as a draft
                            print("Saving as draft:", message)

                            async with session.post(url, headers=headers, json=message) as response:
                                if response.status != 201:
                                    raise HTTPException(
                                        status_code=response.status,
                                        detail=f"Failed to create draft: {await response.text()}"
                                    )

                            return {
                                "status": "success",
                                "message": "Email saved as draft in Outlook"
                            }
                        else:
                            print("Sending email:========")
                            # Send email
                            url = 'https://graph.microsoft.com/v1.0/me/sendMail'
                            print("Sending email:", message)

                            async with session.post(url, headers=headers, json={"message": message}) as response:
                                if response.status != 202:
                                    raise HTTPException(
                                        status_code=response.status,
                                        detail=f"Failed to send email: {await response.text()}"
                                    )

                            return {
                                "status": "success",
                                "message": "Email sent successfully"
                            }

                    elif reply_data["email_id"] != None and reply_data["is_draft"] == False:
                        print("replydata =================")
                        # Send reply
                        url = f'https://graph.microsoft.com/v1.0/me/messages/{email.get("message_id")}/reply'
                        formatted_comment = reply_data["body"]
                        formatted_comment = formatted_comment.replace('\n', '<br>')

                        async with session.post(url, headers=headers, json={"comment": formatted_comment}) as response:
                            if response.status != 202:
                                raise HTTPException(
                                    status_code=response.status,
                                    detail=f"Failed to send reply: {await response.text()}"
                                )


                        return {
                            "status": "success",
                            "message": "Email sent successfully"
                        }
            else:
                # Use ImapService for IMAP/Gmail
                if "imap_config" in email_config:
                    # Use IMAP service directly
                    if reply_data.get("is_draft", False):
                        # Save as draft
                        draft_server_id = await ImapService.create_draft(
                            email_config,
                            recipient_address,
                            subject,
                            reply_data["body"],
                            in_reply_to,
                            references
                        )
                        
                        return {
                            "status": "success",
                            "message": "Email saved as draft",
                            "draft_id": draft_server_id.get("server_uid")
                        }
                    else:
                        # Send email
                        result = await ImapService.send_email(
                            email_config,
                            recipient_address,
                            subject,
                            reply_data["body"],
                            in_reply_to,
                            references
                        )
                        
                        return result
                else:
                    # Gmail implementation using Google API
                    # Get Gmail configuration
                    if "gmail_config" not in email_config:
                        raise HTTPException(status_code=404, detail="Gmail configuration not found")

                    # Get valid access token
                    from app.services.gmail_service import GmailService
                    gmail_service = GmailService()
                    access_token = await gmail_service.get_valid_token(db, reply_data["user_id"])

                    # Build Gmail service
                    from google.oauth2.credentials import Credentials
                    creds = Credentials(token=access_token)
                    service = build('gmail', 'v1', credentials=creds)

                    # Prepare message with headers
                    headers = [
                        f"From: me",
                        f"To: {recipient_address}",
                        f"Subject: {subject}"
                    ]

                    # Add In-Reply-To and References headers if available
                    if in_reply_to:
                        headers.append(f"In-Reply-To: {in_reply_to}")

                    if references:
                        headers.append(f"References: {references}")
                    # If no references but there is in_reply_to, use that as references too
                    elif in_reply_to:
                        headers.append(f"References: {in_reply_to}")

                    # Construct the full message
                    message_text = "\r\n".join(headers) + "\r\n\r\n" + reply_data['body']
                    message_bytes = message_text.encode('utf-8')
                    message_b64 = base64.urlsafe_b64encode(message_bytes).decode('utf-8')

                    # Create message
                    message = {'raw': message_b64}

                    try:
                        if reply_data.get("is_draft", False):
                            # Save as draft
                            draft = service.users().drafts().create(userId="me", body={"message": message}).execute()
                            print(f'Draft email created: {draft.get("id")}')
                            return {
                                "status": "success",
                                "message": "Email saved as draft in Gmail",
                                "draft_id": draft.get("id")
                            }
                        else:
                            # Send email
                            message = service.users().messages().send(userId="me", body=message).execute()
                            print(f'sent message to {recipient_address} Message Id: {message["id"]}')
                            return {
                                "status": "success",
                                "message": "Email sent successfully",
                                "message_id": message["id"]
                            }
                    except Exception as gmail_error:
                        logger.error(f"Error sending or saving Gmail email: {str(gmail_error)}")
                        raise HTTPException(
                            status_code=500,
                            detail=f"Failed to send/save email via Gmail: {str(gmail_error)}"
                        )

        except Exception as e:
            logging.error(f"Error in send_reply: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to process reply: {str(e)}")
