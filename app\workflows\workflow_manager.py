import logging
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import UpdateOne
from bson import ObjectId
from .states import WorkflowState, WorkflowTransition, WorkflowContext
from .lang_graph import EmailProcessingGraph
from fastapi import HTT<PERSON>Exception
from app.utils.thread_utils import ThreadManager

logger = logging.getLogger(__name__)
thread_manager = ThreadManager()

class WorkflowManager:
    def __init__(self, db: AsyncIOMotorDatabase):
        from app.config.db_pool import get_workflow_db
        self.db = get_workflow_db()  # Use dedicated workflow connection
        self.graph = EmailProcessingGraph(self.db)
        self.state_transitions = {
            WorkflowState.INITIATED: {
                WorkflowTransition.FETCH_EMAILS: WorkflowState.FETCHED
            },
            WorkflowState.FETCHED: {
                WorkflowTransition.CLASSIFY_CATEGORY: WorkflowState.CA<PERSON>GORY_CLASSIFIED
            },
            WorkflowState.CATEGORY_CLASSIFIED: {
                WorkflowTransition.ANALYZE_SENTIMENT: WorkflowState.SENTIMENT_ANALYZED
            },
            WorkflowState.SENTIMENT_ANALYZED: {
                WorkflowTransition.RETRIEVE_CONTEXT: WorkflowState.CONTEXT_RETRIEVED
            },
            WorkflowState.CONTEXT_RETRIEVED: {
                WorkflowTransition.GENERATE_RESPONSE: WorkflowState.RESPONSE_GENERATED
            },
            WorkflowState.RESPONSE_GENERATED: {
                WorkflowTransition.CREATE_DRAFT: WorkflowState.DRAFT_CREATED
            },
            WorkflowState.DRAFT_CREATED: {
                WorkflowTransition.COMPLETE: WorkflowState.COMPLETED
            }
        }

    async def initialize_workflow(self, user_id: str, email_ids: list[str]) -> str:
        """
        Create a new workflow instance and process emails concurrently.

        Args:
            user_id: ID of the user creating the workflow.
            email_ids: List of email IDs to process.

        Returns:
            str: ID of the created workflow.

        Raises:
            HTTPException: If workflow creation fails or emails are invalid.
        """
        try:
            # Validate emails exist and belong to the user
            email_object_ids = [ObjectId(email_id) for email_id in email_ids]
            emails = await self.db.emails.find({
                "_id": {"$in": email_object_ids},
                "user_id": user_id
            }).to_list(None)

            if not emails:
                raise HTTPException(
                    status_code=404,
                    detail="No emails found for the given IDs"
                )

            if len(emails) != len(email_ids):
                raise HTTPException(
                    status_code=400,
                    detail="One or more email IDs are invalid or do not belong to the user"
                )

            # Create workflow context
            workflow = {
                "user_id": user_id,
                "email_ids": email_object_ids,
                "current_state": WorkflowState.INITIATED,
                "results": {},  # Store results from each processing step
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }

            # Insert workflow record
            result = await self.db.workflows.insert_one(workflow)
            workflow_id = str(result.inserted_id)
            logger.info(f"Created workflow {workflow_id} for user {user_id}")

            return workflow_id

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create workflow")

    async def get_workflow(self, workflow_id: str) -> Optional[WorkflowContext]:
        """Retrieve workflow by ID"""
        try:
            workflow_data = await self.db.workflows.find_one({"_id": ObjectId(workflow_id)})
            if not workflow_data:
                return None
            return WorkflowContext(**workflow_data)
        except Exception as e:
            logger.error(f"Error retrieving workflow {workflow_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to retrieve workflow")

    async def transition_state(
        self, 
        workflow_id: str, 
        transition: WorkflowTransition,
        results: Dict[str, Any] = None
    ) -> WorkflowContext:
        """Handle workflow state transitions with category classification"""
        """Transition workflow to next state"""
        try:
            workflow = await self.get_workflow(workflow_id)
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow not found")

            current_state = workflow.current_state
            if current_state not in self.state_transitions:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid state transition from {current_state}"
                )

            allowed_transitions = self.state_transitions[current_state]
            if transition not in allowed_transitions:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid transition {transition} from state {current_state}"
                )

            new_state = allowed_transitions[transition]
            
            # Update workflow state and results
            update_data = {
                "current_state": new_state,
                "updated_at": datetime.utcnow()
            }
            
            if results:
                update_data[f"results.{new_state}"] = results

            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": update_data}
            )

            # Handle category classification transition
            if transition == WorkflowTransition.CLASSIFY_CATEGORY:
                try:
                    print("pochey")
                    from app.services.category_service import CategoryService
                    category_service = CategoryService(self.db)
                    
                    workflow = await self.get_workflow(workflow_id)
                    if not workflow:
                        raise HTTPException(status_code=404, detail="Workflow not found")
                    
                    # Get categories for the user
                    categories = await category_service.get_categories(workflow.user_id)
                    # print("categories",categories)
                    
                    # Get emails for classification
                    emails = await self.db.emails.find({"_id": {"$in": workflow.email_ids}}).to_list(None)
                    # print("emails", emails)
                    
                    classification_results = {}
                    for email in emails:
                        try:
                            category = await category_service.classify_email(email["body"], categories, workflow.user_id)
                            await category_service.update_email_category(email["_id"], category)
                            classification_results[str(email["_id"])] = category
                            print("classification_results",classification_results)
                        except Exception as e:
                            logger.error(f"Error classifying email {email['_id']}: {str(e)}")
                            classification_results[str(email["_id"])] = "error"
                    
                    # Update workflow with classification results
                    results = {"classification_results": classification_results}
                
                except Exception as e:
                    logger.error(f"Error in category classification: {str(e)}")
                    await self.handle_error(workflow_id, f"Category classification failed: {str(e)}")
                    raise
            
            return await self.get_workflow(workflow_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error transitioning workflow {workflow_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to transition workflow state")

    async def handle_error(
        self, 
        workflow_id: str, 
        error_message: str
    ) -> WorkflowContext:
        """Handle workflow error"""
        try:
            workflow = await self.get_workflow(workflow_id)
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow not found")

            if workflow.retry_count < workflow.max_retries:
                # Increment retry count
                await self.db.workflows.update_one(
                    {"_id": ObjectId(workflow_id)},
                    {
                        "$inc": {"retry_count": 1},
                        "$set": {
                            "error_message": error_message,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
            else:
                # Move to error state if max retries exceeded
                await self.db.workflows.update_one(
                    {"_id": ObjectId(workflow_id)},
                    {
                        "$set": {
                            "current_state": WorkflowState.ERROR,
                            "error_message": error_message,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )

            return await self.get_workflow(workflow_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error handling workflow error for {workflow_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to handle workflow error")
    async def process_workflow(self, workflow_id: str, user_id: str, email_ids: list[str]):
        """Process workflow emails based on configuration in a separate thread"""
        try:
            # Update workflow status to processing
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {"status": "processing", "updated_at": datetime.utcnow()}}
            )
            
            # Run the actual processing directly since it's already async
            await self._process_workflow_internal(
                workflow_id,
                user_id,
                email_ids
            )
            logger.info(f"Workflow {workflow_id} processing started in background thread")
        except Exception as e:
            logger.error(f"Error starting workflow processing: {str(e)}")
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {"status": "error", "error_message": str(e), "updated_at": datetime.utcnow()}}
            )
            raise HTTPException(status_code=500, detail="Failed to start workflow processing")

    async def _process_workflow_internal(self, workflow_id: str, user_id: str, email_ids: list[str]):
        """Internal method to process workflow emails"""
        try:
            # Create a semaphore to limit concurrent database operations
            sem = asyncio.Semaphore(5)  # Limit concurrent DB operations
            
            # Get workflow configuration with caching
            cache_key = f"workflow_config_{user_id}"
            Total_config = await self.db.configurations.find_one(
                {"user_id": user_id},
                projection={"email_responder.workflow": 1}
            )
            config = Total_config.get("email_responder", {})
            workflow_config = config.get("workflow", {"processing_type": "sequential", "processing_limit": 1})
            
            email_graph = EmailProcessingGraph(self.db)
            logger.info(f"Processing workflow {workflow_id} with email_ids: {email_ids}")
            logger.info(f"Processing type: {workflow_config['processing_type']}")

            final_results = {
                "processed_emails": [],
                "errors": []
            }
            
            # Create task queue with limited concurrency
            queue = asyncio.Queue(maxsize=workflow_config.get("processing_limit", 5))
            print("workflow_config", workflow_config)
            if workflow_config["processing_type"] == "concurrent":
                # Process emails concurrently with resource management
                concurrent_limit = workflow_config["processing_limit"]
                logger.info(f"Processing {len(email_ids)} emails concurrently with limit {concurrent_limit}")
                
                # Create worker tasks
                workers = []
                for _ in range(concurrent_limit):
                    workers.append(asyncio.create_task(self._worker(queue, email_graph, workflow_id, user_id, sem)))

                # Add emails to queue
                for email_id in email_ids:
                    await queue.put(email_id)
                
                # Add stop signals
                for _ in range(concurrent_limit):
                    await queue.put(None)
                
                # Wait for all workers to complete
                batch_results = await asyncio.gather(*workers, return_exceptions=True)
                
                # Handle results
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"Error in concurrent processing: {str(result)}")
                        final_results["errors"].append({"error": str(result)})
                    else:
                        final_results["processed_emails"].append(result)
            else:
                print("sequential")
                # Process emails sequentially
                logger.info(f"Processing {len(email_ids)} emails sequentially")
                for email_id in email_ids:
                    try:
                        result = await self._process_single_email(email_graph, workflow_id, user_id, email_id)
                        final_results["processed_emails"].append(result)
                    except Exception as e:
                        logger.error(f"Error processing email {email_id}: {str(e)}")
                        final_results["errors"].append({
                            "email_id": email_id,
                            "error": str(e)
                        })

            # Batch update workflow status with optimized write concern
            status = "completed" if not final_results["errors"] else "completed_with_errors"
            current_time = datetime.utcnow()
            
            # Use unordered bulk write for better performance
            bulk_ops = []
            bulk_ops.append(
                UpdateOne(
                    {"_id": ObjectId(workflow_id)},
                    {
                        "$set": {
                            "status": status,
                            "results": final_results,
                            "updated_at": current_time,
                            "completed_at": current_time
                        }
                    }
                )
            )
            
            # Execute bulk operations with write concern
            await self.db.workflows.bulk_write(
                bulk_ops,
                ordered=False,
                write_concern={"w": 1, "j": False}  # Disable journaling for speed
            )
            
            # Release locks and yield control more frequently
            await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"Error in workflow processing: {str(e)}")
            await self.handle_error(workflow_id, f"Processing error: {str(e)}")

            # Update workflow status
            status = "completed" if not final_results["errors"] else "completed_with_errors"
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {
                    "$set": {
                        "status": status,
                        "results": final_results,
                        "updated_at": datetime.utcnow(),
                        "completed_at": datetime.utcnow()
                    }
                }
            )

    async def _worker(self, queue: asyncio.Queue, email_graph: EmailProcessingGraph, workflow_id: str, user_id: str, sem: asyncio.Semaphore):
        """Worker to process emails from queue"""
        while True:
            email_id = await queue.get()
            if email_id is None:  # Stop signal
                queue.task_done()
                break
                
            try:
                async with sem:  # Limit concurrent DB operations
                    result = await self._process_single_email(email_graph, workflow_id, user_id, email_id)
                queue.task_done()
                return result
            except Exception as e:
                logger.error(f"Error in worker processing email {email_id}: {str(e)}")
                queue.task_done()
                return {"email_id": email_id, "error": str(e)}

    async def _process_single_email(self, email_graph: EmailProcessingGraph, workflow_id: str, user_id: str, email_id: str) -> dict:
        """Process a single email in the workflow"""
        logger.info(f"Processing email {email_id} in workflow {workflow_id}")
        
        try:
            # Clear any previous state
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$unset": {"current_email_id": ""}}
            )

            # Set current email being processed
            initial_state = {
                "email_id": str(email_id),
                "user_id": user_id,
                "workflow_id": workflow_id,
                "current_email_id": str(email_id),
                "email": {},
                "classification": "",
                "sentiment": {},
                "context": {},
                "response": "",
                "draft": {},
                "error": None
            }

            result = await email_graph.process_email(initial_state)
            
            if result.get("error"):
                logger.error(f"Error processing email {email_id}: {result['error']}")
                raise Exception(result["error"])

            return {
                "email_id": str(email_id),
                "processing_id": result.get("processing_id"),
                "status": "completed",
                "results": result
            }

        except Exception as e:
            logger.error(f"Error processing email {email_id}: {str(e)}")
            raise

        except Exception as e:
            logger.error(f"Error in workflow processing: {str(e)}")
            await self.handle_error(workflow_id, f"Processing error: {str(e)}")
