#!/bin/bash

# Two-Step Password Reset API Examples
# Make sure your server is running on http://localhost:8000

echo "=== Two-Step Password Reset API Examples ==="
echo ""

# Step 1: Request password reset (generates OTP)
echo "Step 1: Requesting password reset..."
curl -X POST "http://localhost:8000/users/forget-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
echo -e "\n"

# Wait for user to check email
echo "Check your email for the 6-digit OTP, then continue..."
echo "Press Enter when you have the OTP..."
read

# Step 2: Validate OTP
echo "Step 2: Validating OTP..."
echo "Enter the 6-digit OTP from your email:"
read otp

curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"<EMAIL>\",
    \"type\": \"otp\",
    \"otp\": \"$otp\"
  }"
echo -e "\n"

# Step 3: Update password
echo "Step 3: Updating password..."
echo "Enter your new password:"
read -s new_password

curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"<EMAIL>\",
    \"type\": \"validation\",
    \"new_password\": \"$new_password\"
  }"
echo -e "\n"

echo "Password reset process completed!"

# Example error scenarios
echo ""
echo "=== Error Scenario Examples ==="

# Invalid OTP
echo "Testing invalid OTP..."
curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "otp",
    "otp": "999999"
  }'
echo -e "\n"

# Invalid request type
echo "Testing invalid request type..."
curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "invalid",
    "otp": "123456"
  }'
echo -e "\n"

# Missing OTP for otp type
echo "Testing missing OTP for otp type..."
curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "otp"
  }'
echo -e "\n"

# Missing password for validation type
echo "Testing missing password for validation type..."
curl -X POST "http://localhost:8000/users/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "validation"
  }'
echo -e "\n"
