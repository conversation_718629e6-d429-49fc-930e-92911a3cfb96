import logging
import asyncio
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.services.notification_service import NotificationService
from app.config.database import get_db

logger = logging.getLogger(__name__)

async def start_subscription_monitor():
    """Start the subscription monitoring service on application startup"""
    try:
        logger.info("Initializing subscription monitoring service")
        db = await get_db()  # Await the database instance
        notification_service = NotificationService(db)
        
        # Start the monitoring task with the database instance
        notification_service.monitor_task = asyncio.create_task(
            notification_service.monitor_subscriptions(db)
        )
        logger.info("Subscription monitoring service started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start subscription monitoring: {str(e)}")
        # Don't raise the exception - we want the application to start even if monitoring fails
