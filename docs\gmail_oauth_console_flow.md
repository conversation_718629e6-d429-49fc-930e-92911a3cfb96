graph LR
    A[Start] --> B(Google Cloud Console);
    B --> C{Create New Project?};
    C -- Yes --> D[Enter Project Name & Create];
    C -- No --> E[Select Existing Project];
    E --> F(Go to APIs & Services);
    F --> G[Credentials];
    G --> H{OAuth 2.0 Client IDs?};
    H -- No --> I[Create Credentials -> OAuth client ID];
    I --> J[Application Type: Web application];
    J --> K[Name your client ID];
    K --> L[Add authorized JavaScript origins ];
    L --> M[Add authorized redirect URIs];
    M --> N[Create];
    N --> O(OAuth 2.0 Client ID Created);
    H -- Yes --> O;
    O --> P(Go to APIs & Services -> Library);
    P --> Q[Search for Gmail API];
    Q --> R{Gmail API Enabled?};
    R -- No --> S[Enable Gmail API];
    R -- Yes --> T[Configure Consent Screen];
    T --> U[Select User Type ];
    U --> V[Fill in App details ];
    V --> W[Add Scopes];
    W --> X[Select Gmail API Scopes ];
    X --> Y[Save];
    Y --> Z(Consent Screen Configured);
    Z --> AA(Go to Pub/Sub);
    AA --> BB{Topic Exists?};
    BB -- No --> CC[Create Topic];
    BB -- Yes --> DD[Select Existing Topic];
    DD --> EE(Topic Ready);
    CC --> EE;
    EE --> FF{Subscription Exists?};
    FF -- No --> GG[Create Subscription];
    GG --> HH[Select Topic, Subscription ID, Delivery Type ];
    HH --> II[Configure Push Endpoint ];
    II --> JJ[Create];
    JJ --> KK(Subscription Created);
    FF -- Yes --> KK;
    KK --> LL(Go to IAM & Admin -> Service accounts);
    LL --> MM{Service Account Exists?};
    MM -- No --> NN[Create Service Account];
    NN --> OO[Enter Service Account Details ];
    OO --> PP[Grant this service account access to project? ];
    PP -- Yes --> QQ[Assign Roles ];
    PP -- No --> RR[Click Done];
    RR --> SS[Create Key];
    SS --> TT[Select Key Type ];
    TT --> UU[Click Create];
    UU --> VV(Service Account Key File Downloaded);
    MM -- Yes --> VV;
    VV --> WW[Configure Application with OAuth Client ID & Secret];
    WW --> XX[Implement OAuth 2.0 Flow in Application];
    XX --> YY[Implement Pub/Sub Subscription Handling in Application];
    YY --> ZZ[Use Service Account Key for Server-to-Server Interactions ];
    ZZ --> AAA[End];