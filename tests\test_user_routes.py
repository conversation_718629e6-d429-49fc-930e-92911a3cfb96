import pytest
from fastapi.testclient import TestClient
from motor.motor_asyncio import AsyncIOMotorDatabase
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from fastapi import HTTPException
from app.utils.security import create_access_token
from app.main import app
from bson import ObjectId

# Mock data
MOCK_USER_ID = str(ObjectId())
MOCK_TOKEN = f"Bearer {create_access_token({'sub': MOCK_USER_ID})}"
MOCK_USER = {
    "_id": ObjectId(MOCK_USER_ID),
    "username": "testuser",
    "full_name": "Test User",
    "email_config": {
        "provider": "gmail",
        "gmail_config": {
            "email": "<EMAIL>"
        }
    }
}

@pytest.fixture
def mock_db():
    db = Mock(spec=AsyncIOMotorDatabase)
    # Mock the users collection
    db.users = Mock()
    db.users.find_one = AsyncMock()
    db.users.update_one = AsyncMock()
    return db

@pytest.fixture
def mock_user_service():
    with patch('app.controllers.user_controller.UserService.update_user') as mock:
        # Configure the mock to be used both directly and with side_effect
        async def mock_coro(*args, **kwargs):
            # Return a properly structured response by default
            return {
                "_id": MOCK_USER_ID,
                "username": "testuser",
                "full_name": "Test User",
                "email_config": {
                    "provider": "gmail",
                    "gmail_config": {
                        "email": "<EMAIL>"
                    }
                }
            }
        mock.side_effect = mock_coro
        yield mock

def serialize_object_id(obj):
    """Helper function to convert ObjectId to string in nested dictionaries"""
    if isinstance(obj, dict):
        for key, value in obj.items():
            if isinstance(value, ObjectId):
                obj[key] = str(value)
            elif isinstance(value, dict):
                serialize_object_id(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        serialize_object_id(item)
    return obj

@pytest.fixture
def test_app():
    return TestClient(app)

def test_update_user_with_imap_config(test_app, mock_db, mock_user_service):
    
    # Test data
    update_data = {
        "email_config": {
            "provider": "imap",
            "imap_config": {
                "email": "<EMAIL>",
                "password": "testpass123",
                "imap_server": "imap.example.com",
                "imap_port": 993,
                "smtp_server": "smtp.example.com",
                "smtp_port": 587,
                "use_ssl": True
            }
        }
    }

    # Mock the database response
    mock_updated_user = MOCK_USER.copy()
    # Ensure all ObjectId instances are converted to strings
    mock_updated_user = serialize_object_id(mock_updated_user)
    mock_updated_user["email_config"] = update_data["email_config"].copy()
    mock_updated_user["email_config"]["imap_config"] = update_data["email_config"]["imap_config"].copy()
    mock_updated_user["email_config"]["imap_config"].pop("password")  # Password should be removed from response
    
    # Override the default mock response with our custom one
    async def custom_mock_response(*args, **kwargs):
        return mock_updated_user
    
    mock_user_service.side_effect = custom_mock_response

    # Mock the database calls
    mock_db.users.find_one.return_value = MOCK_USER
    mock_db.users.update_one.return_value = MagicMock(modified_count=1)

    # Make request
    response = test_app.put(
        "/users/update",
        json=update_data,
        headers={"Authorization": MOCK_TOKEN}
    )

    # Assertions
    assert response.status_code == 200
    response_json = response.json()
    assert "email_config" in response_json, f"Response missing email_config: {response_json}"
    assert response_json["email_config"]["provider"] == "imap"
    assert "imap_config" in response_json["email_config"], f"Response missing imap_config: {response_json['email_config']}"
    assert "password" not in response_json["email_config"]["imap_config"]
    assert response_json["email_config"]["imap_config"]["email"] == "<EMAIL>"

# def test_update_user_invalid_token(test_app, mock_db):
#     response = test_app.put(
#         "/users/update",
#         json={"username": "newname"},
#         headers={"Authorization": "Invalid token"}
#     )
    
#     assert response.status_code == 401
#     assert "Invalid authorization header" in response.json()["detail"]

def test_update_user_no_changes(test_app, mock_db, mock_user_service):
    # Mock service to raise HTTPException with 304 status code
    async def no_changes_response(*args, **kwargs):
        raise HTTPException(status_code=304, detail="No changes were made")
    
    mock_user_service.side_effect = no_changes_response
    
    # Make request
    response = test_app.put(
        "/users/update",
        json={"username": "same_username"},
        headers={"Authorization": MOCK_TOKEN}
    )
    
    # For 304 responses, FastAPI doesn't automatically include a response body
    # So we just check the status code
    assert response.status_code == 304

def test_update_user_not_found(test_app, mock_db, mock_user_service):
    # Mock service to return 404
    async def not_found_response(*args, **kwargs):
        raise HTTPException(status_code=404, detail="User not found")
    
    mock_user_service.side_effect = not_found_response
    
    response = test_app.put(
        "/users/update",
        json={"username": "newname"},
        headers={"Authorization": MOCK_TOKEN}
    )
    
    assert response.status_code == 404
    assert "User not found" in response.json()["detail"]
