import logging
from fastapi import HTTPException
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
import requests
from bson import ObjectId
import os
from datetime import datetime, timezone
from app.utils.security import fernet
import traceback
import base64
import email.utils

logger = logging.getLogger(__name__)

class GmailService:
    def __init__(self):
        self.client_id = os.getenv("GMAIL_CLIENT_ID")
        self.client_secret = os.getenv("GMAIL_CLIENT_SECRET")
        self.redirect_uri = os.getenv("GMAIL_REDIRECT_URI", "http://localhost:8000/api/auth/gmail/callback")
        self.scopes = [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/gmail.modify'
        ]

    async def get_auth_url(self, state: str = None):
        """Generate Gmail OAuth authorization URL"""
        try:
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "redirect_uris": [self.redirect_uri],
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token"
                    }
                },
                scopes=self.scopes
            )
            flow.redirect_uri = self.redirect_uri

            # Always force prompt=consent to ensure we get a refresh token
            authorization_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                state=state,
                prompt='consent'
            )
            
            logger.info(f"Generated auth URL with prompt=consent to ensure refresh token")
            return authorization_url
        except Exception as e:
            logger.error(f"Error generating auth URL: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate auth URL")

    async def handle_auth_callback(self, code: str, authorization: str = None, state: str = None, db=None, user_id=None):
        """Handle OAuth callback and store tokens"""
        try:
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "redirect_uris": [self.redirect_uri],
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token"
                    }
                },
                scopes=self.scopes,
                state=state
            )
            flow.redirect_uri = self.redirect_uri
            
            # Exchange code for tokens
            flow.fetch_token(code=code)
            credentials = flow.credentials

            # Get user email
            service = build('gmail', 'v1', credentials=credentials)
            profile = service.users().getProfile(userId='me').execute()
            user_email = profile['emailAddress']

            # Prepare token data
            token_data = {
                "email_config.provider": "gmail",
                "email_config.gmail_config": {
                    "access_token": fernet.encrypt(credentials.token.encode()).decode(),
                    "refresh_token": fernet.encrypt(credentials.refresh_token.encode()).decode() if credentials.refresh_token else None,
                    "token_uri": credentials.token_uri,
                    "client_id": credentials.client_id,
                    "client_secret": credentials.client_secret,
                    "scopes": credentials.scopes,
                    "expiry": credentials.expiry.isoformat() if credentials.expiry else None,
                    "email": user_email,
                    "token_status": "active"  # Mark as active when newly created
                }
            }

            # Log the token information
            logger.info(f"New Gmail authentication successful for {user_email}")
            logger.info(f"Refresh token obtained: {credentials.refresh_token is not None}")
            logger.info(f"Access token length: {len(credentials.token)}")
            if credentials.refresh_token:
                logger.info(f"Refresh token length: {len(credentials.refresh_token)}")
            
            # Setup email subscription if db and user_id are provided
            # subscription_data = None
            # if db and user_id:
            #     try:
            #         from app.services.gmail_notification_service import GmailNotificationService
            #         notification_service = GmailNotificationService(db)
            #         subscription_data = await notification_service.create_subscription(user_id)
            #         logger.info(f"Successfully setup Gmail subscription for user {user_id}")
            #     except Exception as e:
            #         logger.error(f"Failed to setup Gmail subscription: {str(e)}")

            # Calculate token expiry timestamp
            token_expiry = int(datetime.now(timezone.utc).timestamp() + credentials.expiry.timestamp() - datetime.now(timezone.utc).timestamp())
            
            # Call credentials endpoint with tokens
            credentials_url = os.getenv("CREDENTIALS_ENDPOINT", "http://localhost:8000/credentials")
            credentials_payload = {
                "credentials": [
                    {"key": "google_access_token", "value": credentials.token},
                    {"key": "google_refresh_token", "value": credentials.refresh_token},
                    {"key": "google_client_id", "value": credentials.client_id},
                    {"key": "google_client_secret", "value": credentials.client_secret},
                    {"key": "google_token_expires", "value": str(token_expiry)}
                ]
            }
            
            try:
                headers = {"Authorization": authorization} if authorization else {}
                response = requests.post(credentials_url, json=credentials_payload, headers=headers)
                response.raise_for_status()
            except Exception as e:
                logger.error(f"Failed to send credentials: {str(e)}")
            
            return {
                "status_code": 200,
                "status": "success",
                "message": "Gmail authentication successful",
                "data": {
                    **token_data,
                    # "subscription": subscription_data
                }
            }

        except Exception as e:
            logger.error(f"Error handling auth callback: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to complete authentication")

    async def refresh_token(self, db, user_id: str):
        """Refresh expired access token"""
        try:
            config = await db.users.find_one({"_id": ObjectId(user_id)})
            if not config or "gmail_config" not in config["email_config"]:
                logger.error(f"Gmail configuration not found for user {user_id}")
                raise HTTPException(status_code=404, detail="Gmail configuration not found")

            gmail_config = config["email_config"]["gmail_config"]
            
            if not gmail_config.get("refresh_token"):
                logger.error("No refresh token available")
                # Mark token as expired in database
                await db.users.update_one(
                    {"_id": ObjectId(user_id)},
                    {"$set": {"email_config.gmail_config.token_status": "expired"}}
                )
                raise HTTPException(status_code=401, detail="No refresh token available. Please re-authenticate with Gmail.")
            
            try:
                # Create credentials object
                try:
                    decrypted_refresh_token = fernet.decrypt(gmail_config["refresh_token"].encode()).decode()
                    decrypted_access_token = fernet.decrypt(gmail_config["access_token"].encode()).decode()
                except Exception as decrypt_error:
                    logger.error(f"Error decrypting tokens: {str(decrypt_error)}")
                    # Mark token as expired
                    await db.users.update_one(
                        {"_id": ObjectId(user_id)},
                        {"$set": {"email_config.gmail_config.token_status": "expired"}}
                    )
                    raise HTTPException(
                        status_code=401, 
                        detail="Error with stored credentials. Please re-authenticate with Gmail."
                    )
                
                # Log token information for debugging
                logger.debug(f"Access token length: {len(decrypted_access_token)}")
                logger.debug(f"Refresh token length: {len(decrypted_refresh_token)}")
                print("refresh token length:", decrypted_refresh_token)
                logger.info(f"Using token_uri: {gmail_config.get('token_uri', 'https://oauth2.googleapis.com/token')}")
                logger.info(f"Using client_id: {gmail_config.get('client_id', self.client_id)[:10]}...")
                
                try:
                    creds = Credentials(
                        token=decrypted_access_token,
                        refresh_token=decrypted_refresh_token,
                        token_uri=gmail_config.get("token_uri", "https://oauth2.googleapis.com/token"),
                        client_id=gmail_config.get("client_id", self.client_id),
                        client_secret=gmail_config.get("client_secret", self.client_secret),
                        scopes=gmail_config.get("scopes", self.scopes)
                    )
                except Exception as cred_obj_error:
                    logger.error(f"Error creating credentials object: {str(cred_obj_error)}")
                    raise HTTPException(status_code=500, detail=f"Failed to create credentials object: {str(cred_obj_error)}")
            except HTTPException as he:
                # Re-raise HTTP exceptions
                raise he
            except Exception as cred_error:
                logger.error(f"Error creating credentials: {str(cred_error)}")
                raise HTTPException(status_code=500, detail=f"Failed to create credentials: {str(cred_error)}")

            # Refresh token
            try:
                logger.info("Attempting to refresh token")
                print("***************Attempting to refresh token***************")
                creds.refresh(Request())
                print("***************Token refreshed successfully***************")
                logger.info("Token refreshed successfully")
            except Exception as refresh_error:
                error_message = str(refresh_error)
                logger.error(f"Token refresh failed: {error_message}")
                
                # Mark token as expired in database
                await db.users.update_one(
                    {"_id": ObjectId(user_id)},
                    {"$set": {"email_config.gmail_config.token_status": "expired"}}
                )
                
                if "invalid_grant" in error_message:
                    # This is a clear indication that the refresh token is expired or revoked
                    raise HTTPException(
                        status_code=401, 
                        detail="Your Gmail authorization has expired. Please re-authenticate with Gmail."
                    )
                else:
                    raise HTTPException(
                        status_code=401, 
                        detail=f"Token refresh failed. Please re-authenticate with Gmail: {error_message}"
                    )
            
            # Update token data
            token_data = {
                "access_token": fernet.encrypt(creds.token.encode()).decode(),
                "refresh_token": fernet.encrypt(creds.refresh_token.encode()).decode() if creds.refresh_token else None,
                "expiry": creds.expiry.isoformat() if creds.expiry else None
            }

            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {
                    "email_config.gmail_config.access_token": token_data["access_token"],
                    "email_config.gmail_config.refresh_token": token_data["refresh_token"],
                    "email_config.gmail_config.expiry": token_data["expiry"]
                }}
            )

            return fernet.decrypt(token_data["access_token"].encode()).decode()

        except HTTPException as he:
            raise he
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to refresh token: {str(e)}")

    async def get_valid_token(self, db, user_id: str):
        """Get a valid access token, refreshing if necessary"""
        try:
            config = await db.users.find_one({"_id": ObjectId(user_id)})
            if not config:
                raise HTTPException(status_code=404, detail="User configuration not found")

            gmail_config = config.get("email_config", {}).get("gmail_config", {})
            if not gmail_config:
                raise HTTPException(status_code=404, detail="Gmail configuration not found")
            
            # Always try to refresh the token to ensure it's valid
            try:
                return await self.refresh_token(db, user_id)
            except Exception as refresh_error:
                logger.warning(f"Token refresh failed, trying to use existing token: {str(refresh_error)}")
                # If refresh fails, try to use the existing token as a fallback
                if gmail_config.get("access_token"):
                    return fernet.decrypt(gmail_config["access_token"].encode()).decode()
                else:
                    raise HTTPException(status_code=401, detail="No valid token available and refresh failed")

        except HTTPException as he:
            raise he
        except Exception as e:
            logger.error(f"Error getting valid token: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to get valid token")
            
    async def fetch_recent_emails(self, db, user_id: str, max_results: int = 3):
        """
        Fetch recent emails from Gmail and store them in the database
        
        Args:
            db: Database connection
            user_id: User ID
            max_results: Maximum number of emails to fetch
            
        Returns:
            List of fetched email IDs
        """
        try:
            print("***************Fetching recent emails for user:", user_id)
            logger.info(f"Fetching recent emails for user {user_id}")
            
            # Get valid access token
            access_token = await self.get_valid_token(db, user_id)
            
            # Build Gmail service
            creds = Credentials(token=access_token)
            service = build('gmail', 'v1', credentials=creds)
            
            # Get list of recent messages
            results = service.users().messages().list(
                userId='me',
                labelIds=['INBOX'],
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            logger.info(f"Found {len(messages)} recent messages")
            
            if not messages:
                logger.info("No new messages found")
                return []
            
            # Get user's email address
            user = await db.users.find_one({"_id": ObjectId(user_id)})
            user_email = user.get("email_config", {}).get("gmail_config", {}).get("email")
            
            # Process each message
            processed_emails = []
            for message in messages:
                message_id = message['id']
                
                # Check if this email is already in the database
                existing_email = await db.emails.find_one({
                    "user_id": user_id,
                    "message_id": message_id
                })
                
                if existing_email:
                    print("Email already exists in database")
                    logger.debug(f"Email {message_id} already exists in database")
                    continue
                
                # Get full message details
                msg = service.users().messages().get(
                    userId='me',
                    id=message_id,
                    format='full'
                ).execute()
                
                # Extract headers first
                headers = {h['name'].lower(): h['value'] for h in msg['payload'].get('headers', [])}
                
                # Extract email body parts
                def get_email_body(payload, body_parts=None):
                    if body_parts is None:
                        body_parts = {'text': '', 'html': ''}
                    
                    if 'parts' in payload:
                        for part in payload['parts']:
                            get_email_body(part, body_parts)
                    else:
                        mime_type = payload.get('mimeType', '').lower()
                        body = payload.get('body', {})
                        data = body.get('data', '')
                        
                        if data and mime_type == 'text/plain':
                            body_parts['text'] += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                        elif data and mime_type == 'text/html':
                            body_parts['html'] += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                    
                    return body_parts
                
                # Get email body content
                body_content = get_email_body(msg['payload'])
                
                # Use attachment_service to process email attachments
                from app.services.attachment_service import attachment_service
                try:
                    email_data, attachments = await attachment_service.process_email_attachments(
                        email_message=msg,
                        service=service,
                        message_id=message_id,
                        provider='gmail'
                    )
                    
                    # Merge the body content with attachment service results
                    email_doc = {
                        'user_id': user_id,
                        'message_id': msg['id'],
                        'thread_id': msg.get('threadId'),
                        'subject': headers.get('subject', '(No subject)'),
                        'sender': headers.get('from', ''),
                        'recipient': headers.get('to', ''),
                        'cc': headers.get('cc', ''),
                        'bcc': headers.get('bcc', ''),
                        'date': email.utils.parsedate_to_datetime(headers.get('date', '')).isoformat() if headers.get('date', '') else datetime.utcnow().isoformat(),
                        'body': body_content.get('text') or email_data.get('text', ''),
                        'html_body': body_content.get('html') or email_data.get('html', ''),
                        'attachments': [{
                            'filename': att['filename'],
                            's3_key': att['s3_key'],
                            'content_type': att['mime_type'],
                            'size': att['size'],
                            'is_inline': att['is_inline'],
                            'content_id': att.get('content_id')
                        } for att in attachments],
                        'inline_images': {
                            cid: {
                                's3_key': img['s3_key'],
                                'content_type': img['content_type'],
                                'size': img['size'],
                                'url': img['url']
                            } for cid, img in email_data.get('inline_images', {}).items()
                        },
                        'processed': False,
                        'created_at': datetime.utcnow()
                    }
                    print("***************Storing email in database***************")
                    print("Email document:", email_doc, "\n")

                    # Store email in database
                    from app.services.email_service import EmailService
                    stored_email = await EmailService.store_new_email(db, user_id, email_doc)

                    if stored_email:
                        processed_emails.append(message_id)
                        # Notify connected clients about new email via Socket.IO
                        from app.websocket.connection_manager import manager
                        serialized_email = EmailService.serialize_mongo_document(stored_email)
                        await manager.send_to_user(
                            user_id,
                            'new_email',
                            serialized_email
                        )
                        logger.info(f"Email {message_id} stored and notification sent")
                except Exception as e:
                    logger.error(f"Error processing email attachments: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Store the email without attachments if processing fails
                    try:
                        from app.services.email_service import EmailService
                        await EmailService.store_new_email(db, user_id, msg)
                    except Exception as store_error:
                        logger.error(f"Failed to store email without attachments: {str(store_error)}")
            
            logger.info(f"Processed {len(processed_emails)} new emails")
            return processed_emails
            
        except Exception as e:
            logger.error(f"Error fetching recent emails: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch recent emails: {str(e)}")
