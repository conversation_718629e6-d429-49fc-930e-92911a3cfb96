from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime
from typing import Any, Dict, Optional

class CategoryCreate(BaseModel):
    name: str
    description: str
    sample_email: str
    email_id: str

class EmailMessage(BaseModel):
    message_id: str
    subject: str
    sender: str
    recipient: str
    date: datetime
    updated_date: Optional[datetime] = None
    body: str
    is_read: bool
    folder: str = "INBOX"
    status: str = "unprocessed"  # possible values: unprocessed, processed
    fetched_date: Optional[datetime] = None
    user_id: Optional[str] = None
    
    # Allow additional fields
    class Config:
        extra = "allow"

class EmailData(BaseModel):
    emails: List[EmailMessage]
    total: int
    page: int
    page_size: int
    has_more: bool
    refreshed: bool
    latest_fetched_date: Optional[str]

class EmailReply(BaseModel):
    email_id: Optional[str] = None
    body: str
    is_draft: bool = False
    user_id:str
    subject: Optional[str] = None
    to: Optional[str] = None
    in_reply_to: Optional[str] = None
    references: Optional[str] = None

class EmailAddressCreate(BaseModel):
    emails: List[EmailStr]

class EmailAddressUpdate(BaseModel):
    emails: List[EmailStr]

class EmailAddressResponse(BaseModel):
    status_code: int
    status: str
    message: str
    data: List[str]

class EmailResponse(BaseModel):
    status_code: int
    status: str
    message: str
    data: EmailData
