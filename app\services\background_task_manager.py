import asyncio
import logging
from typing import Dict
from app.services.imap_idle_service import ImapIdleService
from bson import ObjectId

logger = logging.getLogger(__name__)

class BackgroundTaskManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BackgroundTaskManager, cls).__new__(cls)
            cls._instance.imap_service = ImapIdleService()
            cls._instance.active_users: Dict[str, dict] = {}
        return cls._instance
    
    async def start_email_monitoring(self, user_id: str, email_config: dict, db=None):
        """Start email monitoring for a user"""
        try:
            # Check if user is active for monitoring
            if db is not None:
                user = await db.users.find_one({"_id": ObjectId(user_id)})
                if user is not None and "monitoring_active" in user and user["monitoring_active"] is False:
                    logger.info(f"Skipping monitoring for inactive user {user_id}")
                    return
            
            if user_id in self.active_users:
                await self.stop_email_monitoring(user_id)
            
            # Store user config
            self.active_users[user_id] = email_config
            
            # Normalize email to lowercase before starting connection
            normalized_config = email_config.copy()
            if "email" in normalized_config:
                normalized_config["email"] = normalized_config["email"].lower()
            
            # Start IMAP IDLE connection
            await self.imap_service.start_idle_connection(user_id, normalized_config, db)
            
            logger.info(f"Started email monitoring for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error starting email monitoring: {str(e)}")
            raise
    
    async def stop_email_monitoring(self, user_id: str):
        """Stop email monitoring for a user"""
        try:
            if user_id in self.active_users:
                await self.imap_service.stop_idle_connection(user_id)
                del self.active_users[user_id]
                logger.info(f"Stopped email monitoring for user {user_id}")
            else:
                logger.info(f"User {user_id} not in active_users, nothing to stop")
                
        except Exception as e:
            logger.error(f"Error stopping email monitoring: {str(e)}")
            raise
    
    async def restart_email_monitoring(self, user_id: str, db=None):
        """Restart email monitoring for a user"""
        try:
            if user_id in self.active_users:
                email_config = self.active_users[user_id]
                await self.stop_email_monitoring(user_id)
                await self.start_email_monitoring(user_id, email_config, db)
                logger.info(f"Restarted email monitoring for user {user_id}")
            else:
                # If user is not in active_users, try to get their config from the database
                if db is not None:
                    user = await db.users.find_one({"_id": ObjectId(user_id)})
                    if user and "email_config" in user and "imap_config" in user["email_config"]:
                        email_config = user["email_config"]["imap_config"]
                        await self.start_email_monitoring(user_id, email_config, db)
                        logger.info(f"Started email monitoring for previously inactive user {user_id}")
                
        except Exception as e:
            logger.error(f"Error restarting email monitoring: {str(e)}")
            raise
    
    async def cleanup(self):
        """Clean up all connections on shutdown"""
        for user_id in list(self.active_users.keys()):
            await self.stop_email_monitoring(user_id)
