import logging
from fastapi import HTTPException
from app.services.user_service import UserService
from app.schemas.user_schema import Use<PERSON><PERSON><PERSON>, User, UserLogin, ForgetPasswordRequest, ResetPasswordRequest
from app.utils.security import decode_jwt_token

logger = logging.getLogger(__name__)

class UserController:
    @staticmethod
    async def create_user(user: UserCreate, db):
        try:
            result = await UserService.create_user(user, db)
            if not result:
                raise HTTPException(status_code=500, detail="Failed to create user")
            return result
        except HTTPException as he:
            logger.warning(f"Validation error during user creation: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error during user creation: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
            
    @staticmethod
    async def login_user(user_credentials: UserLogin, db):
        try:
            return await UserService.login_user(user_credentials, db)
        except HTTPException as he:
            logger.warning(f"Login failed: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    # @staticmethod
    # async def get_user_details(token: str, db):
    #     try:
    #         user_id = decode_jwt_token(token)
    #         user = await UserService.get_user_details(user_id, db)
    #         return {
    #             "status_code": 200,
    #             "status": "OK",
    #             "message": "User details fetched successfully",
    #             "data": user
    #         }
    #     except HTTPException as he:
    #         logger.warning(f"Error retrieving user details: {str(he.detail)}")
    #         raise he
    #     except Exception as e:
    #         logger.error(f"Unexpected error retrieving user details: {str(e)}")
    #         raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def update_user(update_data: dict, user_id: str, db):
        """Update user details and email configuration"""
        try:
            result = await UserService.update_user(update_data, user_id, db)
            return result
        except HTTPException as he:
            logger.warning(f"Error updating user: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error updating user: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

    @staticmethod
    async def get_user(token: str, db):
        try:
            user_id = decode_jwt_token(token)
            user = await UserService.get_user(user_id, db)
            return {
                "status_code": 200,
                "status": "OK",
                "message": "User details fetched successfully",
                "data": user
            }
        except HTTPException as he:
            logger.warning(f"Error retrieving user {user_id}: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error retrieving user {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def forget_password(email: str, db):
        """
        Handle forget password request

        Args:
            email: Email address to send reset token to
            db: Database connection

        Returns:
            Response with status message

        Raises:
            HTTPException: If request processing fails
        """
        try:
            result = await UserService.forget_password(email, db)
            return result
        except HTTPException as he:
            logger.warning(f"Forget password failed for email {email}: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error during forget password for email {email}: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @staticmethod
    async def reset_password(email: str, request_type: str, otp: str = None, new_password: str = None, db=None):
        """
        Handle password reset request with two-step process

        Args:
            email: Email address of the user
            request_type: Type of request - "otp" for OTP validation, "validation" for password update
            otp: 6-digit OTP from forget password request (required for type="otp")
            new_password: New password to set (required for type="validation")
            db: Database connection

        Returns:
            Response with status message

        Raises:
            HTTPException: If OTP is invalid, expired, or request processing fails
        """
        try:
            result = await UserService.reset_password(email, request_type, otp, new_password, db)
            return result
        except HTTPException as he:
            logger.warning(f"Password reset failed for email {email}, type {request_type}: {str(he.detail)}")
            raise he
        except Exception as e:
            logger.error(f"Unexpected error during password reset for email {email}, type {request_type}: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")
