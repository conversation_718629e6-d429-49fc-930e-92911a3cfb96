import pytest
from fastapi import HTTPException
from app.services.category_service import CategoryService
from datetime import datetime
from unittest.mock import Mock, patch

@pytest.fixture
def mock_db():
    return Mock()

@pytest.fixture
def category_service(mock_db):
    return CategoryService(mock_db)

@pytest.mark.asyncio
async def test_get_categories(category_service, mock_db):
    # Setup
    user_id = "test_user"
    mock_config = {
        "email_responder": {
            "categories": [
                {"name": "test_category", "description": "test description"}
            ]
        }
    }
    mock_db.configurations.find_one.return_value = mock_config
    
    # Execute
    categories = await category_service.get_categories(user_id)
    
    # Assert
    assert len(categories) == 1
    assert categories[0]["name"] == "test_category"
    mock_db.configurations.find_one.assert_called_once_with({"user_id": user_id})

@pytest.mark.asyncio
async def test_classify_email(category_service):
    # Setup
    email_content = "This is a test email about support"
    categories = [
        {
            "name": "support",
            "description": "Support related emails",
            "sample_email": "about support"
        }
    ]
    
    # Execute
    category = await category_service.classify_email(email_content, categories)
    
    # Assert
    assert category == "support"

@pytest.mark.asyncio
async def test_update_email_category(category_service, mock_db):
    # Setup
    email_id = "test_email_id"
    category = "test_category"
    mock_db.emails.update_one.return_value.modified_count = 1
    
    # Execute
    result = await category_service.update_email_category(email_id, category)
    
    # Assert
    assert result is True
    mock_db.emails.update_one.assert_called_once()
