import logging
import base64
import json
import traceback
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from msal import ConfidentialClientApplication
import urllib3
from urllib3.exceptions import HTTPError
import time
from requests.exceptions import RequestException
import os
from datetime import datetime
import requests
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from app.utils.security import fernet
from app.services.attachment_service import attachment_service

logger = logging.getLogger(__name__)

class OutlookService:
    def __init__(self):
        self.client_id = os.getenv("OUTLOOK_CLIENT_ID")
        self.client_secret = os.getenv("OUTLOOK_CLIENT_SECRET") 
        self.tenant_id = os.getenv("OUTLOOK_TENANT_ID")
        self.redirect_uri = os.getenv("OUTLOOK_REDIRECT_URI", "http://localhost:8000/auth/callback")
        self.authority = "https://login.microsoftonline.com/common"
        self.scopes = ["Mail.Read", "Mail.ReadWrite", "Mail.Send", "User.Read"]
        
        self.msal_app = ConfidentialClientApplication(
            self.client_id,
            authority=self.authority,
            client_credential=self.client_secret
        )

    async def get_auth_url(self, state: str = None):
        """Generate Microsoft OAuth authorization URL"""
        try:
            auth_url = self.msal_app.get_authorization_request_url(
                self.scopes,
                redirect_uri=self.redirect_uri,
                state=state,
                prompt="select_account"
            )
            return auth_url
        except Exception as e:
            logger.error(f"Error generating auth URL: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate auth URL")

    async def handle_auth_callback(self, code: str, authorization: str = None, db=None, user_id=None):
        """Handle OAuth callback and store tokens in user's OutlookConfig"""
        try:
            logger.info(f"Starting auth callback handling with code length: {len(code)}")
            print(f"Starting auth callback with code: {code[:10]}...")  # Print first 10 chars for debugging

            # Exchange code for tokens
            logger.info("Attempting to acquire token with authorization code")
            print("Attempting to acquire token with MSAL...")
            
            try:
                token_response = self.msal_app.acquire_token_by_authorization_code(
                    code,
                    scopes=self.scopes,
                    redirect_uri=self.redirect_uri
                )
                print("Token response received")
                print("Token response keys: {token_response.keys()}",token_response.keys(),token_response)
                logger.debug(f"Token response keys: {token_response.keys()}")
            except Exception as token_error:
                error_msg = f"Token acquisition failed: {str(token_error)}"
                logger.error(error_msg)
                print(f"Token error: {error_msg}")
                raise HTTPException(status_code=500, detail=error_msg)

            if "error" in token_response:
                error_msg = f"Token acquisition error: {token_response.get('error_description', 'Unknown error')}"
                logger.error(error_msg)
                print(f"Token response error: {error_msg}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Auth error: {token_response.get('error_description')}"
                )

            logger.info("Successfully acquired token")
            print("Token acquired successfully")

            # Get user email from token response
            try:
                logger.info("Attempting to fetch user details from Graph API")
                print("Fetching user details from Microsoft Graph...")
                
                headers = {
                    'Authorization': f'Bearer {token_response.get("access_token")}',
                    'Content-Type': 'application/json'
                }
                
                user_response = requests.get('https://graph.microsoft.com/v1.0/me', headers=headers)
                print(f"Graph API response status: {user_response.status_code}")
                logger.debug(f"Graph API response: {user_response.text[:200]}...")  # Log first 200 chars
                
                if user_response.status_code != 200:
                    error_msg = f"Failed to fetch user details. Status: {user_response.status_code}, Response: {user_response.text}"
                    logger.error(error_msg)
                    print(f"Graph API error: {error_msg}")
                    raise HTTPException(status_code=500, detail=error_msg)
                
                user_data = user_response.json()
                user_email = user_data.get('userPrincipalName')
                
                if not user_email:
                    error_msg = "User email not found in Graph API response"
                    logger.error(error_msg)
                    print(error_msg)
                    raise HTTPException(status_code=500, detail=error_msg)
                
                logger.info(f"Successfully retrieved user email: {user_email}")
                print(f"Retrieved user email: {user_email}")
                
            except requests.exceptions.RequestException as req_error:
                error_msg = f"Network error fetching user details: {str(req_error)}"
                logger.error(error_msg)
                print(f"Request error: {error_msg}")
                raise HTTPException(status_code=500, detail=error_msg)
            except ValueError as json_error:
                error_msg = f"Invalid JSON in Graph API response: {str(json_error)}"
                logger.error(error_msg)
                print(f"JSON error: {error_msg}")
                raise HTTPException(status_code=500, detail=error_msg)

            # Update user's email configuration
            update_data = {
                "email_config.provider": "outlook",
                "email_config.outlook_config": {
                    # Original implementation
                    # "access_token": token_response.get("access_token"),
                    # "refresh_token": token_response.get("refresh_token"),
                    # Fernet encrypted version for future use
                    "access_token": fernet.encrypt(token_response.get("access_token").encode()).decode(),
                    "refresh_token": fernet.encrypt(token_response.get("refresh_token").encode()).decode(),
                    "expires_in": token_response.get("expires_in"),
                    "token_timestamp": datetime.utcnow().timestamp(),
                    "email": user_email
                }
            }

            # result = await db.users.update_one(
            #     {"_id": ObjectId(user_id)},
            #     {"$set": update_data}
            # )

            # if result.modified_count == 0:
            #     raise HTTPException(status_code=404, detail="User not found")

            subscription_data = None
            # Setup email subscription only if db and user_id are provided
            if db and user_id:
                from app.services.subscription_manager import SubscriptionManager
                subscription_manager = SubscriptionManager(db)
                
                # Use configured notification URL or fallback
                notification_url = os.getenv("NOTIFICATION_URL", "https://email-ai-test.comorin.co/emails/notifications/outlook")
                
                try:
                    subscription_data = await subscription_manager.setup_subscription(
                        user_id,
                        notification_url
                    )
                    logger.info(f"Successfully setup subscription for user {user_id}")
                except Exception as e:
                    logger.error(f"Failed to setup subscription: {str(e)}")

            # Calculate token expiry timestamp
            token_expiry = int(datetime.now(timezone.utc).timestamp() + token_response.get("expires_in", 3600))
            
            # Call credentials endpoint with tokens
            credentials_url = os.getenv("CREDENTIALS_ENDPOINT", "http://localhost:8000/credentials")
            credentials_payload = {
                "credentials": [
                    {"key": "outlook_access_token", "value": token_response.get("access_token")},
                    {"key": "outlook_refresh_token", "value": token_response.get("refresh_token")},
                    {"key": "outlook_client_id", "value": self.client_id},
                    {"key": "outlook_client_secret", "value": self.client_secret},
                    {"key": "outlook_tenant_id", "value": self.tenant_id},
                    {"key": "outlook_token_expires", "value": str(token_expiry)}
                ]
            }
            
            try:
                headers = {"Authorization": authorization} if authorization else {}
                response = requests.post(credentials_url, json=credentials_payload, headers=headers)
                response.raise_for_status()
            except Exception as e:
                logger.error(f"Failed to send credentials: {str(e)}")
            
            return {
                "status_code": 200,
                "status": "success",
                "message": "Outlook authentication successful",
                "data": {
                    **update_data,
                    "subscription": subscription_data
                }
            }

        except Exception as e:
            logger.error(f"Error handling auth callback: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to complete authentication")

    async def refresh_token(self, db: AsyncIOMotorDatabase, user_id: str):
        """Refresh expired access token"""
        try:
            # print("Refreshing token...")
            config = await db.users.find_one({"_id": ObjectId(user_id)})
            print("refersh",config)
            if not config or "outlook_config" not in config["email_config"]:
                raise HTTPException(status_code=404, detail="Outlook configuration not found")

            # Get refresh token
            # refresh_token = config["email_config"]["outlook_config"].get("refresh_token")
            # Fernet decryption for future use
            encrypted_refresh_token = config["email_config"]["outlook_config"].get("refresh_token")
            refresh_token = fernet.decrypt(encrypted_refresh_token.encode()).decode()
            if not refresh_token:
                raise HTTPException(status_code=401, detail="No refresh token available")
            # print("refresh_token",refresh_token)
            token_response = self.msal_app.acquire_token_by_refresh_token(
                refresh_token,
                scopes=self.scopes
            )
            print("token_response-------------",token_response)

            if "error" in token_response:
                raise HTTPException(status_code=401, detail="Failed to refresh token")

            # Store token data
            token_data = {
                # Original implementation
                # "access_token": token_response.get("access_token"),
                # "refresh_token": token_response.get("refresh_token", refresh_token),
                # Fernet encrypted version for future use
                "access_token": fernet.encrypt(token_response.get("access_token").encode()).decode(),
                "refresh_token": fernet.encrypt(token_response.get("refresh_token", refresh_token).encode()).decode(),
                "expires_in": token_response.get("expires_in"),
                "email": config["email_config"]["outlook_config"].get("email"),
                "token_timestamp": datetime.now(timezone.utc).timestamp()
            }

            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"email_config.outlook_config": token_data}}
            )
            return token_response.get("access_token")

            # return fernet.encrypt(token_response.get("access_token").encode()).decode()

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to refresh token")

    async def _get_headers(self, db: AsyncIOMotorDatabase, user_id: str) -> Dict[str, str]:
        """Get headers with valid access token for API requests"""
        access_token = await self.get_valid_token(db, user_id)
        return {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

    async def process_outlook_message(self, db: AsyncIOMotorDatabase, user_id: str, message: dict) -> dict:
        """Process an Outlook message with attachments"""
        try:
            # Get message details
            message_id = message.get('id')
            if not message_id:
                raise ValueError("Message ID is required")

            # Get full message with attachments
            headers = await self._get_headers(db, user_id)
            message_url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}?$expand=attachments"
            response = requests.get(message_url, headers=headers)
            response.raise_for_status()
            full_message = response.json()

            # Process email with attachments
            email_data, attachments = await attachment_service.process_email_attachments(
                email_message=full_message,
                service=self,
                message_id=message_id,
                provider='outlook'
            )

            # Get the email body content
            body_content = full_message.get('body', {})
            body_text = body_content.get('content', '')
            body_type = body_content.get('contentType', 'text')
            
            # If no body text found in the main body, check the uniqueBody
            if not body_text:
                body_content = full_message.get('uniqueBody', {})
                body_text = body_content.get('content', '')
            
            # If still no body text, try to get it from the snippet
            if not body_text:
                body_text = full_message.get('snippet', '')
            
            # Prepare email document for database
            sender = full_message.get('sender', {})
            recipients = full_message.get('toRecipients', [])
            cc_recipients = full_message.get('ccRecipients', [])
            bcc_recipients = full_message.get('bccRecipients', [])

            # Get the primary recipient (first in the list or empty string if none)
            to_emails = [r.get('emailAddress', {}).get('address', '') for r in recipients]
            recipient_email = to_emails[0] if to_emails else ''
            
            # Handle date formatting
            try:
                received_date = full_message.get('receivedDateTime')
                if received_date and isinstance(received_date, str):
                    # Parse the date string to datetime object
                    from dateutil import parser
                    if 'T' in received_date and ('Z' in received_date or '+' in received_date):
                        # Handle ISO format with timezone
                        date_obj = parser.isoparse(received_date)
                    else:
                        # Parse other date formats
                        date_obj = parser.parse(received_date)
                else:
                    # Fallback to current time
                    date_obj = datetime.utcnow()
                
                # Convert to UTC timezone-naive datetime for MongoDB
                if hasattr(date_obj, 'tzinfo') and date_obj.tzinfo is not None:
                    date_obj = date_obj.astimezone(timezone.utc).replace(tzinfo=None)
                
                # Store as datetime object for MongoDB to store as ISODate
                date_value = date_obj
                
            except Exception as e:
                logger.warning(f"Error parsing date: {str(e)}")
                date_obj = datetime.utcnow()
                date_value = date_obj
                
            # Set fetched date to current UTC time as datetime object
            fetched_date = datetime.utcnow().replace(tzinfo=None)
            
            email_doc = {
                'user_id': user_id,
                'message_id': message_id,
                'conversation_id': full_message.get('conversationId'),
                'subject': full_message.get('subject', '(No subject)'),
                'sender': sender.get('emailAddress', {}).get('address', ''),
                'from_email': sender.get('emailAddress', {}).get('address', ''),
                'from_name': sender.get('emailAddress', {}).get('name', ''),
                'to': to_emails,
                'recipient': recipient_email,
                'cc': [r.get('emailAddress', {}).get('address', '') for r in cc_recipients],
                'bcc': [r.get('emailAddress', {}).get('address', '') for r in bcc_recipients],
                'date': date_value,
                'fetched_date': fetched_date,
                'body': body_text if body_type.lower() == 'text' else body_text if body_type.lower() == 'html' else '',
                'html_body': body_text if body_type.lower() == 'html' else '',
                'is_read': not full_message.get('isRead', False),
                'folder': 'INBOX',  # Default folder, can be updated based on actual folder
                'status': 'unprocessed',
                'attachments': [{
                    'filename': att['filename'],
                    's3_key': att['s3_key'],
                    'content_type': att['mime_type'],
                    'size': att['size'],
                    'is_inline': att['is_inline'],
                    'content_id': att.get('content_id')
                } for att in attachments],
                'inline_images': {
                    cid: {
                        's3_key': img['s3_key'],
                        'content_type': img['content_type'],
                        'size': img['size'],
                        'url': img['url']
                    } for cid, img in email_data.get('inline_images', {}).items()
                },
                'processed': False,
                'created_at': datetime.utcnow()
            }

            # Store email in database
            result = await db.emails.insert_one(email_doc)
            email_doc['_id'] = str(result.inserted_id)
            
            return email_doc

        except Exception as e:
            logger.error(f"Error processing Outlook message: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    async def get_valid_token(self, db: AsyncIOMotorDatabase, user_id: str):
        """Get a valid access token, refreshing if necessary"""
        try:
            logger.info(f"Getting valid token for user {user_id}")

            config = await db.users.find_one({"_id": ObjectId(user_id)})
            # print("config",config)
            if not config:
                logger.error(f"No configuration found for user {user_id}")
                raise HTTPException(status_code=404, detail="User configuration not found")

            email_config = config.get("email_config", {})
            auth_data = email_config.get("outlook_config", {})

            # Validate required fields
            required_fields = ["access_token", "refresh_token", "expires_in", "token_timestamp"]
            missing_fields = [field for field in required_fields if field not in auth_data]
            if missing_fields:
                print("Missing required fields:", missing_fields)
                logger.error(f"Missing required fields: {missing_fields}")
                raise HTTPException(status_code=400, detail=f"Missing required fields: {', '.join(missing_fields)}")

            # Convert timestamps to UTC
            utc_now = datetime.now(timezone.utc).timestamp()
            print("utc_now",utc_now)
            token_timestamp = auth_data.get("token_timestamp", 0)
            print("token_timestamp",token_timestamp)
            token_age = utc_now - token_timestamp
            print("token_age",token_age)

            logger.info(f"UTC Now: {utc_now}, Token Timestamp: {token_timestamp}, Token Age: {token_age} seconds")
            print(f"UTC Now: {utc_now}, Token Timestamp: {token_timestamp}, Token Age: {token_age} seconds")

            # Buffer of 5 minutes before expiry
            if token_age >= (auth_data["expires_in"] - 300):
                print("Token expired or near expiry, refreshing...")
                logger.info("Token expired or near expiry, refreshing...")
                return await self.refresh_token(db, user_id)

            logger.info("Using existing valid token")
            # Return access token
            # return auth_data["access_token"]
            # Fernet decryption for future use
            encrypted_token = auth_data["access_token"]
            print("get valid token",encrypted_token)
            return fernet.decrypt(encrypted_token.encode()).decode()

        except Exception as e:
            logger.error(f"Error fetching token for user {user_id}: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to retrieve valid token")

