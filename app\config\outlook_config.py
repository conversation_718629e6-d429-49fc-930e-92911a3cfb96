from msal import ConfidentialClientApplication
import os

 # OAuth configuration
OUTLOOK_CLIENT_ID = os.getenv('OUTLOOK_CLIENT_ID', '7beda09d-3346-4a6c-b0fe-59d2ba8f90cc')
OUTLOOK_CLIENT_SECRET = os.getenv('OUTLOOK_CLIENT_SECRET', '****************************************')
OUTLOOK_TENANT_ID = os.getenv('OUTLOOK_TENANT_ID', 'f8cdef31-a31e-4b4a-93e4-5f571e91255a')
OUTLOOK_REDIRECT_URI = os.getenv('OUTLOOK_REDIRECT_URI', 'http://localhost:8000/auth/callback')
OUTLOOK_AUTHORITY = "https://login.microsoftonline.com/common"
OUTLOOK_SCOPES = ["Mail.Read", "Mail.ReadWrite", "Mail.Send"]

def get_msal_app():
     return ConfidentialClientApplication(
         OUTLOOK_CLIENT_ID,
         authority=OUTLOOK_AUTHORITY,
         client_credential=OUTLOOK_CLIENT_SECRET
    )