from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from bson import ObjectId

class WorkflowState(str, Enum):
    INITIATED = "initiated"
    FETCHED = "fetched"
    CATEGORY_CLASSIFIED = "category_classified"
    SENTIMENT_ANALYZED = "sentiment_analyzed"
    CONTEXT_RETRIEVED = "context_retrieved"
    RESPONSE_GENERATED = "response_generated"
    DRAFT_CREATED = "draft_created"
    COMPLETED = "completed"
    ERROR = "error"
    
class WorkflowTransition(str, Enum):
    FETCH_EMAILS = "fetch_emails"
    CLASSIFY_CATEGORY = "classify_category"
    ANALYZE_SENTIMENT = "analyze_sentiment"
    RETRIEVE_CONTEXT = "retrieve_context"
    GENERATE_RESPONSE = "generate_response"
    CREATE_DRAFT = "create_draft"
    COMPLETE = "complete"
    ERROR = "error"

class WorkflowContext(BaseModel):
    user_id: str
    email_ids: List[ObjectId]
    current_state: WorkflowState
    error_message: Optional[str] = None
    results: Dict[str, Any] = {}
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    retry_count: int = 0
    max_retries: int = 3

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat(),
            ObjectId: str
        }
    }

    def dict(self, *args, **kwargs):
        d = super().dict(*args, **kwargs)
        # Convert datetime objects to ISO format strings
        if "created_at" in d:
            d["created_at"] = d["created_at"].isoformat()
        if "updated_at" in d:
            d["updated_at"] = d["updated_at"].isoformat()
        return d
