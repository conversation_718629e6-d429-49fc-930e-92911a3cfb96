from pydantic import BaseModel
from typing import Dict, List, Optional

class SystemPrompt(BaseModel):
    name: str
    prompt: str
    system_prompt: str
    description: Optional[str] = None

class PromptSettings(BaseModel):
    prompts: Dict[str, SystemPrompt]
    email_adapter: Optional[Dict[str, List[str]]] = None

class PromptSettingsResponse(BaseModel):
    status_code: int = 200
    status: str = "OK" 
    message: str = "Prompt settings retrieved successfully"
    data: PromptSettings

class PromptSettingsUpdate(BaseModel):
    prompts: Optional[Dict[str, SystemPrompt]] = None
    email_adapter: Optional[Dict[str, List[str]]] = None
