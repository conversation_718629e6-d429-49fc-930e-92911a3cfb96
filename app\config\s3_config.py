import boto3
from botocore.config import Config
import os
import aioboto3
from functools import lru_cache

class S3Config:
    _instance = None
    _client = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(S3Config, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._client is not None:
            return
        self.aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        self.aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.region = os.getenv('AWS_REGION', 'us-east-1')
        self.bucket_name = os.getenv('S3_BUCKET_NAME')
        
        # S3 client configuration
        self.config = Config(
            region_name=self.region,
            retries={
                'max_attempts': 3,
                'mode': 'standard'
            }
        )
        
        # Initialize aioboto3 session
        self.session = aioboto3.Session(
            aws_access_key_id=self.aws_access_key,
            aws_secret_access_key=self.aws_secret_key,
            region_name=self.region
        )
        
    async def get_client(self):
        if self._client is None:
            self._client = self.session.client('s3', config=self.config)
        return self._client

    @property
    def client(self):
        return boto3.client(
            's3',
            aws_access_key_id=self.aws_access_key,
            aws_secret_access_key=self.aws_secret_key,
            region_name=self.region,
            config=self.config
        )

s3_client = S3Config().client