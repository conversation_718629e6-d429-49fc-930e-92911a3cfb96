import json
import base64
from datetime import datetime

def encode_state(data: dict) -> str:
    """Encode state data to base64"""
    json_data = json.dumps(data)
    return base64.b64encode(json_data.encode()).decode()

def decode_state(state: str) -> dict:
    """Decode state data from base64"""
    try:
        json_data = base64.b64decode(state.encode()).decode()
        return json.loads(json_data)
    except Exception as e:
        raise ValueError(f"Invalid state parameter: {str(e)}")
