from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.config.database import get_db
from app.utils.security import decode_jwt_token
from typing import List, Dict
from datetime import datetime
import logging

router = APIRouter(prefix="/category-templates", tags=["category-templates"])
logger = logging.getLogger(__name__)

class CategoryTemplate:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db

    async def store_template(self, category: str, template: str) -> Dict:
        """Store category template in database"""
        try:
            result = await self.db.category_templates.update_one(
                {"category": category},
                {"$set": {
                    "template": template,
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )
            return {"status": "success", "message": "Template stored successfully"}
        except Exception as e:
            logger.error(f"Error storing template: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to store template")

    async def get_template(self, category: str) -> str:
        """Retrieve category template from database"""
        try:
            result = await self.db.category_templates.find_one(
                {"category": category}
            )
            return result["template"] if result else None
        except Exception as e:
            logger.error(f"Error retrieving template: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to retrieve template")

@router.post("/{category}")
async def store_category_template(
    category: str,
    template: Dict[str, str],
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Store template for a specific category"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        category_template = CategoryTemplate(db)
        result = await category_template.store_template(category, template["template"])
        
        return {
            "status": "success",
            "message": "Template stored successfully",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error storing category template: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to store category template")

@router.get("/{category}")
async def get_category_template(
    category: str,
    db: AsyncIOMotorDatabase = Depends(get_db),
    authorization: str = Header(None)
):
    """Get template for a specific category"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        category_template = CategoryTemplate(db)
        template = await category_template.get_template(category)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {
            "status": "success",
            "data": {"template": template}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving category template: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve category template")
