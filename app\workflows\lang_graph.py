import asyncio
import imaplib
import email as email_lib
import re
import uuid
import aiohttp
from typing import Dict, Any, List, TypedDict, Optional, Union
from fastapi import HTTPException
import requests
from typing_extensions import Annotated
from langgraph.graph import StateGraph, END
from app.services.claude_service import ClaudeService
from app.services.category_service import CategoryService
from motor.motor_asyncio import AsyncIOMotorDatabase
import logging
from datetime import datetime, timezone
from bson import ObjectId
from app.utils.security import fernet
from app.services.outlook_service import OutlookService
from app.config.constant import RAG_ENDPOINT

logger = logging.getLogger(__name__)

class WorkflowState(TypedDict):
    """Comprehensive state for email processing workflow"""
    workflow_id: str
    user_id: str
    processing_id: str
    current_email_id: str
    email: Optional[Dict[str, Any]]
    classification: Optional[str]
    sentiment: Optional[Dict[str, Any]]
    context: Optional[Dict[str, Any]]
    response: Optional[str]
    draft: Optional[Dict[str, Any]]
    error: Optional[str]
    classifications: Dict[str, str]
    sentiments: Dict[str, Dict[str, Any]]
    responses: Dict[str, str]
    drafts: Dict[str, Dict[str, Any]]
    action: Optional[Dict[str, Any]]
    data_validation: Optional[Dict[str, Any]]
    has_missing_fields: Optional[bool]

class EmailProcessingGraph:
    """Enhanced LangGraph implementation of email processing workflow"""

    def __init__(self, db):
        """Initialize the graph with state schema"""
        from app.config.db_pool import get_workflow_db
        self.db = get_workflow_db()  # Use dedicated workflow connection
        self.graph = StateGraph(WorkflowState)
        self.claude_service = ClaudeService(self.db)
        self.category_service = CategoryService(self.db)

        # Build graph structure
        self.build_graph()

    async def fetch_emails(self, state: WorkflowState) -> WorkflowState:
        """Node for fetching emails"""
        try:
            current_email_id = state['current_email_id']
            user_id = state['user_id']

            # Use find_one with maxTimeMS to limit query execution time
            email = await self.db.emails.find_one(
                {
                    "_id": ObjectId(current_email_id),
                    "user_id": user_id 
                },
                max_time_ms=2000  # 2 second timeout
            )

            if not email:
                raise ValueError(f"Email not found: {current_email_id}")

            # Convert ObjectId to string
            email["_id"] = str(email["_id"])

            # Update state with fetched email
            return {
                **state,
                "email": email,
                "error": None
            }

        except Exception as e:
            logger.error(f"Error fetching emails: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def classify_category(self, state: WorkflowState) -> WorkflowState:
        """Node for classifying email category using Claude AI"""
        try:
            email = state.get("email", {})
            current_email_id = state['current_email_id']

            if not email or "body" not in email:
                logger.error("Email or email body missing")
                category = "unknown"
            else:
                try:
                    category = await self.claude_service.classify_category(
                        email["body"], 
                        state["user_id"]
                    )
                    category = category if isinstance(category, str) else "unknown"
                    logger.info(f"Classified email {current_email_id} as category: {category}")
                except Exception as e:
                    logger.error(f"Classification error: {str(e)}")
                    category = "unknown"

            # Update email in database
            await self.db.emails.update_one(
                {"_id": ObjectId(current_email_id)},
                {"$set": {
                    "category": category,
                    "status": "inprocess",
                    "stage": "classify_category",
                    "updated_date": datetime.utcnow()
                }}
            )

            # Update classifications and classification in state
            classifications = state.get('classifications', {})
            classifications[current_email_id] = category

            return {
                **state,
                "classification": category,
                "classifications": classifications,
                "error": None
            }

        except Exception as e:
            logger.error(f"Error classifying email: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def detect_actions(self, state: WorkflowState) -> WorkflowState:
        """Node for detecting predefined actions in email"""
        try:
            email = state.get("email", {})
            current_email_id = state['current_email_id']
            
            action_result = await self.claude_service.detect_actions(
                email["body"],
                state["user_id"]
            )
            
            # Update email in database with all detected actions and dependency information
            update_data = {
                "detected_actions": action_result["detected_actions"],
                "action_confidence": action_result["overall_confidence"],
                "action_dependency_chain": action_result.get("dependency_chain", []),
                "action_analysis": {
                    "overall_confidence": action_result["overall_confidence"],
                    "dependency_chain": action_result.get("dependency_chain", []),
                    "timestamp": datetime.utcnow()
                },
                "action_dependencies": {
                    action["action_name"]: {
                        "requires": action["dependencies"]["requires_actions"],
                        "required_by": action["dependencies"]["required_by_actions"],
                        "reason": action["dependencies"]["dependency_reason"],
                        "confidence": action["confidence"],
                        "context": action["context"]
                    }
                    for action in action_result["detected_actions"]
                },
                "status": "inprocess",
                "stage": "actions_detected",
                "updated_date": datetime.utcnow()
            }
            
            # Store action detection history
            history_entry = {
                "timestamp": datetime.utcnow(),
                "action_result": action_result,
                "email_id": current_email_id
            }
            
            await self.db.action_detection_history.insert_one(history_entry)
            
            # Update email document with detection results
            await self.db.emails.update_one(
                {"_id": ObjectId(current_email_id)},
                {"$set": update_data}
            )
            
            return {
                **state,
                "action": action_result,
                "has_missing_fields": None,  # Will be set after validation
                "error": None
            }
            
        except Exception as e:
            logger.error(f"Error detecting actions: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def analyze_sentiment(self, state: WorkflowState) -> WorkflowState:
        """Node for sentiment analysis using Claude AI"""
        try:
            email = state.get("email", {})
            current_email_id = state['current_email_id']

            sentiment_result = await self.claude_service.analyze_sentiment(
                email["body"], 
                state["user_id"]
            )
            
            # Ensure sentiment result is a dictionary
            if not isinstance(sentiment_result, dict):
                sentiment_result = {
                    "label": "neutral",
                    "score": 0.0,
                    "confidence": 0.0 
                }

            # Update email in database
            await self.db.emails.update_one(
                {"_id": ObjectId(current_email_id)},
                {"$set": {
                    "sentiment": sentiment_result,
                    "status": "inprocess",
                    "stage": "sentiment_analysis",
                    "updated_date": datetime.utcnow()
                }}
            )

            # Update sentiments in state
            sentiments = state.get('sentiments', {})
            sentiments[current_email_id] = sentiment_result

            return {
                **state,
                "sentiment": sentiment_result,
                "sentiments": sentiments,
                "error": None
            }

        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def retrieve_context(self, state: WorkflowState) -> WorkflowState:
        """Node for context retrieval using RAG API"""
        try:
            email = state.get("email", {})
            current_email_id = state['current_email_id']
            category = state.get("classifications", {}).get(current_email_id)
            
            # Prepare email content and category for RAG query
            email_content = email.get("body", "")
            category = state.get("classifications", {}).get(current_email_id, "unknown")
            
            # Construct RAG query incorporating category context into question
            print("email_content",email_content)
            rag_query = {
                "question": email_content
            }
            
            # Query RAG API
            try:
                async with aiohttp.ClientSession() as session:
                    try:
                        async with session.post(
                            RAG_ENDPOINT,
                            json=rag_query,
                            timeout=30  # 30 second timeout
                        ) as response:
                            if response.status != 200:
                                logger.error(f"RAG API error: Status {response.status}")
                                response_text = await response.text()
                                logger.error(f"RAG API error response: {response_text}")
                                rag_response = {"error": f"RAG API returned status {response.status}", "details": response_text}
                            else:
                                rag_response = await response.json()
                    except aiohttp.ClientError as e:
                        logger.error(f"RAG API connection error: {str(e)}")
                        rag_response = {"error": f"RAG API connection failed: {str(e)}"}
            except Exception as e:
                logger.error(f"RAG API request failed: {str(e)}")
                rag_response = {"error": f"RAG API request failed: {str(e)}"}

            context = {
                "category": category,
                "rag_context": rag_response,
                "has_error": "error" in rag_response,
                "category_template": email.get("category_template", ""),
                "action_validations": email.get("action_validations", []),
                "action_executions": email.get("action_executions", {}),
                "detected_actions": email.get("detected_actions", []),
                "action_confidence": email.get("action_confidence", 0.0),
                "action_dependency_chain": email.get("action_dependency_chain", []),
                "action_analysis": email.get("action_analysis", {}),
                "action_dependencies": email.get("action_dependencies", {}),
                "action_payloads": email.get("action_payloads", {})
            }

            # Initialize validation results
            validation_results = []
            
            # Update email in database with context and validation data
            update_data = {
                "context": context,
                "status": "inprocess", 
                "stage": "context_retrieved",
                "updated_date": datetime.utcnow()
            }

            # Add validation data if we have results
            if validation_results:
                update_data.update({
                    "data_validation": validation_results,
                    "action_validations": {
                        result["action_name"]: {
                            "matched_data": result["validation"]["matched_data"],
                            "missing_fields": result["validation"]["missing_fields"],
                            "confidence_scores": result["validation"]["confidence_scores"],
                            "dependencies": result["validation"].get("dependencies", {})
                        }
                        for result in validation_results
                    }
                })

            await self.db.emails.update_one(
                {"_id": ObjectId(current_email_id)},
                {"$set": update_data}
            )

            # Update state with context
            state_update = {
                **state,
                "context": context,  # Store context directly, not nested under email_id
                "error": None
            }

            # If actions were detected, proceed with data validation for each action
            action_result = state.get("action", {})
            if action_result.get("detected_actions"):
                try:
                    validation_results = []
                    
                    for detected_action in action_result["detected_actions"]:
                        action_name = detected_action["action_name"]
                        
                        try:
                            action = await self.db.actions.find_one({"name": action_name})
                        except Exception as e:
                            logger.error(f"Invalid action name: {action_name}: {str(e)}")
                            continue
                            
                        if action and action.get("parameters"):
                            # Validate and extract data for this action
                            validation_result = await self.claude_service.validate_missing_data(
                                email["body"],
                                action["parameters"],
                                context.get("rag_context", {})
                            )
                            
                            validation_results.append({
                                "action_name": action_name,
                                "validation": validation_result,
                                "confidence": detected_action["confidence"],
                                "context": detected_action["context"]
                            })
                    
                    if validation_results:
                        # Update email with extracted data for all actions
                        update_data = {
                            "action_validations": validation_results,
                            "action_payloads": {
                                result["action_name"]: {
                                    "parameters": (await self.db.actions.find_one({"name": result["action_name"]}))["parameters"],
                                    "extracted_data": result["validation"]["matched_data"],
                                    "missing_fields": result["validation"]["missing_fields"],
                                    "confidence_scores": result["validation"]["confidence_scores"]
                                }
                                for result in validation_results
                            }
                        }
                        
                        await self.db.emails.update_one(
                            {"_id": ObjectId(current_email_id)},
                            {"$set": update_data}
                        )
                        
                        # Add validation results to state and check for missing fields
                        state_update["data_validation"] = validation_results
                        state_update["has_missing_fields"] = any(
                            result["validation"]["missing_fields"] 
                            for result in validation_results
                        )
                
                except Exception as e:
                    logger.error(f"Error in data validation: {str(e)}")
                    state_update["error"] = f"Data validation failed: {str(e)}"
            
            return state_update

        except HTTPException as he:
            logger.error(f"HTTP error retrieving RAG context: {str(he)}")
            return {
                **state,
                "error": str(he)
            }
        except Exception as e:
            logger.error(f"Error retrieving context: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def generate_response(self, state: WorkflowState) -> WorkflowState:
        """Node for response generation using Claude AI"""
        try:
            email = state.get("email", {})
            current_email_id = state['current_email_id']
            
            context = state.get("context", {})
            category = state.get("classification", "unknown")  # Use direct classification
            sentiment = state.get("sentiment", {})  # Use direct sentiment
            
            # Check if context retrieval failed
            if context.get("has_error", False):
                logger.warning(f"Generating response without RAG context due to previous error")
                context = {"category": category}  # Fallback to just using category
            
            # Get complete email data for context
            email_data = await self.db.emails.find_one({"_id": ObjectId(current_email_id)})
            
            # Ensure we have all necessary data
            complete_context = {
                **context,
                "email_data": email_data,
                "workflow_id": state.get("workflow_id"),
                "processing_id": state.get("processing_id")
            }
            
            response = await self.claude_service.generate_response(
                email["body"],
                category,
                sentiment or {"label": "neutral", "score": 0.0},  # Provide default sentiment
                complete_context,
                state["user_id"]
            )
            
            # Update email in database with response
            await self.db.emails.update_one(
                {"_id": ObjectId(current_email_id)},
                {"$set": {
                    "response": response,
                    "status": "inprocess",
                    "stage": "response_generated",
                    "updated_date": datetime.utcnow()
                }}
            )

            # Update responses in state
            responses = state.get('responses', {})
            responses[current_email_id] = response

            return {
                **state,
                "response": response,
                "responses": responses,
                "error": None
            }

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def create_draft(self, state: WorkflowState) -> WorkflowState:
        """Create a draft email on mail server and update email status"""
        try:
            user_id = state['user_id']
            current_email_id = state['current_email_id']
            response_text = state.get('response')
            
            if not response_text:
                raise ValueError("No response text available in state")

            # Get user's email configuration
            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            if not user or "email_config" not in user:
                raise HTTPException(status_code=404, detail="User email configuration not found")

            # Get original email details
            email = await self.db.emails.find_one({"_id": ObjectId(current_email_id)})
            if not email:
                raise HTTPException(status_code=404, detail="Email not found")

            email_provider = user["email_config"].get("email_provider", "gmail")
            draft_id = None

            if email_provider == "outlook":
                # Create Outlook draft using Microsoft Graph API
                outlook_service = OutlookService()
                access_token = await outlook_service.get_valid_token(self.db, user_id)
                
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                # Create draft as reply to original message using message ID
                draft_message = {
                    "message": {
                        "subject": f"Re: {email.get('subject', '')}",
                        "toRecipients": [{"emailAddress": {"address": email.get('sender')}}],
                        "body": {
                            "content": response_text,
                            "contentType": "text"
                        },
                        "conversationId": email.get('message_id'),  # Link to original conversation
                        "references": [email.get('message_id')],  # Reference original message
                        "inReplyTo": email.get('message_id')  # Mark as reply to original
                    }
                }
                
                response = requests.post(
                    'https://graph.microsoft.com/v1.0/me/messages/draft',
                    headers=headers,
                    json=draft_message
                )
                
                if response.status_code != 201:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create Outlook draft: {response.text}"
                    )
                
                draft_id = response.json().get('id')

            else:
                # Gmail/IMAP draft creation
                email_config = user["email_config"]

                try:
                    decrypted_password = fernet.decrypt(email_config["password"].encode()).decode()
                except Exception as e:
                    raise HTTPException(status_code=500, detail="Failed to decrypt email password")

                # Determine IMAP server
                if "gmail.com" in email_config["email"].lower():
                    imap_server = "imap.gmail.com"
                else:
                    imap_server = email_config.get("host", "imap.example.com")

                # Connect to IMAP server
                mail = imaplib.IMAP4_SSL(imap_server)
                mail.login(email_config["email"], decrypted_password)

                # Create draft message with proper references
                msg = email_lib.message.EmailMessage()
                msg["From"] = email_config["email"]
                msg["To"] = email.get('sender')
                msg["Subject"] = f"Re: {email.get('subject', '')}"
                msg["Message-ID"] = f"<{uuid.uuid4()}@{email_config['email'].split('@')[1]}>"
                msg["In-Reply-To"] = email.get('message_id')
                msg["References"] = email.get('message_id')
                msg.set_content(response_text)

                # Draft folder detection and selection
                draft_folders = [
                    "[Gmail]/Drafts",
                    "Drafts",
                    "DRAFTS",
                    "[Gmail]/Draft",
                    "Draft"
                ]
            
                draft_folder = None
                for folder in draft_folders:
                    try:
                        mail.select(folder)
                        draft_folder = folder
                        break
                    except imaplib.IMAP4.error:
                        continue
            
                if not draft_folder:
                    # Fallback to creating Drafts folder
                    draft_folder = "Drafts"
                    try:
                        mail.create(draft_folder)
                        mail.select(draft_folder)
                    except imaplib.IMAP4.error as e:
                        logger.error(f"Failed to create drafts folder: {str(e)}")
                        raise HTTPException(status_code=500, detail="Failed to create drafts folder")
                
                # Advanced draft ID retrieval mechanism
                draft_id = None
                server_uid = None
                
                try:
                    # Attempt to save draft and get UID
                    result = mail.append(
                        draft_folder, 
                        "", 
                        imaplib.Time2Internaldate(datetime.now(timezone.utc)), 
                        str(msg).encode()
                    )
                    
                    # Multiple strategies to extract draft ID
                    if isinstance(result, tuple):
                        response = result[1][0].decode() if result[1] else ""
                        
                        # Strategy 1: Direct APPENDUID extraction
                        if "[APPENDUID" in response:
                            try:
                                # Parse APPENDUID response format: [APPENDUID <validity_token> <uid>]
                                uid_parts = response.split()
                                logger.debug(f"APPENDUID parts: {uid_parts}")
                            
                                # Extract UID from the APPENDUID response
                                # Format is typically: ['[APPENDUID', '<validity_token>', '<uid>]']
                                if len(uid_parts) >= 3:
                                    # Get the UID part and remove trailing ']'
                                    server_uid = uid_parts[2].rstrip(']')
                                    # Create a unique draft ID that includes all necessary components
                                    draft_id = {
                                        "email": email_config['email'],
                                        "folder": draft_folder,
                                        "server_uid": server_uid,
                                        "validity_token": uid_parts[1],
                                        "full_response": response
                                    }
                                    logger.info(f"Successfully extracted draft ID components: {draft_id}")
                                else:
                                    raise ValueError(f"Unexpected APPENDUID format: {response}")
                            except Exception as e:
                                logger.warning(f"Failed to extract UID from APPENDUID: {e}")
                                draft_id = None
                        
                        # Strategy 2: Search for latest message
                        if not draft_id:
                            try:
                                mail.select(draft_folder)
                                _, messages = mail.search(None, "ALL")
                                latest_email_id = messages[0].split()[-1]
                                _, msg_data = mail.fetch(latest_email_id, "(UID)")
                                
                                # Extract UID from message data
                                for response_part in msg_data:
                                    if isinstance(response_part, tuple):
                                        uid_match = re.search(r'UID ([0-9]+)', response_part[1].decode())
                                        if uid_match:
                                            server_uid = uid_match.group(1)
                                            draft_id = {
                                                "email": email_config['email'],
                                                "folder": draft_folder,
                                                "server_uid": server_uid,
                                                "search_criteria": f"UID {server_uid}"
                                            }
                                            break
                            except Exception as e:
                                logger.warning(f"Fallback UID extraction failed: {e}")
                    
                    # Final fallback: generate a unique identifier
                    if not draft_id:
                        draft_id = f"{email_config['email']}_{draft_folder}_{uuid.uuid4().hex[:8]}"
                        logger.warning("Generated fallback draft ID")
                
                except Exception as e:
                    logger.error(f"Draft creation error: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Draft creation failed: {str(e)}")
                
                finally:
                    mail.logout()
                
                # Ensure draft_id is not None
                if not draft_id:
                    draft_id = f"{email_config['email']}_{draft_folder}_{uuid.uuid4().hex[:8]}"
                
                # Prepare draft data for database storage
                draft_data = {
                    "user_id": user_id,
                    "email_id": current_email_id,
                    "response_text": response_text,
                    "status": "draft",
                    "draft_server_id": draft_id,
                    "email_provider": "gmail" if "gmail.com" in email_config["email"].lower() else "other",
                    "draft_folder": draft_folder,
                    "server_uid": server_uid,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }

                # Insert draft into database
                result = await self.db.drafts.insert_one(draft_data)
                local_draft_id = str(result.inserted_id)

                # Update email status
                await self.db.emails.update_one(
                    {"_id": ObjectId(current_email_id)},
                    {
                        "$set": {
                            "status": "drafted",
                            "draft_id": local_draft_id,
                            "draft_server_id": draft_id,
                            "updated_date": datetime.utcnow()
                        }
                    }
                )

                # Update drafts in state
                drafts = state.get('drafts', {})
                drafts[current_email_id] = {
                    "draft_id": local_draft_id,
                    "draft_server_id": draft_id
                }

                return {
                    **state,
                    "draft": {
                        "draft_id": local_draft_id,
                        "draft_server_id": draft_id
                    },
                    "drafts": drafts,
                    "error": None
                }

        except Exception as e:
                logger.error(f"Comprehensive draft creation error: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Comprehensive draft creation failed: {str(e)}")

    async def mark_complete(self, state: WorkflowState) -> WorkflowState:
        """Node for marking workflow complete"""
        try:
            workflow_id = state['workflow_id']
            
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {
                    "status": "completed",
                    "results": {
                        "classifications": state.get("classifications", {}),
                        "sentiments": state.get("sentiments", {}),
                        "responses": state.get("responses", {}),
                        "drafts": state.get("drafts", {})
                    },
                    "completed_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }}
            )
            
            # Update emails status
            email_ids = [ObjectId(email_id) for email_id in state.get("classifications", {}).keys()]
            
            if email_ids:
                await self.db.emails.update_many(
                    {"_id": {"$in": email_ids}},
                    {"$set": {
                        "status": "processed",
                        "stage": "completed",
                        "completed_at": datetime.utcnow()
                    }}
                )
                
            return state
        
        except Exception as e:
            logger.error(f"Error marking workflow complete: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    async def handle_error(self, state: WorkflowState) -> WorkflowState:
        """Node for handling errors in the workflow"""
        try:
            workflow_id = state['workflow_id']
            error_msg = state.get("error", "Unknown error occurred")
            
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {
                    "status": "error",
                    "error_message": error_msg,
                    "updated_at": datetime.utcnow()
                }}
            )
            
            return state
        
        except Exception as e:
            logger.error(f"Error in error handler: {str(e)}")
            return {
                **state,
                "error": str(e)
            }

    def build_graph(self):
        """Build the graph structure with nodes and edges"""
        # Add nodes to the graph
        self.graph.add_node("fetch", self.fetch_emails)
        self.graph.add_node("detect_actions", self.detect_actions)
        self.graph.add_node("classify", self.classify_category)
        self.graph.add_node("analyze", self.analyze_sentiment)
        self.graph.add_node("retrieve", self.retrieve_context)
        self.graph.add_node("generate", self.generate_response)
        self.graph.add_node("create_draft", self.create_draft)
        self.graph.add_node("complete", self.mark_complete)
        self.graph.add_node("handle_error", self.handle_error)
        
        # Define routing functions
        def route_after_fetch(state: WorkflowState) -> str:
            return "handle_error" if state.get("error") else "detect_actions"
            
        def route_after_actions(state: WorkflowState) -> str:
            return "handle_error" if state.get("error") else "classify"
            
        def route_after_classify(state: WorkflowState) -> str:
            return "handle_error" if state.get("error") else "analyze"
            
        def route_after_analyze(state: WorkflowState) -> str:
            return "handle_error" if state.get("error") else "retrieve"
            
        def route_after_retrieve(state: WorkflowState) -> str:
            if state.get("error"):
                return "handle_error"
            
            # If actions detected but no missing fields, skip response generation
            if state.get("action", {}).get("detected_actions") and state.get("has_missing_fields") is False:
                return "complete"
                
            return "generate"
            
        def route_after_generate(state: WorkflowState) -> str:
            if state.get("error"):
                return "handle_error"
            
            # If we got here with actions, we need draft for missing fields
            if state.get("action", {}).get("detected_actions"):
                return "create_draft"
            
            return "create_draft"
            
        def route_after_draft(state: WorkflowState) -> str:
            if state.get("error"):
                return "handle_error"
            return "complete"
        
        # Configure edges with conditional routing
        self.graph.add_conditional_edges("fetch", route_after_fetch)
        self.graph.add_conditional_edges("detect_actions", route_after_actions)
        self.graph.add_conditional_edges("classify", route_after_classify)
        self.graph.add_conditional_edges("analyze", route_after_analyze)
        self.graph.add_conditional_edges("retrieve", route_after_retrieve)
        self.graph.add_conditional_edges("generate", route_after_generate)
        self.graph.add_conditional_edges("create_draft", route_after_draft)
        
        # Set entry point
        self.graph.set_entry_point("fetch")
        self.graph.add_edge("handle_error", END)
        self.graph.add_edge("complete", END)

    async def process_email(self, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the graph workflow for a single email"""
        try:
            email_id = initial_state["current_email_id"]
            workflow_id = initial_state["workflow_id"]
            
            # Update workflow status
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {
                    "current_email_id": email_id,
                    "updated_at": datetime.utcnow()
                }}
            )
            
            # Compile and execute graph
            app = self.graph.compile()
            result = await app.ainvoke(initial_state)
            
            # Update workflow with results
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {
                    f"results.{email_id}": result,
                    "updated_at": datetime.utcnow()
                }}
            )
            
            logger.info(f"Email {email_id} processed in workflow {workflow_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing email {email_id}: {str(e)}")
            # Update workflow with error
            await self.db.workflows.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {
                    f"results.{email_id}.error": str(e),
                    "updated_at": datetime.utcnow()
                }}
            )
            return {"error": str(e)}
