<!DOCTYPE html>
<html>
<head>
    <title>Socket.IO Test Client</title>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        #messages { height: 300px; border: 1px solid #ccc; padding: 10px; overflow-y: scroll; margin-bottom: 10px; }
        input, button { padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>Socket.IO Test Client</h1>
    
    <div>
        <label for="userId">Your User ID:</label>
        <input type="text" id="userId" value="user_123">
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>
    
    <div id="connectionStatus">Status: Disconnected</div>
    
    <div id="messages"></div>
    
    <div>
        <label for="targetUserId">Target User ID:</label>
        <input type="text" id="targetUserId" value="user_456">
    </div>
    
    <div>
        <label for="messageInput">Message:</label>
        <input type="text" id="messageInput" placeholder="Type your message">
        <button onclick="sendMessage()">Send Private Message</button>
    </div>

    <script>
        let socket;
        // const serverUrl = "http://localhost:8001"; // Update with your server URL
        const serverUrl = "https://email-ai-test.comorin.co/email-responder/socket.io"; // Update with your server URL
        
        function connect() {
            const userId = document.getElementById('userId').value;
            
            socket = io(serverUrl + '/', {
                path: '/email-responder/socket.io',
                query: { user_id: userId },
                transports: ['websocket']
            });
            
            socket.on('connect', () => {
                logMessage(`Connected as ${userId} (Socket ID: ${socket.id})`);
                updateStatus('Connected');
            });
            
            socket.on('disconnect', () => {
                logMessage('Disconnected');
                updateStatus('Disconnected');
            });
            
            socket.on('private_response', (data) => {
                logMessage(`[Private] ${data.message}`);
            });
            
            socket.on('broadcast_message', (data) => {
                logMessage(`[Broadcast] ${data.message}`);
            });
            
            socket.on('connection_response', (data) => {
                logMessage(`[System] ${data.message}`);
            });
            
            socket.on('authentication_response', (data) => {
                logMessage(`[Auth] ${data.message}`);
            });
            
            // Listen for new_email event
            socket.on('new_email', (data) => {
                logMessage(`[Email] From: ${data.sender}, Subject: ${data.subject}, Message: ${data.body}`);
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
            }
        }
        
        function sendMessage() {
            if (!socket || !socket.connected) {
                alert("Not connected to server!");
                return;
            }
            
            const targetUserId = document.getElementById('targetUserId').value;
            const message = document.getElementById('messageInput').value;
            
            socket.emit('private_message', {
                to: targetUserId,
                message: message
            });
            
            logMessage(`[You → ${targetUserId}] ${message}`);
            document.getElementById('messageInput').value = '';
        }
        
        function logMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.textContent = message;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('connectionStatus').textContent = `Status: ${status}`;
        }
    </script>
</body>
</html>
