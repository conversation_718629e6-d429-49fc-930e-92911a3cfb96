#!/usr/bin/env python3
"""
Test script to verify the two-step password reset functionality
"""

import asyncio
import sys
import os
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.user_service import UserService
from app.config.database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_two_step_reset_password():
    """Test the complete two-step password reset flow"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        test_email = "<EMAIL>"
        new_password = "NewSecurePassword123!"
        
        logger.info("Testing two-step password reset flow...")
        
        # Step 1: Request password reset (generate OTP)
        logger.info("Step 1: Requesting password reset...")
        forget_result = await UserService.forget_password(test_email, db)
        logger.info(f"Forget password result: {forget_result}")
        
        # Get the reset OTP from database
        user = await db.users.find_one({
            "$or": [
                {"email_config.gmail_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.outlook_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"email_config.imap_config.email": {"$regex": f"^{test_email}$", "$options": "i"}}
            ]
        })
        
        if not user or "reset_otp" not in user:
            logger.warning("No user found or no reset OTP in database")
            return {"error": "No user found or no OTP generated"}
        
        reset_otp = user["reset_otp"]
        logger.info(f"Found reset OTP: {reset_otp}")
        
        # Step 2: Validate OTP
        logger.info("Step 2: Validating OTP...")
        otp_validation_result = await UserService.reset_password(
            email=test_email,
            request_type="otp",
            otp=reset_otp,
            db=db
        )
        logger.info(f"OTP validation result: {otp_validation_result}")
        
        # Step 3: Update password
        logger.info("Step 3: Updating password...")
        password_update_result = await UserService.reset_password(
            email=test_email,
            request_type="validation",
            new_password=new_password,
            db=db
        )
        logger.info(f"Password update result: {password_update_result}")
        
        # Close database connection
        client.close()
        
        return {
            "forget_result": forget_result,
            "otp_validation_result": otp_validation_result,
            "password_update_result": password_update_result
        }
        
    except Exception as e:
        logger.error(f"Two-step flow test failed with error: {str(e)}")
        return {"error": str(e)}

async def test_invalid_scenarios():
    """Test various invalid scenarios"""
    try:
        # Connect to database
        client = AsyncIOMotorClient("mongodb://localhost:27017/")
        db = client["email_responder_a2a"]
        
        test_email = "<EMAIL>"
        
        logger.info("Testing invalid scenarios...")
        
        # Test 1: Invalid OTP
        logger.info("Test 1: Testing invalid OTP...")
        try:
            invalid_otp_result = await UserService.reset_password(
                email=test_email,
                request_type="otp",
                otp="999999",  # Invalid OTP
                db=db
            )
            logger.info(f"Invalid OTP result: {invalid_otp_result}")
        except Exception as e:
            logger.info(f"Expected error for invalid OTP: {str(e)}")
        
        # Test 2: Invalid request type
        logger.info("Test 2: Testing invalid request type...")
        try:
            invalid_type_result = await UserService.reset_password(
                email=test_email,
                request_type="invalid_type",
                otp="123456",
                db=db
            )
            logger.info(f"Invalid type result: {invalid_type_result}")
        except Exception as e:
            logger.info(f"Expected error for invalid type: {str(e)}")
        
        # Test 3: Password update without OTP validation
        logger.info("Test 3: Testing password update without prior OTP validation...")
        try:
            # First clear any existing OTP
            await db.users.update_one(
                {"email_config.gmail_config.email": {"$regex": f"^{test_email}$", "$options": "i"}},
                {"$unset": {"reset_otp": "", "reset_otp_expires": ""}}
            )
            
            no_otp_result = await UserService.reset_password(
                email=test_email,
                request_type="validation",
                new_password="NewPassword123!",
                db=db
            )
            logger.info(f"No OTP validation result: {no_otp_result}")
        except Exception as e:
            logger.info(f"Expected error for no OTP validation: {str(e)}")
        
        # Close database connection
        client.close()
        
        return {"status": "Invalid scenarios tested"}
        
    except Exception as e:
        logger.error(f"Invalid scenarios test failed with error: {str(e)}")
        return {"error": str(e)}

async def test_schema_validation():
    """Test the schema validation for different request types"""
    logger.info("Testing schema validation...")
    
    from app.schemas.user_schema import ResetPasswordRequest
    from pydantic import ValidationError
    
    # Test valid OTP request
    try:
        otp_request = ResetPasswordRequest(
            email="<EMAIL>",
            type="otp",
            otp="123456"
        )
        logger.info(f"Valid OTP request: {otp_request}")
    except ValidationError as e:
        logger.error(f"OTP request validation failed: {e}")
    
    # Test valid validation request
    try:
        validation_request = ResetPasswordRequest(
            email="<EMAIL>",
            type="validation",
            new_password="NewPassword123!"
        )
        logger.info(f"Valid validation request: {validation_request}")
    except ValidationError as e:
        logger.error(f"Validation request validation failed: {e}")
    
    # Test invalid OTP request (missing OTP)
    try:
        invalid_otp_request = ResetPasswordRequest(
            email="<EMAIL>",
            type="otp"
            # Missing OTP
        )
        logger.error("This should have failed!")
    except ValidationError as e:
        logger.info(f"Expected validation error for missing OTP: {e}")
    
    # Test invalid validation request (missing password)
    try:
        invalid_validation_request = ResetPasswordRequest(
            email="<EMAIL>",
            type="validation"
            # Missing new_password
        )
        logger.error("This should have failed!")
    except ValidationError as e:
        logger.info(f"Expected validation error for missing password: {e}")

if __name__ == "__main__":
    print("Testing two-step password reset functionality...")
    
    # Test schema validation
    print("\n=== Testing Schema Validation ===")
    asyncio.run(test_schema_validation())
    
    # Test two-step flow
    print("\n=== Testing Two-Step Flow ===")
    flow_result = asyncio.run(test_two_step_reset_password())
    print(f"Two-step flow result: {json.dumps(flow_result, indent=2)}")
    
    # Test invalid scenarios
    print("\n=== Testing Invalid Scenarios ===")
    invalid_result = asyncio.run(test_invalid_scenarios())
    print(f"Invalid scenarios result: {json.dumps(invalid_result, indent=2)}")
