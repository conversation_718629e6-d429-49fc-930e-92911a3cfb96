import pytest
from unittest.mock import MagicMock, patch, ANY
from fastapi.testclient import Test<PERSON>lient
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.schemas.email_schema import EmailReply
from datetime import datetime

# Mock data
MOCK_USER_ID = "mock_user_id"
MOCK_TOKEN = "mock_token"
MOCK_IMAP_CONFIG = {
    "email": "<EMAIL>",
    "password": "encrypted_password",
    "imap_server": "imap.example.com",
    "smtp_server": "smtp.example.com",
    "smtp_port": 587
}
MOCK_GMAIL_CONFIG = {
    "email": "<EMAIL>",
    "access_token": "mock_access_token",
    "refresh_token": "mock_refresh_token",
    "token_expiry": "2024-04-28T00:00:00Z"
}
MOCK_EMAILS = [
    {
        "_id": "email1",
        "message_id": "msg1",
        "subject": "Test Email 1",
        "sender": "<EMAIL>",
        "recipient": "<EMAIL>",
        "date": datetime.utcnow().isoformat(),
        "body": "Test email body 1",
        "status": "unprocessed"
    },
    {
        "_id": "email2",
        "message_id": "msg2",
        "subject": "Test Email 2",
        "sender": "<EMAIL>",
        "recipient": "<EMAIL>",
        "date": datetime.utcnow().isoformat(),
        "body": "Test email body 2",
        "status": "processed"
    }
]

@pytest.fixture
def mock_db_imap():
    """Create a mock database with IMAP config"""
    db = MagicMock(spec=AsyncIOMotorDatabase)
    
    # Create mock collections first
    db.users = MagicMock()
    db.emails = MagicMock()
    db.drafts = MagicMock()
    
    # Then configure their methods
    db.users.find_one = MagicMock(return_value={
        "_id": MOCK_USER_ID,
        "email_config": {
            "provider": "imap",
            "imap_config": MOCK_IMAP_CONFIG
        }
    })
    
    # Setup email collection mocks
    db.emails.find = MagicMock()
    db.emails.find.return_value.to_list = MagicMock(return_value=MOCK_EMAILS)
    db.emails.count_documents = MagicMock(return_value=len(MOCK_EMAILS))
    db.emails.find_one = MagicMock(return_value=MOCK_EMAILS[0])
    
    return db

@pytest.fixture
def mock_db_gmail():
    """Create a mock database with Gmail config"""
    db = MagicMock(spec=AsyncIOMotorDatabase)
    
    # Create mock collections first
    db.users = MagicMock()
    db.emails = MagicMock()
    db.drafts = MagicMock()
    
    # Then configure their methods
    db.users.find_one = MagicMock(return_value={
        "_id": MOCK_USER_ID,
        "email_config": {
            "provider": "gmail",
            "gmail_config": MOCK_GMAIL_CONFIG
        }
    })
    
    # Setup email collection mocks
    db.emails.find = MagicMock()
    db.emails.find.return_value.to_list = MagicMock(return_value=MOCK_EMAILS)
    db.emails.count_documents = MagicMock(return_value=len(MOCK_EMAILS))
    db.emails.find_one = MagicMock(return_value=MOCK_EMAILS[0])
    
    return db

@pytest.fixture
def mock_token():
    """Create a mock authorization token"""
    return f"Bearer {MOCK_TOKEN}"

@pytest.mark.asyncio
async def test_fetch_emails_imap_success(mock_db_imap, mock_token):
    """Test successful email fetching with IMAP configuration"""
    with patch('app.utils.security.decode_jwt_token', return_value=MOCK_USER_ID):
        with patch('app.controllers.email_controller.EmailController.fetch_emails') as mock_fetch:
            from app.routes.email_routes import fetch_emails
            
            # Mock the controller response
            mock_fetch.return_value = {
                "emails": MOCK_EMAILS,
                "total": len(MOCK_EMAILS),
                "page": 1,
                "page_size": 10
            }
            
            response = await fetch_emails(
                page=1,
                page_size=10,
                refresh=True,  # Test refresh functionality
                status=None,
                category=None,
                sentiment=None,
                urgency=None,
                search=None,
                exclude=None,
                db=mock_db_imap,
                authorization=mock_token
            )
            
            assert "emails" in response
            assert len(response["emails"]) == len(MOCK_EMAILS)
            assert response["total"] == len(MOCK_EMAILS)
            assert response["page"] == 1
            mock_fetch.assert_called_once()

@pytest.mark.asyncio
async def test_fetch_emails_gmail_success(mock_db_gmail, mock_token):
    """Test successful email fetching with Gmail configuration"""
    with patch('app.utils.security.decode_jwt_token', return_value=MOCK_USER_ID):
        with patch('app.controllers.email_controller.EmailController.fetch_emails') as mock_fetch:
            from app.routes.email_routes import fetch_emails
            
            # Mock the controller response
            mock_fetch.return_value = {
                "emails": MOCK_EMAILS,
                "total": len(MOCK_EMAILS),
                "page": 1,
                "page_size": 10
            }
            
            response = await fetch_emails(
                page=1,
                page_size=10,
                refresh=True,  # Test refresh functionality
                status=None,
                category=None,
                sentiment=None,
                urgency=None,
                search=None,
                exclude=None,
                db=mock_db_gmail,
                authorization=mock_token
            )
            
            assert "emails" in response
            assert len(response["emails"]) == len(MOCK_EMAILS)
            assert response["total"] == len(MOCK_EMAILS)
            assert response["page"] == 1
            mock_fetch.assert_called_once()

@pytest.mark.asyncio
async def test_fetch_emails_with_status_filter(mock_db_imap, mock_token):
    """Test email fetching with status filter"""
    with patch('app.utils.security.decode_jwt_token', return_value=MOCK_USER_ID):
        with patch('app.controllers.email_controller.EmailController.fetch_emails') as mock_fetch:
            from app.routes.email_routes import fetch_emails
            
            # Filter mock emails for unprocessed status
            filtered_emails = [email for email in MOCK_EMAILS if email["status"] == "unprocessed"]
            
            # Mock the controller response
            mock_fetch.return_value = {
                "emails": filtered_emails,
                "total": len(filtered_emails),
                "page": 1,
                "page_size": 10
            }
            
            response = await fetch_emails(
                page=1,
                page_size=10,
                refresh=False,
                status="unprocessed",
                category=None,
                sentiment=None,
                urgency=None,
                search=None,
                exclude=None,
                db=mock_db_imap,
                authorization=mock_token
            )
            
            assert "emails" in response
            assert all(email["status"] == "unprocessed" for email in response["emails"])
            mock_fetch.assert_called_once()

@pytest.mark.asyncio
async def test_fetch_emails_unauthorized(mock_db_imap):
    """Test email fetching without authorization"""
    from app.routes.email_routes import fetch_emails
    from fastapi import HTTPException
    
    with pytest.raises(HTTPException) as exc_info:
        await fetch_emails(
            page=1,
            page_size=10,
            refresh=False,
            status=None,
            category=None,
            sentiment=None,
            urgency=None,
            search=None,
            exclude=None,
            db=mock_db_imap,
            authorization=None
        )
    assert exc_info.value.status_code == 401
    assert "Authorization header missing" in str(exc_info.value.detail)

@pytest.mark.asyncio
async def test_send_reply_imap_success(mock_db_imap):
    """Test successful email reply with IMAP configuration"""
    with patch('app.controllers.email_controller.EmailController.send_reply') as mock_send:
        from app.routes.email_routes import send_reply
        
        reply_data = EmailReply(
            email_id="email1",
            body="Test reply",
            is_draft=False,
            user_id=MOCK_USER_ID
        )
        
        mock_send.return_value = {"status": "success", "message": "Email sent successfully"}
        
        response = await send_reply(
            reply_data=reply_data,
            db=mock_db_imap
        )
        
        assert response["status"] == "success"
        assert "Email sent successfully" in response["message"]
        mock_send.assert_called_once()

@pytest.mark.asyncio
async def test_send_reply_gmail_success(mock_db_gmail):
    """Test successful email reply with Gmail configuration"""
    with patch('app.controllers.email_controller.EmailController.send_reply') as mock_send:
        from app.routes.email_routes import send_reply
        
        reply_data = EmailReply(
            email_id="email1",
            body="Test reply",
            is_draft=False,
            user_id=MOCK_USER_ID
        )
        
        mock_send.return_value = {"status": "success", "message": "Email sent successfully"}
        
        response = await send_reply(
            reply_data=reply_data,
            db=mock_db_gmail
        )
        
        assert response["status"] == "success"
        assert "Email sent successfully" in response["message"]
        mock_send.assert_called_once()

@pytest.mark.asyncio
async def test_send_reply_as_draft_imap(mock_db_imap):
    """Test saving email reply as draft with IMAP configuration"""
    with patch('app.controllers.email_controller.EmailController.send_reply') as mock_send:
        from app.routes.email_routes import send_reply
        
        reply_data = EmailReply(
            email_id="email1",
            body="Test draft",
            is_draft=True,
            user_id=MOCK_USER_ID
        )
        
        mock_send.return_value = {"status": "success", "message": "Draft saved successfully"}
        
        response = await send_reply(
            reply_data=reply_data,
            db=mock_db_imap
        )
        
        assert response["status"] == "success"
        assert "Draft saved successfully" in response["message"]
        mock_send.assert_called_once()

@pytest.mark.asyncio
async def test_send_reply_as_draft_gmail(mock_db_gmail):
    """Test saving email reply as draft with Gmail configuration"""
    with patch('app.controllers.email_controller.EmailController.send_reply') as mock_send:
        from app.routes.email_routes import send_reply
        
        reply_data = EmailReply(
            email_id="email1",
            body="Test draft",
            is_draft=True,
            user_id=MOCK_USER_ID
        )
        
        mock_send.return_value = {"status": "success", "message": "Email saved as draft in Gmail"}
        
        response = await send_reply(
            reply_data=reply_data,
            db=mock_db_gmail
        )
        
        assert response["status"] == "success"
        assert "Email saved as draft in Gmail" in response["message"]
        mock_send.assert_called_once()
