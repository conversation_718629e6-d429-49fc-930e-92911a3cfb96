import smtplib
import random
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# SMTP Configuration
SMTP_SERVER = "mail.comorin.co"  # Example: Gmail SMTP server
SMTP_PORT = 465
EMAIL_ADDRESS = "<EMAIL>"  # Replace with your email
EMAIL_PASSWORD = "6q#1QtN)6j3z"       # Replace with your app-specific password

# Single recipient email
recipient_email = "<EMAIL>"  # Replace with the actual recipient email

# 10 Email Templates
email_templates = [
    "Hello, this is Template 1. Thank you for choosing our service!",
    "Hi there! Template 2 here. Don’t miss out on our upcoming offers.",
    "Greetings! Template 3 is about our exciting new products.",
    "This is Template 4, bringing you the latest news and updates.",
    "Template 5: Reminder! Please check your account for details.",
    "Template 6: Your subscription has been successfully activated.",
    "Hello! Template 7 shares our gratitude for your support.",
    "Template 8: We have received your request and will process it soon.",
    "Hey! Template 9 highlights some exclusive deals just for you.",
    "Template 10: We value your feedback and would love to hear from you."
]

def send_email(to_email, subject, body):
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_ADDRESS
        msg['To'] = to_email
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'plain'))

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.set_debuglevel(1)  # Enable debugging
            server.starttls()
            server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            server.sendmail(EMAIL_ADDRESS, to_email, msg.as_string())
        
        print(f"Email sent successfully to {to_email}")
    except Exception as e:
        print(f"Failed to send email to {to_email}. Error: {e}")


# Main function to send 100 emails to one recipient
def main():
    for i in range(1):  # Send 100 emails
        template = random.choice(email_templates)  # Randomly select a template
        subject = f"Notification {i+1}"  # Email subject with a counter
        print(f"Sending email {i+1} to {recipient_email} with template: {template}")
        send_email(recipient_email, subject, template)

if __name__ == "__main__":
    main()
