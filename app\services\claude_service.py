import logging
import os
import re
from typing import Op<PERSON>, Dict, Any
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from anthropic import Anthropic
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
from app.config.constant import DEFAULT_EMAIL_RESPONDER

logger = logging.getLogger(__name__)

class ClaudeService:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        self.model = "claude-3-opus-20240229"  # Can be configured based on needs

    async def get_user_prompts(self, user_id: str) -> Dict[str, Dict[str, str]]:
        """Fetch user-specific prompts from configuration"""
        try:
            config = await self.db.configurations.find_one({"user_id": user_id})
            if not config or "email_responder" not in config:
                # Fall back to default prompts if user config not found
                return DEFAULT_EMAIL_RESPONDER["prompts"]
            
            return config["email_responder"].get("prompts", DEFAULT_EMAIL_RESPONDER["prompts"])
        except Exception as e:
            logger.error(f"Error fetching prompts: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch prompts")
    async def get_category_info(self, user_id: str) -> str:
        """Fetch user-specific category information from configuration"""
        try:
            config = await self.db.configurations.find_one({"user_id": user_id})
            if not config or "email_responder" not in config:
                raise HTTPException(status_code=404, detail="Category configuration not found")
            
            return config["email_responder"].get("categories", "")
        except Exception as e:
            logger.error(f"Error fetching category information: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch category information")

    async def analyze_text(self, text: str, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        Analyze text using Claude AI with given prompt
        
        Args:
            text: Text to analyze
            prompt: Specific prompt for the analysis
            system_prompt: Optional system prompt to guide Claude's behavior
        """
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=1000,
                temperature=0,
                system=system_prompt if system_prompt else "",
                messages=[{
                    "role": "user",
                    "content": f"{prompt}\n\nText to analyze: {text}"
                }]
            )
            # Extract text content from response
            if isinstance(response.content, list):
                return response.content[0].text
            return str(response.content)

        except Exception as e:
            logger.error(f"Error in Claude AI analysis: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to analyze text with Claude AI")

    async def classify_category(self, text: str, user_id: str) -> str:
        """
        Classify text into predefined categories
        
        Args:
            text: Email content to classify
            user_id: User ID for fetching prompts
            category_info: String containing available categories and their descriptions
        """
        print("classify 123456----->>>>")
        prompts = await self.get_user_prompts(user_id)
        category_info = await self.get_category_info(user_id)
        
        classification_prompts = prompts.get("classification", {})
        user_prompt = classification_prompts.get("user", "What is the classification of this text?")
        system_prompt = classification_prompts.get("system", "")
        
        # Build prompt with category information
        classification_prompt = f"""
        Available categories:
        {category_info}
        
        {user_prompt}
        
        If the email doesn't match any of the listed categories, respond with 'unknown'.
        """
        
        try:
            result = await self.analyze_text(text, classification_prompt, system_prompt)
            return result.strip().lower()  # Ensure clean string output
        except Exception as e:
            logger.error(f"Error in category classification: {str(e)}")
            return "unknown"

    async def detect_actions(self, text: str, user_id: str) -> dict:
        """
        Analyze email content to detect multiple predefined actions
        
        Returns:
            dict containing:
            - detected_actions: list of dicts, each containing:
                - action_name: str
                - confidence: float (0.0 to 1.0)
                - context: dict with relevant extracted information
            - overall_confidence: float (0.0 to 1.0)
        """
        try:
            prompts = await self.get_user_prompts(user_id)
            action_prompts = prompts.get("action_detection", {})
            
            # Get user's custom actions
            from app.config.constant import get_user_actions
            user_actions = await get_user_actions(self.db, user_id)
            # print("user_actions", user_actions)
            
            # Build prompt with user's actions and analyze descriptions for dependencies
            actions_context = []
            for action in user_actions.values():
                # Extract dependency information from description
                description = action['description']
                parameters = action.get('parameters', [])
                param_info = [
                    f"- {param['name']}: {param.get('description', 'No description')}"
                    for param in parameters
                ]
                
                actions_context.append(
                    f"Action: {action['name']}\n"
                    f"Description: {description}\n"
                    f"Parameters:\n" + "\n".join(param_info) + "\n"
                    f"Note: Review description carefully for dependencies on other actions\n"
                )
            
            actions_context = "\n".join(actions_context)
            print("actions_context", actions_context)
            
            system_prompt = """You are an expert at analyzing emails for multiple actions and their dependencies.
            Carefully review each action's description to identify dependencies between actions.
            Identify ALL possible actions mentioned in the email, including those that may be prerequisites for other actions.
            
            For each detected action, provide:
            1. The action name from the available actions list
            2. A confidence score (0.0 to 1.0) for how certain you are
            3. Relevant context extracted from the email for that action
            4. Dependencies identified from the action's description
            5. Whether this action is required by other actions
            
            Return your analysis in JSON format:
            {
                "detected_actions": [
                    {
                        "action_name": "action_id",
                        "confidence": 0.95,
                        "context": {"relevant": "information"},
                        "dependencies": {
                            "requires_actions": ["action_names"],
                            "required_by_actions": ["action_names"],
                            "dependency_reason": "Explanation from description"
                        }
                    }
                ],
                "overall_confidence": 0.90,
                "dependency_chain": ["action1", "action2"]
            }"""
            
            user_prompt = (
                f"Available Actions:\n{actions_context}\n\n"
                f"Analyze this email and identify ALL actions that apply. "
                f"Consider partial matches and implied actions.\n\n"
                f"{action_prompts.get('user', 'What actions are requested in this email?')}"
            )
            
            result = await self.analyze_text(text, user_prompt, system_prompt)
            print("result--->", result)
            
            # Parse JSON response with default structure
            import json
            try:
                # First try to extract JSON if it's embedded in other text
                json_match = re.search(r'\{[\s\S]*\}', result)
                if json_match:
                    result = json_match.group(0)
                
                # Clean up any potential formatting issues
                result = re.sub(r'(?m)^\s*#.*\n?', '', result)  # Remove comments
                result = re.sub(r'(?m)^\s*//.*\n?', '', result)  # Remove JS comments
                result = re.sub(r'```json\s*|\s*```', '', result)  # Remove code blocks
                
                action_data = json.loads(result)
                
                # Validate and fix structure if needed
                if not isinstance(action_data.get("detected_actions"), list):
                    raise ValueError("Invalid response structure - missing detected_actions list")
                
                # Ensure each action has required fields and dependency information
                for action in action_data["detected_actions"]:
                    # Add missing fields with defaults if necessary
                    if "confidence" not in action:
                        action["confidence"] = 0.5
                    if "context" not in action:
                        action["context"] = {"relevant": "No context provided"}
                    if "dependencies" not in action:
                        action["dependencies"] = {
                            "requires_actions": [],
                            "required_by_actions": [],
                            "dependency_reason": "No dependencies specified"
                        }
                    
                    # Ensure dependencies structure is complete
                    deps = action["dependencies"]
                    for key in ["requires_actions", "required_by_actions", "dependency_reason"]:
                        if key not in deps:
                            deps[key] = [] if key != "dependency_reason" else "No dependency reason provided"
                
                # Add overall confidence if missing
                if "overall_confidence" not in action_data:
                    confidences = [a.get("confidence", 0.5) for a in action_data["detected_actions"]]
                    action_data["overall_confidence"] = (
                        sum(confidences) / len(confidences) if confidences else 0.0
                    )
                
                # Ensure dependency_chain exists
                if "dependency_chain" not in action_data:
                    action_data["dependency_chain"] = [
                        action["action_name"] for action in action_data["detected_actions"]
                    ]
                
                logger.info(f"Successfully parsed action data: {json.dumps(action_data, indent=2)}")
                
            except Exception as e:
                logger.error(f"Error parsing action detection response: {str(e)}")
                logger.debug(f"Raw response: {result}")
                action_data = {
                    "detected_actions": [],
                    "overall_confidence": 0.0,
                    "dependency_chain": []
                }
            print("action_data", action_data)
            return action_data
            
        except Exception as e:
            logger.error(f"Error detecting actions: {str(e)}")
            return {
                "detected_action": None,
                "confidence": 0.0,
                # "required_info": {"present": [], "missing": []}
            }

    async def analyze_sentiment(self, text: str, user_id: str) -> dict:
        print("analyze----->>>>")
        """
        Analyze sentiment of text and return detailed sentiment analysis
        
        Returns:
            dict containing:
            - label: str (positive/negative/neutral)
            - score: float (-1.0 to 1.0)
            - confidence: float (0.0 to 1.0)
        """
        prompts = await self.get_user_prompts(user_id)
        sentiment_prompts = prompts.get("sentiment", {})
        user_prompt = sentiment_prompts.get("user", "What is the sentiment of this text?")
        system_prompt = sentiment_prompts.get("system", "")
        
        result = await self.analyze_text(text, user_prompt, system_prompt)
        
        try:
            label, score, confidence = result.strip().split("|")
            print("label, score, confidence", label, score, confidence)
            return {
                "label": label.strip().lower(),
                "score": float(score),
                "confidence": float(confidence)
            }
        except Exception as e:
            logger.error(f"Error parsing sentiment result: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to parse sentiment analysis result"
            )

    async def generate_summary(self, text: str, user_id: str) -> str:
        """Generate summary of text"""
        prompts = await self.get_user_prompts(user_id)
        summary_prompt = prompts.get("summary", "Summarize this text in one sentence.")
        
        system_prompt = """You are an expert in text summarization.
        Create a concise one-sentence summary of the text.
        Keep your response focused and brief."""
        
        return await self.analyze_text(text, summary_prompt, system_prompt)

    async def validate_missing_data(self, email_body: str, action_payload: dict, rag_context: dict, action_description: str = None, detected_actions: list = None) -> dict:
        """
        Validate and extract required data from email content based on action payload template
        
        Args:
            email_body: Content of the email
            action_payload: Template payload from the detected action
            rag_context: Context from RAG system
            action_description: Description of the current action being validated
            detected_actions: List of all detected actions in the email
            
        Returns:
            dict containing:
            - matched_data: Dict of matched key-value pairs
            - missing_fields: List of missing required fields
            - confidence_scores: Dict of confidence scores for matched data
            - dependencies: Dict of identified action dependencies
        """
        try:
            system_prompt = (
                "You are an expert at extracting structured data from email content.\n"
                "Analyze the email carefully to find values matching the required fields.\n"
                "Consider both explicit and implicit mentions of the required information.\n\n"
                f"Action Description: {action_description}\n\n"
                f"Other Detected Actions: {', '.join([action['action_name'] for action in (detected_actions or [])]) if detected_actions else 'None'}\n\n"
                "Consider dependencies between actions when extracting data. Some fields may depend on the completion or data from other actions.\n\n"
                "Return your analysis in JSON format:\n"
                "{\n"
                "    \"matched_data\": {field: extracted_value},\n"
                "    \"missing_fields\": [field_names],\n"
                "    \"confidence_scores\": {field: confidence_score},\n"
                "    \"dependencies\": {\n"
                "        \"requires_actions\": [action_names],\n"
                "        \"provides_data_for\": [action_names],\n"
                "        \"dependent_fields\": {field: required_action}\n"
                "    }\n"
                "}"
            )
            
            # Extract parameter names and descriptions from the parameters array
            required_fields = [(param["name"], param.get("description", "")) for param in action_payload]
            
            # Get other action names and descriptions
            other_actions_info = []
            if detected_actions:
                other_actions_info = [
                    f"Action: {action['action_name']}\nDescription: {action.get('description', 'No description')}"
                    for action in detected_actions
                ]
            
            # Prepare detailed prompt with all requirements
            user_prompt = (
                "Extract the following required fields from the email content:" + "\n" +
                [f"Field: {field[0]}\nDescription: {field[1]}" for field in required_fields] + "\n\n" +
                "Current Action Description:" + "\n" +
                (action_description or "No description provided") + "\n\n" +
                "Other Actions in Email:" + "\n" +
                ("\n".join(other_actions_info) if other_actions_info else "No other actions detected") + "\n\n" +
                "Additional context from RAG:" + "\n" +
                rag_context + "\n\n" +
                "Email content to analyze:" + "\n" +
                email_body + "\n\n" +
                "For each field:" + "\n" +
                "1. Find the most likely matching value" + "\n" +
                "2. Assign a confidence score (0.0 to 1.0)" + "\n" +
                "3. Mark as missing if no valid match found" + "\n" +
                "4. Identify if the field depends on other actions" + "\n" +
                "5. Note if this field's value is needed by other actions"

            )
            
            result = await self.analyze_text(email_body, user_prompt, system_prompt)
            print("validate ========================>>",result)
            
            # Parse JSON response
            import json
            try:
                # Extract the JSON part from the response
                json_str = result.split('{', 1)[1].rsplit('}', 1)[0]
                json_str = '{' + json_str + '}'
                
                # Clean up any explanatory text or formatting
                json_str = re.sub(r'(?m)^\s*//.*\n?', '', json_str)  # Remove comments
                json_str = re.sub(r'(?m)^\s*#.*\n?', '', json_str)   # Remove Python-style comments
                
                extracted_data = json.loads(json_str)
                
                # Validate the structure
                if not all(key in extracted_data for key in ['matched_data', 'missing_fields', 'confidence_scores']):
                    raise ValueError("Invalid response structure")
                print("extracted_data", extracted_data) 
                return extracted_data
                
            except (json.JSONDecodeError, ValueError, IndexError) as e:
                logger.error(f"Error parsing validation result: {str(e)}")
                # Extract parameter names for missing fields
                missing_fields = [param["name"] for param in action_payload]
                return {
                    "matched_data": {},
                    "missing_fields": missing_fields,
                    "confidence_scores": {},
                    "dependencies": {
                        "requires_actions": [],
                        "provides_data_for": [],
                        "dependent_fields": {}
                    }
                }
                
        except Exception as e:
            logger.error(f"Error validating missing data: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to validate and extract data from email"
            )
    async def generate_response(self, email_body: str, category: str, sentiment: Dict[str, Any], 
                              context: Dict[str, Any], user_id: str, feedback: Optional[str] = None,
                              previous_response: Optional[str] = None) -> str:
        """
        Generate or regenerate email response using Claude AI
        
        Args:
            email_body: Content of the email to respond to
            category: Classified category of the email
            sentiment: Sentiment analysis results
            context: Context including previous emails and category template
            user_id: User ID for fetching prompts
            feedback: Optional user feedback for response regeneration
        """
        try:
            # Get user's full name from database
            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
            user_full_name = f"{user.get('full_name', '')}".strip()
            
            prompts = await self.get_user_prompts(user_id)
            response_prompts = prompts.get("response_generation", {})
            user_prompt = response_prompts.get("user", "Generate a professional email response.")
            system_prompt = response_prompts.get("system", "") + f"\n\nBest regards,\n{user_full_name}"
            
            # Build comprehensive prompt with all available information
            missing_fields_info = ""
            print("context", context.get("action_payloads"),context.get("email_data",{}).get("action_validations", []))
            # Extract data from full context
            action_data = context.get("action", {})
            data_validation = context.get("email_data",{}).get("action_validations", [])
            has_missing_fields = context.get("has_missing_fields", False)
            
            # Check for action validations
            if data_validation:
                missing_fields = []
                for validation in data_validation:
                    action_name = validation["action_name"]
                    missing = validation["validation"]["missing_fields"]
                    confidence = validation.get("confidence", 0.0)
                    if missing:
                        # Get parameter descriptions from action_payloads
                        param_descriptions = {}
                        action_payload = context.get("action_payloads", {}).get(action_name, {})
                        if action_payload and "parameters" in action_payload:
                            param_descriptions = {
                                param["name"]: param.get("description", "")
                                for param in action_payload["parameters"]
                            }
                        
                        # Format missing fields with descriptions
                        missing_with_desc = [
                            f"{field} ({param_descriptions.get(field, 'No description')})"
                            for field in missing
                        ]
                        
                        missing_fields.append(
                            f"For {action_name} (confidence: {confidence:.1%}), "
                            f"we need:\n- " + "\n- ".join(missing_with_desc)
                        )
                
                if missing_fields:
                    missing_fields_info = (
                        "\n\nAdditional Information Required:\n" + 
                        "\n".join(missing_fields) +
                        "\n\nPlease provide the missing information to proceed with these actions."
                    )
            print("missing_fields_info",missing_fields_info)
            # Format action executions if available
            action_executions_info = ""
            if context.get("email_data",{}).get("action_executions"):
                action_results = []
                for action_name, execution in context.get("email_data",{}).get("action_executions").items():
                    if execution.get("status_code") == 200:
                        result = execution.get("response", {})
                        if isinstance(result, dict):
                            status = result.get("processed_data", {}).get("status", "completed")
                            action_results.append(f"Action '{action_name}' was executed successfully with status: {status}")
                if action_results:
                    action_executions_info = "\n\nExecuted Actions:\n" + "\n".join(action_results)
                
            print("action_executions_info",action_executions_info)

            detailed_prompt = (
                f"Task: {'Regenerate' if feedback else 'Generate'} a professional email response\n\n"
                f"Email Category: {category}\n"
                f"Sentiment Analysis: {sentiment}\n\n"
                f"Category Template: {context.get('category_template', 'No template available')}\n\n"
                f"Retrieved Context from Knowledge Base:\n"
                f"{context.get('rag_context', 'No context available')}\n\n"
                f"Original Email:\n"
                f"{email_body}\n\n"
                f"Previous Response:\n"
                f"{previous_response if feedback else 'No previous response'}\n\n"
                f"User Feedback for Improvement:\n"
                f"{feedback if feedback else 'No feedback provided'}\n"
                f"{missing_fields_info}\n\n"
                f"Action Execution Results:\n"
                f"{action_executions_info if action_executions_info else 'No actions were executed'}\n\n"
                f"Instructions:\n"
                f"1. Generate a professional response addressing the original email\n"
                f"2. If actions were executed successfully, include their results in the response\n"
                f"3. Maintain a polite and professional tone throughout\n"
                f"4. End with appropriate signature\n\n"
                f"{user_prompt}"
            )
            
            response = await self.analyze_text(email_body, detailed_prompt, system_prompt)
            
            # Clean up the response
            # Remove "Here is a draft response:" and similar prefixes
            response = re.sub(r'^(Here is|This is|Here\'s)(\sa|\sthe)?\s(draft\s)?(response|reply)[:\n\s]*', '', response.strip(), flags=re.IGNORECASE)
            
            # Remove any explanatory text after the signature
            response = re.sub(r'(How\'s this\?|Let me know|I aimed).*$', '', response.strip(), flags=re.MULTILINE|re.DOTALL)
            
            # If we have action execution info but it's not in the response, add it before the signature
            if action_executions_info and action_executions_info not in response:
                # Find position before signature or end of text
                signature_pos = response.find("Best regards,")
                if signature_pos == -1:
                    # No signature found, append to end
                    response = f"{response.strip()}\n\nAction Status:{action_executions_info}"
                else:
                    # Insert before signature
                    response = (
                        f"{response[:signature_pos].strip()}\n\n"
                        f"Action Status:{action_executions_info}\n\n"
                        f"{response[signature_pos:].strip()}"
                    )
            
            # Ensure the response has the signature
            if "Best regards," not in response:
                response = f"{response.strip()}\n\nBest regards,\n{user_full_name}"
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to generate email response"
            )
