from fastapi import APIRouter
from app.websocket.connection_manager import manager, socket_app

router = APIRouter(
    prefix="/api",
    tags=["Socket.IO"]
)

@router.post("/broadcast")
async def broadcast_message(message: str):
    """
    REST endpoint to broadcast message to all Socket.IO clients
    Can be tested via Postman REST API
    """
    await manager.broadcast('broadcast_message', {'message': message})
    return {"message": "Broadcast sent"}
