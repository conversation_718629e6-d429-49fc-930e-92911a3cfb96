import logging
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, status, Query, Header
from fastapi.responses import StreamingResponse, JSONResponse
from typing import Optional, List, Dict, Any
from boto3.s3.transfer import TransferConfig
from botocore.exceptions import ClientError
import uuid
from werkzeug.utils import secure_filename

from ..config.s3_config import S3Config
from ..schemas.attachment_schema import AttachmentResponse, AttachmentListResponse, MultipleAttachmentResponse
from ..models.user_model import UserInDB
from ..utils.security import decode_jwt_token

router = APIRouter()
s3_config = S3Config()
BUCKET_NAME = s3_config.bucket_name

def get_s3():
    return s3_config.client

def get_file_category(content_type: str, filename: str) -> str:
    """Determine file category based on content type and extension"""
    # Image types
    if content_type.startswith('image/') or filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
        return 'images'
    # PDF
    elif content_type == 'application/pdf' or filename.lower().endswith('.pdf'):
        return 'documents/pdf'
    # JSON
    elif content_type == 'application/json' or filename.lower().endswith('.json'):
        return 'documents/json'
    # Plain text
    elif content_type == 'text/plain' or filename.lower().endswith('.txt'):
        return 'documents/text'
    # Default category
    return 'others'

def validate_file(file: UploadFile):
    """Validate file type and size."""
    MAX_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_TYPES = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/json',
        'text/plain'  # Some JSON files might be detected as text/plain
    ]
    
    logger.info(f"Validating file: {file.filename}, Content-Type: {file.content_type}")
    
    # Check file type
    if file.content_type not in ALLOWED_TYPES:
        # Try to get file extension as fallback
        file_extension = file.filename.split('.')[-1].lower()
        if file_extension != 'json':
            raise HTTPException(
                status_code=400, 
                detail=(
                    f"File type '{file.content_type}' not allowed. "
                    f"Allowed types: {', '.join(ALLOWED_TYPES)}. "
                    "For JSON files, ensure they have a .json extension."
                )
            )
        # If we get here, it's a JSON file with a .json extension but wrong content-type
        logger.warning(f"JSON file detected with content-type {file.content_type}")
    
    # Check file size
    if hasattr(file, 'size') and file.size > MAX_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File size exceeds maximum allowed size of {MAX_SIZE/(1024*1024)}MB"
        )

async def get_user_id(
    authorization: Optional[str] = Header(None, alias="Authorization"),
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
) -> str:
    """
    Extract and validate user ID from either:
    1. JWT token in Authorization header (Bearer token)
    2. Direct user_id in X-User-Id header
    """
    if authorization and authorization.startswith("Bearer "):
        # Handle JWT token
        try:
            token = authorization.split(" ")[1]
            payload = decode_jwt_token(token)
            user_id = payload.get("user_id")
            if not user_id:
                raise HTTPException(status_code=401, detail="Invalid token: user_id not found")
            return str(user_id)
        except Exception as e:
            raise HTTPException(status_code=401, detail=f"Token validation failed: {str(e)}")
    
    elif x_user_id:
        # Handle direct user_id
        if not x_user_id.strip():
            raise HTTPException(status_code=401, detail="X-User-Id header cannot be empty")
        return x_user_id.strip()
    
    # No valid authentication provided
    raise HTTPException(
        status_code=401,
        detail="Authentication required. Provide either a valid JWT token (Authorization: Bearer <token>) or a user ID (X-User-Id: <user_id>)"
    )

logger = logging.getLogger(__name__)

# class EmailForm:
#     def __init__(self, email: str = Form(...)):
#         self.email = email

@router.post("/attachments/upload", response_model=MultipleAttachmentResponse)
async def upload_files(
    files: List[UploadFile] = File(...),
    # email_form: EmailForm = Depends(),
    user_id: str = Depends(get_user_id)
):
    logger.info(f"Starting file upload for user_id: {user_id}")
    uploaded_files = []
    failed_uploads = []
    
    # Configure multipart upload
    config = TransferConfig(
        multipart_threshold=1024 * 25,  # 25MB
        max_concurrency=10,
        multipart_chunksize=1024 * 25,  # 25MB
        use_threads=True
    )
    
    for file in files:
        try:
            logger.info(f"Processing file: {file.filename} (Content-Type: {file.content_type}, Size: {file.size if hasattr(file, 'size') else 'unknown'})")
            
            # Log file object details
            logger.debug(f"File object: {dir(file)}")
            
            validate_file(file)
            filename = secure_filename(file.filename)
            file_category = get_file_category(file.content_type, filename)
            # Sanitize email for use in path
            # sanitized_email = email_form.email.replace('@', '_at_').replace('.', '_dot_')
            s3_key = f"attachments/{user_id}/{file_category}/{uuid.uuid4()}/{filename}"
            logger.info(f"Generated S3 key: {s3_key}")
            
            # Upload to S3
            try:
                logger.info(f"Starting S3 upload for {filename} to {BUCKET_NAME}")
                
                # Read file content
                content = await file.read()
                if not content:
                    raise ValueError("Empty file")
                
                s3 = get_s3()
                
                s3.put_object(
                    Bucket=BUCKET_NAME,
                    Key=s3_key,
                    Body=content,
                    ContentType=file.content_type,
                    Metadata={
                        "filename": filename,
                        "user_id": user_id
                    }
                )
                logger.info(f"Successfully uploaded {filename} to S3")
            except Exception as upload_error:
                logger.error(f"S3 upload failed: {str(upload_error)}", exc_info=True)
                raise
            
            uploaded_files.append(
                AttachmentResponse(
                    s3_key=s3_key,
                    filename=filename,
                    content_type=file.content_type
                )
            )
            logger.info(f"Successfully processed {filename}")
            
        except Exception as e:
            error_msg = f"Error processing {file.filename}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            failed_uploads.append({
                "filename": file.filename,
                "error": str(e),
                "type": type(e).__name__
            })
            continue
    
    if not uploaded_files and failed_uploads:
        error_detail = {
            "message": "All file uploads failed",
            "failures": failed_uploads
        }
        logger.error(f"All uploads failed: {error_detail}")
        raise HTTPException(
            status_code=500,
            detail=error_detail
        )
    
    return MultipleAttachmentResponse(
        files=uploaded_files,
        failed_uploads=failed_uploads
    )

@router.get("/attachments/{s3_key:path}")
async def get_file(
    s3_key: str,
    download: bool = False,
    user_id: str = Depends(get_user_id),
    expires_in: int = Query(3600, description="URL expiration time in seconds", ge=60, le=86400)
):
    """
    Generate a pre-signed URL for accessing the file
    
    - **s3_key**: Full path to the file in S3
    - **download**: If True, forces download instead of browser preview
    - **expires_in**: URL expiration time in seconds (60-86400)
    """
    logger.info(f"Generating pre-signed URL for key: {s3_key}")
    logger.info(f"User ID: {user_id}")
    
    try:
        # Ensure the requested file belongs to the user
        if not s3_key.startswith(f"attachments/{user_id}/"):
            logger.warning(f"Access denied: User {user_id} tried to access {s3_key}")
            raise HTTPException(status_code=403, detail="Access denied")
        
        s3 = get_s3()
        
        # Generate the pre-signed URL with response headers if needed
        params = {
            'Bucket': BUCKET_NAME,
            'Key': s3_key,
        }
        
        # Add content disposition if download is requested
        if download:
            filename = s3_key.split('/')[-1]
            params['ResponseContentDisposition'] = f'attachment; filename="{filename}"'
        
        # Generate pre-signed URL
        url = s3.generate_presigned_url(
            'get_object',
            Params=params,
            ExpiresIn=expires_in,
            HttpMethod='GET'
        )
        
        # Get file metadata for the response
        head = s3.head_object(Bucket=BUCKET_NAME, Key=s3_key)
        return {
            "url": url,
            "expires_in": expires_in,
            "metadata": {
                "filename": s3_key.split("/")[-1],
                "content_type": head.get('ContentType', 'application/octet-stream'),
                "size": head.get('ContentLength', 0),
                "last_modified": head.get('LastModified', '').isoformat() if head.get('LastModified') else None
            }
        }
        
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="File not found")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachments", response_model=AttachmentListResponse)
async def list_attachments(
    user_id: str = Depends(get_user_id),
    limit: int = 10,
    continuation_token: Optional[str] = None
):
    try:
        params = {
            'Bucket': BUCKET_NAME,
            'Prefix': f"attachments/{user_id}/",
            'MaxKeys': limit
        }
        
        if continuation_token:
            params['ContinuationToken'] = continuation_token
            
        s3 = get_s3()
        response = s3.list_objects_v2(**params)
        
        files = [
            AttachmentResponse(
                s3_key=obj['Key'],
                size=obj['Size'],
                last_modified=obj['LastModified'],
                filename=obj['Key'].split('/')[-1]
            ) 
            for obj in response.get('Contents', [])
        ]
        
        return AttachmentListResponse(
            files=files,
            continuation_token=response.get('NextContinuationToken')
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/attachments/{s3_key}")
async def delete_file(
    s3_key: str,
    user_id: str = Depends(get_user_id)
):
    try:
        # Security check
        if not s3_key.startswith(f"attachments/{user_id}/"):
            raise HTTPException(status_code=403, detail="Access denied")
            
        s3 = get_s3()
        s3.delete_object(
            Bucket=BUCKET_NAME,
            Key=s3_key
        )
        
        return JSONResponse(content={"message": "File deleted successfully"})
        
    except ClientError as e:
        raise HTTPException(status_code=500, detail=str(e))